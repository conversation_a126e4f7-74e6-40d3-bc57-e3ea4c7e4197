<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vigilant Eye - Dashboard</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #2d3748;
        }

        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .logo-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .header-text h1 {
            font-size: 28px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .header-text p {
            color: #718096;
            font-size: 16px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-avatar {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            width: 45px;
            height: 45px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: 600;
        }

        .user-details h3 {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
        }

        .user-role {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .user-role.admin {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
        }

        .logout-btn {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .logout-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(245, 101, 101, 0.3);
        }

        .services-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .section-title {
            font-size: 24px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 30px;
            text-align: center;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
        }

        .service-card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
            border: 2px solid #e2e8f0;
            position: relative;
            overflow: hidden;
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            border-color: #667eea;
        }

        .service-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .service-card h3 {
            font-size: 20px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 10px;
        }

        .service-card p {
            color: #718096;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .service-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .service-card:hover .service-btn {
            transform: translateX(5px);
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                text-align: center;
            }

            .services-grid {
                grid-template-columns: 1fr;
            }

            .dashboard-container {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="header-left">
                    <div class="logo-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <div class="header-text">
                        <h1>Vigilant Eye Dashboard</h1>
                        <p>AI-Powered Detection & Monitoring System</p>
                    </div>
                </div>
                
                <div class="user-info">
                    <div class="user-avatar">
                        {{ user.full_name[0].upper() if user.full_name else user.username[0].upper() }}
                    </div>
                    <div class="user-details">
                        <h3>{{ user.full_name or user.username }}</h3>
                        <span class="user-role {{ user.role }}">{{ user.role }}</span>
                    </div>
                    <a href="/logout" class="logout-btn" onclick="return confirm('Are you sure you want to logout?')">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </div>
            </div>
        </header>

        <!-- Services Section -->
        <section class="services-section">
            <h2 class="section-title">AI Detection Services</h2>
            
            <div class="services-grid">
                <a href="/crowd_detection/crowd_detection" class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>Crowd Detection</h3>
                    <p>Real-time crowd monitoring and density analysis with intelligent region-of-interest detection.</p>
                    <span class="service-btn">Launch Module</span>
                </a>

                <a href="/helmet_detection/helmet_detection" class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-hard-hat"></i>
                    </div>
                    <h3>PPE Detection</h3>
                    <p>Ensure proper personal protective equipment compliance with automated safety monitoring.</p>
                    <span class="service-btn">Launch Module</span>
                </a>

                <a href="/quality_control/quality_control" class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3>Quality Control</h3>
                    <p>Automated quality assurance and defect detection for manufacturing processes.</p>
                    <span class="service-btn">Launch Module</span>
                </a>

                <a href="/face_recognition/face_recognition" class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <h3>Face Detection</h3>
                    <p>Advanced facial recognition and identification system for security applications.</p>
                    <span class="service-btn">Launch Module</span>
                </a>
            </div>
        </section>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; 2024 Vigilant Eye. All rights reserved. | Powered by AI Detection Technology</p>
        </footer>
    </div>
</body>
</html>
