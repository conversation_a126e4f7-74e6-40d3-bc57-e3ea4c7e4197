Write-Host "================================================" -ForegroundColor Green
Write-Host "Starting Vigilant Eye Services" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green

Write-Host ""
Write-Host "Starting Authentication Service..." -ForegroundColor Yellow
Write-Host "================================================" -ForegroundColor Yellow

# Start Authentication Service in new window
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd authentication; python start.py"

Write-Host ""
Write-Host "Waiting 5 seconds for auth service to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

Write-Host ""
Write-Host "Starting Backend Service..." -ForegroundColor Yellow
Write-Host "================================================" -ForegroundColor Yellow

# Start Backend Service in new window
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd Backend; venv\scripts\activate; uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"

Write-Host ""
Write-Host "================================================" -ForegroundColor Green
Write-Host "Services are starting..." -ForegroundColor Green
Write-Host ""
Write-Host "Authentication Service: http://localhost:8001" -ForegroundColor Cyan
Write-Host "Backend Service: http://localhost:8000" -ForegroundColor Cyan
Write-Host ""
Write-Host "Login credentials:" -ForegroundColor White
Write-Host "Username: admin" -ForegroundColor White
Write-Host "Password: admin123" -ForegroundColor White
Write-Host "================================================" -ForegroundColor Green
Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
