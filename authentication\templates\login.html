<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vigilant Eye - Authentication</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --text-primary: #333;
            --text-secondary: #666;
            --success-color: #10b981;
            --error-color: #ef4444;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f9f9fb;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 40px;
            background: #fff;
            border-bottom: 1px solid #e5e5e5;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            animation: slideDown 0.8s ease-out;
        }

        @keyframes slideDown {
            from { transform: translateY(-100%); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .auth-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: calc(100vh - 90px);
            padding: 20px;
        }

        .auth-box {
            width: 100%;
            max-width: 480px;
            background: #fff;
            border: 1px solid #e5e5e5;
            border-radius: 24px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            margin: 20px auto;
        }

        .logo-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            padding: 30px 30px 20px;
        }

        .logo {
            height: 50px;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
        }

        .logo-fallback {
            height: 50px;
            width: 50px;
            background: var(--primary-gradient);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .company-name {
            font-size: 28px;
            font-weight: 800;
            color: #333;
            letter-spacing: -0.5px;
        }

        .form-section {
            padding: 0 30px 30px;
        }

        .form-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 30px;
            text-align: center;
            animation: fadeInUp 0.6s ease-out 0.3s forwards;
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .input-group {
            margin-bottom: 20px;
            position: relative;
            animation: fadeInUp 0.6s ease-out forwards;
            animation-delay: calc(var(--delay, 0) * 0.1s);
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 14px;
        }

        .input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .input-group input {
            width: 100%;
            padding: 14px 18px;
            background: white;
            border: 2px solid #e5e5e5;
            border-radius: 12px;
            font-size: 15px;
            color: var(--text-primary);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .input-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .input-icon {
            position: absolute;
            right: 18px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 4px;
        }

        .input-icon:hover {
            color: var(--text-primary);
            transform: scale(1.1);
        }

        .remember-me {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }

        .remember-me input[type="checkbox"] {
            width: 18px;
            height: 18px;
            margin-right: 10px;
            accent-color: #667eea;
            cursor: pointer;
        }

        .remember-me label {
            color: var(--text-secondary);
            font-size: 14px;
            cursor: pointer;
            margin-bottom: 0;
        }

        .submit-button {
            width: 100%;
            padding: 15px;
            background: var(--primary-gradient);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 15px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .submit-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .submit-button:hover::before {
            left: 100%;
        }

        .submit-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 30px rgba(102, 126, 234, 0.4);
        }

        .submit-button.loading {
            pointer-events: none;
            opacity: 0.8;
        }

        .submit-button.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .message {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            font-weight: 500;
            text-align: center;
        }

        .error-message {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .success-message {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        @media (max-width: 768px) {
            .auth-box {
                margin: 10px;
                border-radius: 20px;
                max-width: 100%;
            }

            .form-section {
                padding: 0 20px 30px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo-container">
            <img src="/static/ecogo.png" alt="VigilanteEye Logo" class="logo" id="companyLogo" onerror="showLogoFallback()">
            <div class="logo-fallback" id="logoFallback" style="display: none;">
                <i class="fas fa-shield-alt"></i>
            </div>
            <span class="company-name">Vigilant Eye</span>
        </div>
    </div>

    <div class="auth-container">
        <div class="auth-box">
            <div class="logo-container">
                <div class="logo-fallback">
                    <i class="fas fa-eye"></i>
                </div>
            </div>

            <div class="form-section">
                <h2 class="form-title">Welcome Back</h2>

                <div id="loginMessages">
                    {% if error %}
                    <div class="message error-message">
                        <i class="fas fa-exclamation-triangle"></i> {{ error }}
                    </div>
                    {% endif %}
                </div>

                <form id="loginForm" method="post">
                    <div class="input-group" style="--delay: 1">
                        <label for="username">Username</label>
                        <div class="input-wrapper">
                            <input type="text" id="username" name="username" required>
                            <i class="fas fa-user input-icon"></i>
                        </div>
                    </div>

                    <div class="input-group" style="--delay: 2">
                        <label for="password">Password</label>
                        <div class="input-wrapper">
                            <input type="password" id="password" name="password" required>
                            <i class="fas fa-eye input-icon" onclick="togglePassword('password', this)"></i>
                        </div>
                    </div>

                    <div class="remember-me" style="--delay: 3">
                        <input type="checkbox" id="remember" name="remember">
                        <label for="remember">Remember me for 30 days</label>
                    </div>

                    <button type="submit" class="submit-button" style="--delay: 4">
                        <i class="fas fa-sign-in-alt"></i> Sign In
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script>
        function showLogoFallback() {
            const logoImg = document.getElementById('companyLogo');
            const logoFallback = document.getElementById('logoFallback');
            if (logoImg && logoFallback) {
                logoImg.style.display = 'none';
                logoFallback.style.display = 'flex';
            }
        }

        function togglePassword(inputId, icon) {
            const input = document.getElementById(inputId);
            const isPassword = input.type === 'password';
            input.type = isPassword ? 'text' : 'password';
            icon.classList.toggle('fa-eye');
            icon.classList.toggle('fa-eye-slash');
        }

        function setLoading(button, loading) {
            if (loading) {
                button.classList.add('loading');
                button.disabled = true;
            } else {
                button.classList.remove('loading');
                button.disabled = false;
            }
        }

        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const submitButton = this.querySelector('.submit-button');
            setLoading(submitButton, true);
        });

        window.addEventListener('load', function() {
            const authContainer = document.querySelector('.auth-container');
            authContainer.style.animationDelay = '0.2s';
        });
    </script>
</body>
</html>
