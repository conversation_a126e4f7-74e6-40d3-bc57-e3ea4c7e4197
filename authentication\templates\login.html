<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vigilant Eye - Authentication</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --text-primary: #333;
            --text-secondary: #666;
            --success-color: #10b981;
            --error-color: #ef4444;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f9f9fb;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 40px;
            background: #fff;
            border-bottom: 1px solid #e5e5e5;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            animation: slideDown 0.8s ease-out;
        }

        @keyframes slideDown {
            from { transform: translateY(-100%); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .auth-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: calc(100vh - 90px);
            padding: 20px;
        }

        .auth-box {
            width: 100%;
            max-width: 480px;
            background: #fff;
            border: 1px solid #e5e5e5;
            border-radius: 24px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            margin: 20px auto;
        }

        .logo-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            padding: 30px 30px 20px;
        }

        .logo {
            height: 50px;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
        }

        .logo-fallback {
            height: 50px;
            width: 50px;
            background: var(--primary-gradient);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .company-name {
            font-size: 28px;
            font-weight: 800;
            color: #333;
            letter-spacing: -0.5px;
        }

        /* Tab navigation */
        .tab-navigation {
            display: flex;
            position: relative;
            background: rgba(0, 0, 0, 0.05);
            border-radius: 12px;
            padding: 4px;
            margin: 20px 30px 20px;
        }

        .tab-button {
            flex: 1;
            padding: 12px 20px;
            background: none;
            border: none;
            font-size: 14px;
            font-weight: 600;
            color: var(--text-secondary);
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
        }

        .tab-button.active {
            color: var(--text-primary);
        }

        .tab-indicator {
            position: absolute;
            top: 4px;
            left: 4px;
            width: calc(50% - 4px);
            height: calc(100% - 8px);
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            z-index: 1;
        }

        .tab-indicator.register {
            transform: translateX(100%);
        }

        /* Form container */
        .form-container {
            position: relative;
            height: auto;
            overflow: hidden;
        }

        .form-wrapper {
            display: flex;
            width: 200%;
            transition: transform 0.5s ease;
        }

        .form-wrapper.show-register {
            transform: translateX(-50%);
        }

        .form-section {
            width: 50%;
            padding: 0 30px 30px;
        }

        .form-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 30px;
            text-align: center;
            animation: fadeInUp 0.6s ease-out 0.3s forwards;
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .input-group {
            margin-bottom: 20px;
            position: relative;
            animation: fadeInUp 0.6s ease-out forwards;
            animation-delay: calc(var(--delay, 0) * 0.1s);
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 14px;
        }

        .input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .input-group input {
            width: 100%;
            padding: 14px 18px;
            background: white;
            border: 2px solid #e5e5e5;
            border-radius: 12px;
            font-size: 15px;
            color: var(--text-primary);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .input-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .input-icon {
            position: absolute;
            right: 18px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 4px;
        }

        .input-icon:hover {
            color: var(--text-primary);
            transform: scale(1.1);
        }

        /* Form row for side-by-side inputs */
        .form-row {
            display: flex;
            gap: 15px;
        }

        .form-row .input-group {
            flex: 1;
        }

        .remember-me {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }

        .remember-me input[type="checkbox"] {
            width: 18px;
            height: 18px;
            margin-right: 10px;
            accent-color: #667eea;
            cursor: pointer;
        }

        .remember-me label {
            color: var(--text-secondary);
            font-size: 14px;
            cursor: pointer;
            margin-bottom: 0;
        }

        .submit-button {
            width: 100%;
            padding: 15px;
            background: var(--primary-gradient);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 15px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .submit-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .submit-button:hover::before {
            left: 100%;
        }

        .submit-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 30px rgba(102, 126, 234, 0.4);
        }

        .submit-button.loading {
            pointer-events: none;
            opacity: 0.8;
        }

        .submit-button.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .message {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            font-weight: 500;
            text-align: center;
        }

        .error-message {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .success-message {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        @media (max-width: 768px) {
            .auth-box {
                margin: 10px;
                border-radius: 20px;
                max-width: 100%;
            }

            .form-section {
                padding: 0 20px 30px;
            }

            .tab-navigation {
                margin: 0 20px 20px;
            }

            .form-row {
                flex-direction: column;
                gap: 0;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo-container">
            <img src="/static/ecogo.png" alt="VigilanteEye Logo" class="logo" id="companyLogo" onerror="showLogoFallback()">
            <div class="logo-fallback" id="logoFallback" style="display: none;">
                <i class="fas fa-shield-alt"></i>
            </div>
            <span class="company-name">Vigilant Eye</span>
        </div>
    </div>

    <div class="auth-container">
        <div class="auth-box">
            <div class="logo-container">
                <div class="logo-fallback">
                    <i class="fas fa-eye"></i>
                </div>
            </div>

            <!-- Tab navigation -->
            <div class="tab-navigation">
                <div class="tab-indicator" id="tabIndicator"></div>
                <button class="tab-button active" id="loginTab" onclick="switchTab('login')">
                    Login
                </button>
                <button class="tab-button" id="registerTab" onclick="switchTab('register')">
                    Register
                </button>
            </div>

            <!-- Forms container -->
            <div class="form-container">
                <div class="form-wrapper" id="formWrapper">
                    <!-- Login Form -->
                    <div class="form-section" id="loginSection">
                        <h2 class="form-title">Welcome Back</h2>

                        <div id="loginMessages">
                            {% if error %}
                            <div class="message error-message">
                                <i class="fas fa-exclamation-triangle"></i> {{ error }}
                            </div>
                            {% endif %}
                            {% if success %}
                            <div class="message success-message">
                                <i class="fas fa-check-circle"></i> {{ success }}
                            </div>
                            {% endif %}
                        </div>

                        <form id="loginForm" method="post" action="/login">
                            <div class="input-group" style="--delay: 1">
                                <label for="username">Username</label>
                                <div class="input-wrapper">
                                    <input type="text" id="username" name="username" required>
                                    <i class="fas fa-user input-icon"></i>
                                </div>
                            </div>

                            <div class="input-group" style="--delay: 2">
                                <label for="password">Password</label>
                                <div class="input-wrapper">
                                    <input type="password" id="password" name="password" required>
                                    <i class="fas fa-eye input-icon" onclick="togglePassword('password', this)"></i>
                                </div>
                            </div>

                            <div class="remember-me" style="--delay: 3">
                                <input type="checkbox" id="remember" name="remember">
                                <label for="remember">Remember me for 30 days</label>
                            </div>

                            <button type="submit" class="submit-button" style="--delay: 4">
                                <i class="fas fa-sign-in-alt"></i> Sign In
                            </button>
                        </form>
                    </div>

                    <!-- Register Form -->
                    <div class="form-section" id="registerSection">
                        <h2 class="form-title">Create Account</h2>

                        <div id="registerMessages"></div>

                        <form id="registerForm" method="post" action="/register">
                            <div class="form-row">
                                <div class="input-group" style="--delay: 1">
                                    <label for="regUsername">Username</label>
                                    <div class="input-wrapper">
                                        <input type="text" id="regUsername" name="username" required>
                                        <i class="fas fa-user input-icon"></i>
                                    </div>
                                </div>

                                <div class="input-group" style="--delay: 2">
                                    <label for="regEmail">Email</label>
                                    <div class="input-wrapper">
                                        <input type="email" id="regEmail" name="email" required>
                                        <i class="fas fa-envelope input-icon"></i>
                                    </div>
                                </div>
                            </div>

                            <div class="input-group" style="--delay: 3">
                                <label for="regFullName">Full Name</label>
                                <div class="input-wrapper">
                                    <input type="text" id="regFullName" name="full_name">
                                    <i class="fas fa-id-card input-icon"></i>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="input-group" style="--delay: 4">
                                    <label for="regPassword">Password</label>
                                    <div class="input-wrapper">
                                        <input type="password" id="regPassword" name="password" required>
                                        <i class="fas fa-eye input-icon" onclick="togglePassword('regPassword', this)"></i>
                                    </div>
                                </div>

                                <div class="input-group" style="--delay: 5">
                                    <label for="regConfirmPassword">Confirm Password</label>
                                    <div class="input-wrapper">
                                        <input type="password" id="regConfirmPassword" name="confirm_password" required>
                                        <i class="fas fa-eye input-icon" onclick="togglePassword('regConfirmPassword', this)"></i>
                                    </div>
                                </div>
                            </div>

                            <button type="submit" class="submit-button" style="--delay: 6">
                                <i class="fas fa-user-plus"></i> Create Account
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Logo fallback function
        function showLogoFallback() {
            const logoImg = document.getElementById('companyLogo');
            const logoFallback = document.getElementById('logoFallback');
            if (logoImg && logoFallback) {
                logoImg.style.display = 'none';
                logoFallback.style.display = 'flex';
            }
        }

        // Tab switching functionality
        function switchTab(tab) {
            const loginTab = document.getElementById('loginTab');
            const registerTab = document.getElementById('registerTab');
            const tabIndicator = document.getElementById('tabIndicator');
            const formWrapper = document.getElementById('formWrapper');

            if (tab === 'login') {
                loginTab.classList.add('active');
                registerTab.classList.remove('active');
                tabIndicator.classList.remove('register');
                formWrapper.classList.remove('show-register');
            } else {
                registerTab.classList.add('active');
                loginTab.classList.remove('active');
                tabIndicator.classList.add('register');
                formWrapper.classList.add('show-register');
            }
        }

        // Password visibility toggle
        function togglePassword(inputId, icon) {
            const input = document.getElementById(inputId);
            const isPassword = input.type === 'password';
            input.type = isPassword ? 'text' : 'password';
            icon.classList.toggle('fa-eye');
            icon.classList.toggle('fa-eye-slash');
        }

        // Show/hide loading state
        function setLoading(button, loading) {
            if (loading) {
                button.classList.add('loading');
                button.disabled = true;
            } else {
                button.classList.remove('loading');
                button.disabled = false;
            }
        }

        // Form submission handlers
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const submitButton = this.querySelector('.submit-button');
            setLoading(submitButton, true);
        });

        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const password = document.getElementById('regPassword').value;
            const confirmPassword = document.getElementById('regConfirmPassword').value;

            if (password !== confirmPassword) {
                e.preventDefault();
                showMessage('registerMessages', 'Passwords do not match!', 'error');
                return;
            }

            const submitButton = this.querySelector('.submit-button');
            setLoading(submitButton, true);
        });

        // Show message function
        function showMessage(containerId, message, type) {
            const container = document.getElementById(containerId);
            const messageClass = type === 'error' ? 'error-message' : 'success-message';
            const icon = type === 'error' ? 'fa-exclamation-triangle' : 'fa-check-circle';

            container.innerHTML = `
                <div class="message ${messageClass}">
                    <i class="fas ${icon}"></i> ${message}
                </div>
            `;
        }

        // Add entrance animations
        window.addEventListener('load', function() {
            const authContainer = document.querySelector('.auth-container');
            authContainer.style.animationDelay = '0.2s';
        });
    </script>
</body>
</html>
