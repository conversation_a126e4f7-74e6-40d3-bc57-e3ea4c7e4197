@echo off
echo ============================================================
echo 🚀 Starting Vigilant Eye Backend Services
echo ============================================================

echo.
echo 🔧 Activating virtual environment...
call venv\scripts\activate.bat

echo.
echo 🚀 Starting Backend on http://127.0.0.1:8000
echo ============================================================
echo.
echo 📋 Available Services:
echo    Main Dashboard: http://127.0.0.1:8000/
echo    Face Recognition: http://127.0.0.1:8000/face_recognition/
echo    PPE Detection: http://127.0.0.1:8000/helmet_detection/
echo    Crowd Detection: http://127.0.0.1:8000/crowd_detection/
echo    Quality Control: http://127.0.0.1:8000/quality_control/
echo    Health Check: http://127.0.0.1:8000/health
echo.
echo ============================================================

uvicorn app.main:app --reload --host 127.0.0.1 --port 8000
