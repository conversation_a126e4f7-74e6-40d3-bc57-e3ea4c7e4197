# Vigilant Eye Authentication Service

A standalone authentication microservice for the Vigilant Eye system.

## Overview

This authentication service provides centralized user authentication and authorization for all Vigilant Eye services. It's designed as a microservice that can be deployed independently and used by multiple services.

## Features

- **JWT Token Authentication**: Secure token-based authentication
- **User Management**: Create, read, update user accounts
- **Password Hashing**: Secure password storage using bcrypt
- **Token Validation**: API endpoint for other services to validate tokens
- **Session Management**: Support for both short-term and long-term sessions
- **Database Integration**: MySQL database for user storage
- **CORS Support**: Cross-origin resource sharing for web applications

## Quick Start

### 1. Install Dependencies

```bash
cd authentication
pip install -r requirements.txt
```

### 2. Configure Database

Update the database URL in `config.py` or set environment variable:

```bash
export DATABASE_URL="mysql+pymysql://username:password@localhost/vigilanteye_auth"
```

### 3. Start the Service

```bash
python start.py
```

The service will start on `http://localhost:8001`

### 4. Default Admin User

A default admin user is created automatically:
- **Username**: admin
- **Password**: admin123
- **Please change this password after first login!**

## API Endpoints

### Authentication Endpoints

- `GET /login` - Login page
- `POST /login` - Login form submission
- `POST /token` - Get access token (API)
- `POST /logout` - Logout
- `GET /me` - Get current user info

### Token Validation (for other services)

- `POST /validate-token` - Validate token and get user info

### User Management

- `GET /users/{username}` - Get user by username
- `POST /users` - Create new user
- `POST /register` - User registration

### Health Check

- `GET /health` - Service health check

## Configuration

### Environment Variables

- `AUTH_SERVICE_HOST` - Host to bind to (default: localhost)
- `AUTH_SERVICE_PORT` - Port to bind to (default: 8001)
- `DATABASE_URL` - Database connection string
- `SECRET_KEY` - JWT secret key
- `ACCESS_TOKEN_EXPIRE_MINUTES` - Token expiration time

### Database Setup

Create a MySQL database for the authentication service:

```sql
CREATE DATABASE vigilanteye_auth;
```

The service will automatically create the required tables.

## Integration with Other Services

### Using the Auth Client

Other services can use the `auth_client.py` to integrate with this authentication service:

```python
from authentication.auth_client import auth_client, require_auth, get_current_user

# In your FastAPI routes
@app.get("/protected-route")
async def protected_route(current_user: dict = Depends(get_current_user)):
    return {"message": f"Hello {current_user['username']}"}

# Or using decorator
@require_auth
async def another_protected_route(request: Request, current_user: dict):
    return {"user": current_user}
```

### Token Validation

Other services can validate tokens by making a POST request to `/validate-token`:

```python
import requests

response = requests.post(
    "http://localhost:8001/validate-token",
    json={"token": "your-jwt-token"}
)

if response.json().get("valid"):
    user = response.json().get("user")
    # User is authenticated
```

## Security Considerations

1. **Change Default Credentials**: Always change the default admin password
2. **Secret Key**: Use a strong, unique secret key in production
3. **HTTPS**: Use HTTPS in production environments
4. **Database Security**: Secure your database connection
5. **CORS**: Configure CORS properly for your domains

## Development

### Running in Development Mode

```bash
python start.py
```

This will start the service with auto-reload enabled.

### Testing

You can test the authentication service using curl:

```bash
# Login
curl -X POST "http://localhost:8001/token" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "username=admin&password=admin123"

# Validate token
curl -X POST "http://localhost:8001/validate-token" \
     -H "Content-Type: application/json" \
     -d '{"token": "your-jwt-token"}'
```

## Deployment

### Docker Deployment

Create a `Dockerfile`:

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8001
CMD ["python", "start.py"]
```

### Production Configuration

1. Set strong secret keys
2. Use environment variables for configuration
3. Set up proper database with backups
4. Configure reverse proxy (nginx)
5. Set up SSL certificates
6. Monitor logs and health

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │  Other Services │
│   (Browser)     │    │   Service       │    │                 │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                                 ▼
                    ┌─────────────────┐
                    │ Authentication  │
                    │    Service      │
                    │  (Port 8001)    │
                    └─────────┬───────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │   MySQL         │
                    │   Database      │
                    └─────────────────┘
```

## Support

For issues and questions, please check the main Vigilant Eye documentation or create an issue in the project repository.
