from datetime import datetime
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from app.database import get_db
from app.auth import (
    authenticate_user, create_access_token, get_user, get_user_by_email, 
    create_user, decode_access_token, get_current_user, get_current_active_user
)
from app.schemas import LoginRequest, TokenVerificationRequest, UserCreate, Token
from app.models import User, Role
from app.utils import has_permission

router = APIRouter()

@router.post("/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    """OAuth2 compatible token login"""
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token = create_access_token(data={"sub": user.username})
    return {"access_token": access_token, "token_type": "bearer"}

@router.post("/token/refresh")
async def refresh_token(token: str = Depends(), db: Session = Depends(get_db)):
    """Refresh access token"""
    login_user = get_user(db, username=decode_access_token(token))
    if not login_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token = create_access_token(data={"sub": login_user.username})
    return {"access_token": access_token, "token_type": "bearer"}

@router.post("/login")
async def login(form_data: LoginRequest, db: Session = Depends(get_db)):
    """Login endpoint"""
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token = create_access_token(data={"sub": user.username})
    
    # Check if user has admin role
    is_admin = has_permission(user, "admin_access") or any(role.name == "admin" for role in user.roles)
    
    return {
        "access_token": access_token, 
        "token_type": "bearer",
        "user_role": "admin" if is_admin else "viewer"
    }

@router.post("/register/user", status_code=status.HTTP_201_CREATED)
async def register_user(user: UserCreate, current_user: User = Depends(get_current_active_user), db: Session = Depends(get_db)):
    """Register a new user (admin only)"""
    if not has_permission(current_user, "create_user"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions",
        )
    
    db_user = get_user(db, user.username)
    if db_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already registered",
        )
    
    db_email = get_user_by_email(db, user.email)
    if db_email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered",
        )
    
    new_user = create_user(db, user.username, user.password, user.email, user.full_name)
    
    # Assign viewer role by default
    viewer_role = db.query(Role).filter(Role.name == "viewer").first()
    if viewer_role and viewer_role not in new_user.roles:
        new_user.roles.append(viewer_role)
        db.commit()
        db.refresh(new_user)
    
    return {"username": new_user.username, "message": "User created successfully"}

@router.get("/users/me")
async def read_users_me(current_user: User = Depends(get_current_active_user)):
    """Get current user information"""
    return {
        "username": current_user.username,
        "email": current_user.email,
        "full_name": current_user.full_name,
        "roles": [role.name for role in current_user.roles]
    }

@router.post("/verify-token")
async def verify_token(token_request: TokenVerificationRequest, db: Session = Depends(get_db)):
    """Verify JWT token"""
    username = decode_access_token(token_request.token)
    if username is None:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")
    
    user = get_user(db, username=username)
    if user is None:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="User not found")
    
    return {"username": user.username, "valid": True}
