<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attendance Dashboard</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #4a90e2;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --border-color: #dee2e6;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f4f7fc;
            color: #333;
            line-height: 1.6;
        }

        .container {
            width: 95%;
            max-width: 1400px;
            margin: 20px auto;
            padding: 0;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px 20px;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }

        .page-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .back-button {
            display: inline-flex;
            align-items: center;
            background-color: var(--secondary-color);
            color: white;
            padding: 8px 15px;
            text-decoration: none;
            border-radius: 4px;
            font-weight: 500;
            transition: var(--transition);
        }

        .back-button i {
            margin-right: 8px;
        }

        .back-button:hover {
            background-color: #5a6268;
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 20px;
            box-shadow: var(--box-shadow);
            display: flex;
            flex-direction: column;
        }

        .stat-title {
            font-size: 0.9rem;
            color: var(--secondary-color);
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .stat-icon {
            align-self: flex-end;
            font-size: 2.5rem;
            color: rgba(74, 144, 226, 0.2);
            margin-top: -40px;
        }

        .content-card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .card-header {
            padding: 15px 20px;
            background-color: #f8f9fa;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
        }

        .card-body {
            padding: 20px;
        }

        .filter-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-size: 0.9rem;
            font-weight: 500;
            margin-bottom: 8px;
            color: #555;
        }

        .form-control {
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 1rem;
            transition: var(--transition);
        }

        .form-control:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.25);
        }

        .button-group {
            display: flex;
            gap: 10px;
        }

        .form-error {
            color: var(--danger-color);
            font-size: 0.85rem;
            margin-top: 5px;
            display: none;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }

        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }

        .btn {
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn i {
            margin-right: 8px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: #357ab7;
        }

        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        .btn-success {
            background-color: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background-color: #218838;
        }

        .btn-sm {
            padding: 5px 10px;
            font-size: 0.875rem;
        }

        .table-container {
            overflow-x: auto;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .data-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #555;
            position: relative;
            cursor: pointer;
        }

        .data-table th:hover {
            background-color: #e9ecef;
        }

        .data-table th i {
            margin-left: 5px;
            font-size: 0.8rem;
        }

        .data-table tbody tr:hover {
            background-color: #f1f1f1;
        }

        .user-cell {
            display: flex;
            align-items: center;
        }

        .user-image {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 10px;
        }

        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background-color: #f8f9fa;
            border-top: 1px solid var(--border-color);
        }

        .pagination-info {
            font-size: 0.9rem;
            color: #6c757d;
        }

        .pagination-controls {
            display: flex;
            gap: 10px;
        }

        .page-btn {
            padding: 5px 10px;
            border: 1px solid var(--border-color);
            background-color: white;
            border-radius: 4px;
            cursor: pointer;
            transition: var(--transition);
        }

        .page-btn:hover {
            background-color: #e9ecef;
        }

        .page-btn.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .page-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #dee2e6;
        }

        .empty-state h3 {
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .empty-state p {
            font-size: 1rem;
            margin-bottom: 20px;
        }

        .toast {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 15px 25px;
            background-color: #333;
            color: white;
            border-radius: 4px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-width: 300px;
            max-width: 80%;
            font-weight: 500;
        }

        .toast.show {
            opacity: 1;
        }

        .toast.success {
            background-color: var(--success-color);
        }

        .toast.error {
            background-color: var(--danger-color);
        }

        .toast.info {
            background-color: var(--info-color);
        }

        .toast-message {
            flex: 1;
        }

        .toast-close {
            margin-left: 15px;
            cursor: pointer;
            font-size: 18px;
            opacity: 0.7;
            transition: opacity 0.2s;
        }

        .toast-close:hover {
            opacity: 1;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .filter-form {
                grid-template-columns: 1fr;
            }

            .data-table th,
            .data-table td {
                padding: 10px;
            }

            .user-image {
                width: 30px;
                height: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-header">
            <h1 class="page-title">Attendance Dashboard</h1>
            <a href="/face_recognition/face_recognition" class="back-button" id="backBtn">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>

        <div class="stats-container">
            <div class="stat-card">
                <div class="stat-title">Total Records</div>
                <div class="stat-value" id="total-records">{{ total_records }}</div>
                <div class="stat-icon"><i class="fas fa-clipboard-list"></i></div>
            </div>
            <div class="stat-card">
                <div class="stat-title">Unique Users</div>
                <div class="stat-value" id="unique-users">{{ unique_users }}</div>
                <div class="stat-icon"><i class="fas fa-users"></i></div>
            </div>
            <div class="stat-card">
                <div class="stat-title">Today's Attendance</div>
                <div class="stat-value" id="today-attendance">{{ today_attendance }}</div>
                <div class="stat-icon"><i class="fas fa-calendar-day"></i></div>
            </div>
            <div class="stat-card">
                <div class="stat-title">Last Record</div>
                <div class="stat-value" id="last-record">{{ last_record_time }}</div>
                <div class="stat-icon"><i class="fas fa-clock"></i></div>
            </div>
        </div>

        <div class="content-card">
            <div class="card-header">
                <h2 class="card-title">Attendance Records</h2>
                <button class="btn btn-success" id="export-btn">
                    <i class="fas fa-file-export"></i> Export to CSV
                </button>
            </div>
            <div class="card-body">
                <form class="filter-form" id="filter-form">
                    <div class="form-group">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" name="username" id="username" class="form-control" value="{{ request.query_params.get('username', '') }}" placeholder="Filter by username">
                    </div>
                    <div class="form-group">
                        <label for="start_date" class="form-label">Start Date</label>
                        <input type="date" name="start_date" id="start_date" class="form-control" value="{{ request.query_params.get('start_date', '') }}">
                    </div>
                    <div class="form-group">
                        <label for="end_date" class="form-label">End Date</label>
                        <input type="date" name="end_date" id="end_date" class="form-control" value="{{ request.query_params.get('end_date', '') }}">
                    </div>
                    <div class="form-group">
                        <label class="form-label">&nbsp;</label>
                        <div class="button-group">
                            <button type="button" id="apply-filters" class="btn btn-primary">
                                <i class="fas fa-filter"></i> Apply Filters
                            </button>
                            <button type="button" id="clear-filters" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Clear Filters
                            </button>
                        </div>
                    </div>
                </form>

                {% if error_message is defined and error_message %}
                <div class="alert alert-danger">
                    {{ error_message }}
                </div>
                {% endif %}

                <div class="table-container">
                    <table class="data-table" id="attendance-table">
                        <thead>
                            <tr>
                                <th data-sort="username">User <i class="fas fa-sort"></i></th>
                                <th data-sort="timestamp">Date & Time <i class="fas fa-sort"></i></th>
                                <th data-sort="camera">Camera <i class="fas fa-sort"></i></th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if attendance_records %}
                                {% for record in attendance_records %}
                                    <tr>
                                        <td>
                                            <div class="user-cell">
                                                <img src="/images/{{ record.username }}.jpg" alt="{{ record.username }}" class="user-image" onerror="this.src='/cropped_faces/{{ record.username }}.jpg'; this.onerror=function(){this.src='https://via.placeholder.com/40?text=User';}" />
                                                <span>{{ record.username }}</span>
                                            </div>
                                        </td>
                                        <td data-timestamp="{{ record.timestamp.isoformat() if record.timestamp.isoformat is defined else record.timestamp }}">
                                            <span class="full-date">{{ record.timestamp.strftime('%Y-%m-%d %H:%M:%S') if record.timestamp.strftime is defined else record.timestamp }}</span>
                                            <br>
                                            <small class="relative-time"></small>
                                        </td>
                                        <td>{{ record.camera_name if record.camera_name is defined else "System Camera" }}</td>
                                        <td>
                                            <a href="/face_recognition/user/{{ record.user_id }}/page" class="btn btn-secondary btn-sm">
                                                <i class="fas fa-user"></i> View User
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="4">
                                        <div class="empty-state">
                                            <i class="fas fa-calendar-times"></i>
                                            <h3>No Attendance Records</h3>
                                            <p>No attendance records found matching your criteria.</p>
                                        </div>
                                    </td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="pagination">
                <div class="pagination-info">
                    Showing <span id="showing-start">1</span> to <span id="showing-end">{{ attendance_records|length }}</span> of <span id="total-items">{{ total_records }}</span> entries
                </div>
                <div class="pagination-controls" id="pagination-controls">
                    <!-- Pagination buttons will be added here by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // Function to format relative time
        function formatRelativeTime(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffMs = now - date;
            const diffSec = Math.floor(diffMs / 1000);
            const diffMin = Math.floor(diffSec / 60);
            const diffHour = Math.floor(diffMin / 60);
            const diffDay = Math.floor(diffHour / 24);

            if (diffDay > 30) {
                return `${Math.floor(diffDay / 30)} months ago`;
            } else if (diffDay > 0) {
                return `${diffDay} day${diffDay > 1 ? 's' : ''} ago`;
            } else if (diffHour > 0) {
                return `${diffHour} hour${diffHour > 1 ? 's' : ''} ago`;
            } else if (diffMin > 0) {
                return `${diffMin} minute${diffMin > 1 ? 's' : ''} ago`;
            } else {
                return 'Just now';
            }
        }

        // Update relative times
        function updateRelativeTimes() {
            document.querySelectorAll('.relative-time').forEach(el => {
                const timestamp = el.closest('td').getAttribute('data-timestamp');
                el.textContent = formatRelativeTime(timestamp);
            });
        }

        // Sort table
        function sortTable(column, direction) {
            const table = document.getElementById('attendance-table');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Skip if there's an empty state message
            if (rows.length === 1 && rows[0].querySelector('.empty-state')) {
                return;
            }

            rows.sort((a, b) => {
                let aValue, bValue;

                if (column === 'username') {
                    aValue = a.querySelector('.user-cell span').textContent.trim().toLowerCase();
                    bValue = b.querySelector('.user-cell span').textContent.trim().toLowerCase();
                } else if (column === 'timestamp') {
                    aValue = a.querySelector('td[data-timestamp]').getAttribute('data-timestamp');
                    bValue = b.querySelector('td[data-timestamp]').getAttribute('data-timestamp');
                } else if (column === 'camera') {
                    aValue = a.querySelectorAll('td')[2].textContent.trim().toLowerCase();
                    bValue = b.querySelectorAll('td')[2].textContent.trim().toLowerCase();
                }

                if (direction === 'asc') {
                    return aValue > bValue ? 1 : -1;
                } else {
                    return aValue < bValue ? 1 : -1;
                }
            });

            // Clear and re-append rows
            rows.forEach(row => tbody.appendChild(row));
        }

        // Export table to CSV
        function exportTableToCSV() {
            const table = document.getElementById('attendance-table');
            const rows = Array.from(table.querySelectorAll('tr'));

            // Skip if there's an empty state message
            if (rows.length === 1 && rows[0].querySelector('.empty-state')) {
                showToast('No data to export', 'error');
                return;
            }

            let csv = [];

            // Add header row
            const headerRow = rows[0];
            const headers = Array.from(headerRow.querySelectorAll('th'));
            csv.push(headers.map(header => header.textContent.trim().replace(/ .*$/, '')).join(','));

            // Add data rows
            for (let i = 1; i < rows.length; i++) {
                const row = rows[i];
                const cols = Array.from(row.querySelectorAll('td'));

                if (cols.length > 0) {
                    const username = cols[0].querySelector('.user-cell span').textContent.trim();
                    const timestamp = cols[1].querySelector('.full-date').textContent.trim();
                    const camera = cols[2].textContent.trim();

                    csv.push(`${username},${timestamp},${camera}`);
                }
            }

            // Create and download CSV file
            const csvContent = "data:text/csv;charset=utf-8," + csv.join('\n');
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "attendance_records.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showToast('Attendance records exported successfully', 'success');
        }

        // Show toast notification
        function showToast(message, type = 'info') {
            let toast = document.getElementById('toast');
            if (!toast) {
                toast = document.createElement('div');
                toast.id = 'toast';
                document.body.appendChild(toast);
            }

            // Clear previous content
            toast.innerHTML = '';

            // Create message element
            const messageEl = document.createElement('div');
            messageEl.className = 'toast-message';
            messageEl.textContent = message;
            toast.appendChild(messageEl);

            // Create close button
            const closeBtn = document.createElement('div');
            closeBtn.className = 'toast-close';
            closeBtn.innerHTML = '&times;';
            closeBtn.onclick = function() {
                toast.classList.remove('show');
            };
            toast.appendChild(closeBtn);

            // Set class and show
            toast.className = `toast ${type}`;
            toast.classList.add('show');

            // Clear any existing timeout
            if (toast.timeoutId) {
                clearTimeout(toast.timeoutId);
            }

            // Set a new timeout (5 seconds)
            toast.timeoutId = setTimeout(() => {
                toast.classList.remove('show');
            }, 5000);
        }

        // Handle form submission and validation
        function handleFilterForm() {
            const form = document.getElementById('filter-form');
            const startDateInput = document.getElementById('start_date');
            const endDateInput = document.getElementById('end_date');
            const usernameInput = document.getElementById('username');

            // Add error message elements if they don't exist
            if (!document.getElementById('start-date-error')) {
                const startDateError = document.createElement('div');
                startDateError.id = 'start-date-error';
                startDateError.className = 'form-error';
                startDateInput.parentNode.appendChild(startDateError);
            }

            if (!document.getElementById('end-date-error')) {
                const endDateError = document.createElement('div');
                endDateError.id = 'end-date-error';
                endDateError.className = 'form-error';
                endDateInput.parentNode.appendChild(endDateError);
            }

            // Clear filters button
            document.getElementById('clear-filters').addEventListener('click', function() {
                usernameInput.value = '';
                startDateInput.value = '';
                endDateInput.value = '';
                // Redirect to the base attendance URL without parameters
                window.location.href = '/face_recognition/attendance';
            });

            // Apply filters button
            document.getElementById('apply-filters').addEventListener('click', function() {
                applyFilters();
            });

            // Function to apply filters with validation
            function applyFilters() {
                // Reset error messages
                document.getElementById('start-date-error').style.display = 'none';
                document.getElementById('end-date-error').style.display = 'none';

                let isValid = true;

                // If both dates are provided, validate the range
                if (startDateInput.value && endDateInput.value) {
                    const startDate = new Date(startDateInput.value);
                    const endDate = new Date(endDateInput.value);

                    if (startDate > endDate) {
                        document.getElementById('end-date-error').textContent = 'End date must be after start date';
                        document.getElementById('end-date-error').style.display = 'block';
                        showToast('End date must be after start date', 'error');
                        isValid = false;
                    }
                }

                // If only end date is provided without start date, set start date to a reasonable default
                if (!startDateInput.value && endDateInput.value) {
                    // Set start date to 30 days before end date
                    const endDate = new Date(endDateInput.value);
                    const defaultStartDate = new Date(endDate);
                    defaultStartDate.setDate(defaultStartDate.getDate() - 30);

                    // Format as YYYY-MM-DD
                    const year = defaultStartDate.getFullYear();
                    const month = String(defaultStartDate.getMonth() + 1).padStart(2, '0');
                    const day = String(defaultStartDate.getDate()).padStart(2, '0');
                    startDateInput.value = `${year}-${month}-${day}`;
                }

                // If only start date is provided without end date, set end date to today
                if (startDateInput.value && !endDateInput.value) {
                    const today = new Date();
                    const year = today.getFullYear();
                    const month = String(today.getMonth() + 1).padStart(2, '0');
                    const day = String(today.getDate()).padStart(2, '0');
                    endDateInput.value = `${year}-${month}-${day}`;
                }

                if (!isValid) {
                    return false;
                }

                // Build the query string
                const params = new URLSearchParams();
                if (usernameInput.value) {
                    params.append('username', usernameInput.value);
                }
                if (startDateInput.value) {
                    params.append('start_date', startDateInput.value);
                }
                if (endDateInput.value) {
                    params.append('end_date', endDateInput.value);
                }

                // Show loading indicator
                const loadingOverlay = document.createElement('div');
                loadingOverlay.className = 'loading-overlay';
                loadingOverlay.innerHTML = '<div class="spinner"></div>';
                document.body.appendChild(loadingOverlay);

                // Redirect to the URL with parameters
                window.location.href = `/face_recognition/attendance?${params.toString()}`;

                return true;
            }
        }

        // Function to get URL parameters
        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            const results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            // Update relative times
            updateRelativeTimes();
            setInterval(updateRelativeTimes, 60000); // Update every minute

            // Set up sorting
            document.querySelectorAll('th[data-sort]').forEach(th => {
                th.addEventListener('click', function() {
                    const column = this.getAttribute('data-sort');
                    const currentDirection = this.getAttribute('data-direction') || 'desc';
                    const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';

                    // Update direction attribute
                    document.querySelectorAll('th[data-sort]').forEach(el => {
                        el.removeAttribute('data-direction');
                        el.querySelector('i').className = 'fas fa-sort';
                    });

                    this.setAttribute('data-direction', newDirection);
                    this.querySelector('i').className = `fas fa-sort-${newDirection === 'asc' ? 'up' : 'down'}`;

                    // Sort the table
                    sortTable(column, newDirection);
                });
            });

            // Set up export button
            document.getElementById('export-btn').addEventListener('click', exportTableToCSV);

            // Set up filter form handling
            handleFilterForm();

            // Check for error message in the page and display as toast
            const errorAlert = document.querySelector('.alert-danger');
            if (errorAlert) {
                const errorMessage = errorAlert.textContent.trim();
                if (errorMessage) {
                    // Hide the static alert
                    errorAlert.style.display = 'none';

                    // Show as toast notification
                    setTimeout(() => {
                        showToast(errorMessage, 'error');
                    }, 300);
                }
            }

            // Remove any loading overlay that might be present
            const loadingOverlay = document.querySelector('.loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.remove();
            }

            // Preserve face recognition monitoring state when navigating back
            const backBtn = document.getElementById('backBtn');
            if (backBtn) {
                backBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    // Navigate back to the face recognition page without stopping the monitoring
                    window.location.href = '/face_recognition/face_recognition';
                });
            }
        });
    </script>
</body>
</html>