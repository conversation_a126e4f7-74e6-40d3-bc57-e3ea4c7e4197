from ultralytics import YOLO
import cv2
import datetime
from huggingface_hub import hf_hub_download
from face_recognition.utils import generate_deepface_encoding, get_available_devices, get_username_from_id, calculate_cosine_similarity
from face_recognition.models import Attendance, Encoding

# class FaceDetector:
#     def __init__(self, model_repo="arnabdhar/YOLOv8-Face-Detection", model_filename="model.pt"):
#         """
#         Initialize the FaceDetector with a YOLO model.
#         The model is downloaded from the Hugging Face Hub if not already available.
#         """
#         try:
#             self.model_path = hf_hub_download(repo_id=model_repo, filename=model_filename)
#             self.detector = YOLO(self.model_path)
#         except Exception as e:
#             raise RuntimeError(f"Error loading model: {e}")

#     def detect_faces(self, frame):
#         """
#         Detect faces in a given frame using the YOLO model.

#         Args:
#             frame (np.ndarray): Input image frame.

#         Returns:
#             list[dict]: List of detections, each containing 'box', 'confidence', and 'class_id'.
#         """
#         results = self.detector(frame)

#         detections = []
#         for result in results:
#             for faces in result.boxes:
#                 x1, y1, x2, y2 = faces.xyxy[0].cpu().numpy()
#                 confidence = faces.conf[0].cpu().item()
#                 class_id = "face"

#                 detections.append({
#                     "box": [int(x1), int(y1), int(x2), int(y2)],
#                     "confidence": confidence,
#                     "class_id": class_id
#                 })
#         return detections

#     def run(self):
#         """
#         Run face detection on webcam feed.
#         Press 'q' to quit the application.
#         """
#         print("Starting webcam... Press 'q' to quit.")
#         cap = cv2.VideoCapture(0)

#         if not cap.isOpened():
#             raise RuntimeError("Error: Could not open webcam.")

#         try:
#             while True:
#                 ret, frame = cap.read()
#                 if not ret:
#                     print("Error: Could not read frame from webcam.")
#                     break

#                 detections = self.detect_faces(frame)

#                 for detection in detections:
#                     x1, y1, x2, y2 = detection["box"]
#                     confidence = detection["confidence"]

#                     cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
#                     cv2.putText(frame, f"{confidence:.2f}", (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

#                 cv2.imshow("Face Detection", frame)

#                 if cv2.waitKey(1) & 0xFF == ord('q'):
#                     print("Exiting webcam...")
#                     break
#         finally:
#             cap.release()
#             cv2.destroyAllWindows()

# if __name__ == "__main__":
#     try:
#         face_detector = FaceDetector()
#         face_detector.run()
#     except Exception as e:
#         print(f"Error: {e}")

class FaceRecognition:
    def __init__(self, encodings_cache, users_cache):
        self.encodings_cache = encodings_cache
        self.users_cache = users_cache

    def recognize_face(self, face_encoding, db):
        highest_similarity = -1
        most_similar_user = None
        most_similar_user_id = None

        for db_encoding in self.encodings_cache:
            similarity = calculate_cosine_similarity(face_encoding, db_encoding.encoding)
            if similarity > highest_similarity:
                highest_similarity = similarity
                most_similar_user_id = db_encoding.user_id
                most_similar_user = get_username_from_id(most_similar_user_id, self.users_cache)

        similarity_threshold = 0.5
        if most_similar_user and highest_similarity >= similarity_threshold:
            # Log attendance
            time_window_start = datetime.datetime.now() - datetime.timedelta(minutes=1)
            if not db.query(Attendance).filter(
                Attendance.user_id == most_similar_user_id,
                Attendance.timestamp >= time_window_start
            ).first():
                db.add(Attendance(user_id=most_similar_user_id, timestamp=datetime.datetime.now()))
                db.commit()

            return most_similar_user, (0, 255, 0)  # Green for recognized
        return "Unknown", (0, 0, 255)  # Red for unrecognized
