@echo off
echo ============================================================
echo 🔐 Vigilant Eye - Complete System Startup
echo ============================================================

echo.
echo 🚀 Starting Authentication Service...
echo ============================================================
start "🔐 Authentication Service" cmd /k "cd Authentication && python start.py"

echo.
echo ⏳ Waiting 8 seconds for authentication service to start...
timeout /t 8 /nobreak > nul

echo.
echo 🚀 Starting Backend Services...
echo ============================================================
start "⚙️ Backend Services" cmd /k "cd Backend && venv\scripts\activate && uvicorn app.main:app --reload --host 127.0.0.1 --port 8000"

echo.
echo ============================================================
echo ✅ Vigilant Eye System Started Successfully!
echo ============================================================
echo.
echo 🌐 Service URLs:
echo    🔐 Authentication: http://localhost:8001 (Login/Dashboard)
echo    ⚙️  Backend Services: http://127.0.0.1:8000 (Direct Access)
echo.
echo 🔑 Default Login Credentials:
echo    Username: admin
echo    Password: admin123
echo.
echo 📋 Complete User Flow Options:
echo.
echo    🎯 OPTION 1: Full Authentication Flow (Recommended)
echo    1. Go to: http://localhost:8001
echo    2. Login with admin/admin123
echo    3. Access main dashboard
echo    4. Choose from 4 services
echo    5. Logout returns to login page
echo.
echo    🎯 OPTION 2: Direct Backend Access (Development)
echo    1. Go to: http://127.0.0.1:8000
echo    2. Access services directly
echo    3. Logout redirects to authentication service
echo.
echo 🔧 Services Available:
echo    - 👤 Face Recognition
echo    - 🦺 PPE Detection (Helmet Detection)
echo    - 👥 Crowd Detection
echo    - 🔍 Quality Control
echo.
echo 🏗️  Architecture:
echo    - Authentication: Separate service (Port 8001)
echo    - Backend: Clean API services (Port 8000)
echo    - Database: Shared MySQL (vigilanteye)
echo    - Logout: Works from both services
echo.
echo ============================================================
echo.
echo Press any key to exit...
pause > nul
