@echo off
echo ============================================================
echo 🔐 Vigilant Eye - Complete System Startup
echo ============================================================

echo.
echo 🔧 Fixing Backend dependencies...
echo ============================================================
cd Backend
call fix_dependencies.bat
cd ..

echo.
echo 🚀 Starting Authentication Service...
echo ============================================================
start "🔐 Authentication Service" cmd /k "cd authentication && python start.py"

echo.
echo ⏳ Waiting 8 seconds for authentication service to start...
timeout /t 8 /nobreak > nul

echo.
echo 🚀 Starting Backend Services...
echo ============================================================
start "⚙️ Backend Services" cmd /k "cd Backend && venv\scripts\activate && uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"

echo.
echo ============================================================
echo ✅ Vigilant Eye System Started Successfully!
echo ============================================================
echo.
echo 🌐 Service URLs:
echo    Authentication: http://localhost:8001
echo    Backend API:    http://localhost:8000
echo.
echo 🔑 Login Credentials:
echo    Username: admin
echo    Password: admin123
echo.
echo 📋 How to Use:
echo    1. Go to: http://localhost:8001
echo    2. Login with admin/admin123
echo    3. Choose a service from the dashboard
echo    4. Services will open in new tabs
echo.
echo ============================================================
echo.
echo Press any key to exit...
pause > nul
