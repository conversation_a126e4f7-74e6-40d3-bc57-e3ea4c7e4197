@echo off
echo ============================================================
echo 🔐 Vigilant Eye - Complete System Startup
echo ============================================================

echo.
echo 🚀 Starting Authentication Service...
echo ============================================================
start "🔐 Authentication Service" cmd /k "cd Authentication && python start.py"

echo.
echo ⏳ Waiting 8 seconds for authentication service to start...
timeout /t 8 /nobreak > nul

echo.
echo 🚀 Starting Backend Services...
echo ============================================================
start "⚙️ Backend Services" cmd /k "cd Backend && venv\scripts\activate && uvicorn app.main:app --reload --host 127.0.0.1 --port 8000"

echo.
echo ============================================================
echo ✅ Vigilant Eye System Started Successfully!
echo ============================================================
echo.
echo 🌐 Service URLs:
echo    🔐 Authentication: http://localhost:8001
echo    ⚙️  Backend Services: http://127.0.0.1:8000
echo.
echo 🔑 Default Login Credentials:
echo    Username: admin
echo    Password: admin123
echo.
echo 📋 Complete User Flow:
echo    1. Go to: http://localhost:8001
echo    2. Login with admin/admin123
echo    3. Access main dashboard
echo    4. Choose from 4 services:
echo       - 👤 Face Recognition
echo       - 🦺 PPE Detection (Helmet Detection)
echo       - 👥 Crowd Detection
echo       - 🔍 Quality Control
echo    5. Services open in new tabs
echo.
echo 🏗️  Architecture:
echo    - Authentication: Separate service (Port 8001)
echo    - Backend: Clean API services (Port 8000)
echo    - Database: Shared MySQL (vigilanteye)
echo.
echo ============================================================
echo.
echo Press any key to exit...
pause > nul
