@echo off
echo ============================================================
echo 🔐 Vigilant Eye - Complete Connected System Startup
echo ============================================================

echo.
echo 🚀 Starting Authentication Service...
echo ============================================================
start "🔐 Authentication Service" cmd /k "cd Authentication && python start.py"

echo.
echo ⏳ Waiting 8 seconds for authentication service to start...
timeout /t 8 /nobreak > nul

echo.
echo 🚀 Starting Backend Services...
echo ============================================================
start "⚙️ Backend Services" cmd /k "cd Backend && venv\scripts\activate && uvicorn app.main:app --reload --host 127.0.0.1 --port 8000"

echo.
echo ============================================================
echo ✅ Vigilant Eye Connected System Started Successfully!
echo ============================================================
echo.
echo 🌐 Service URLs:
echo    🔐 Authentication: http://localhost:8001 (START HERE)
echo    ⚙️  Backend Services: http://127.0.0.1:8000 (Connected)
echo.
echo 🔑 Default Login Credentials:
echo    Username: admin
echo    Password: admin123
echo.
echo 📋 Complete Connected User Flow:
echo.
echo    🎯 MAIN FLOW (Authentication + Backend Connected):
echo    1. 🔐 Go to: http://localhost:8001
echo    2. 🚪 Login with admin/admin123
echo    3. 🏠 Access Professional Dashboard (home.html functionality)
echo    4. 🎯 Click any of the 4 detection modules:
echo       - 👥 Crowd Detection → Opens Backend service
echo       - 🦺 PPE Detection → Opens Backend service
echo       - 🔍 Quality Control → Opens Backend service
echo       - 👤 Face Recognition → Opens Backend service
echo    5. 🔄 Services open in new tabs (Backend)
echo    6. 🚪 Logout from dashboard → Returns to login
echo.
echo 🔧 Connected Services:
echo    - Authentication Service (Port 8001): Login/Dashboard
echo    - Backend Service (Port 8000): AI Detection Modules
echo    - Dashboard: Professional UI with home.html functionality
echo    - Navigation: Click modules → Opens Backend services
echo.
echo 🏗️  Connected Architecture:
echo    - Authentication: Login → Dashboard (Port 8001)
echo    - Dashboard: Professional UI with module navigation
echo    - Backend: AI Services (Port 8000)
echo    - Connection: Dashboard modules → Backend services
echo    - Logout: Dashboard → Login page
echo.
echo 💡 Key Features:
echo    - Single login for entire system
echo    - Professional dashboard with home.html design
echo    - Seamless navigation to AI services
echo    - Logout returns to login page
echo    - Both services connected and working together
echo.
echo ============================================================
echo.
echo Press any key to exit...
pause > nul
