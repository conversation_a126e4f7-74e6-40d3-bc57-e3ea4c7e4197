@echo off
echo ================================================
echo Starting Vigilant Eye Services
echo ================================================

echo.
echo Starting Authentication Service...
echo ================================================
start "Authentication Service" cmd /k "cd authentication && python start.py"

echo.
echo Waiting 5 seconds for auth service to start...
timeout /t 5 /nobreak > nul

echo.
echo Starting Backend Service...
echo ================================================
start "Backend Service" cmd /k "cd Backend && venv\scripts\activate && uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"

echo.
echo ================================================
echo Services are starting...
echo.
echo Authentication Service: http://localhost:8001
echo Backend Service: http://localhost:8000
echo.
echo Login credentials:
echo Username: admin
echo Password: admin123
echo ================================================
echo.
echo Press any key to exit...
pause > nul
