from pydantic import BaseModel
try:
    from pydantic import EmailStr
except ImportError:
    # Fallback if EmailStr is not available
    EmailStr = str
from typing import Optional

# Token schemas
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

# Request models
class LoginRequest(BaseModel):
    username: str
    password: str

class TokenVerificationRequest(BaseModel):
    token: str

class UserCreate(BaseModel):
    username: str
    email: EmailStr
    full_name: str
    password: str

class RegisterRequest(BaseModel):
    username: str
    email: EmailStr
    full_name: str
    password: str
    role: Optional[str] = "viewer"
