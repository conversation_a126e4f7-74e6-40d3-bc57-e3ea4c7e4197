<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vigilant Eye - Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: #667eea;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-role {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-left: 0.5rem;
        }

        .admin-role {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #8b5a00;
        }

        .viewer-role {
            background: linear-gradient(135deg, #60a5fa, #3b82f6);
            color: white;
        }

        .logout-btn {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .dashboard-title {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
        }

        .dashboard-title h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .dashboard-title p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .service-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            color: inherit;
        }

        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .service-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }

        .crowd-detection { color: #ff6b6b; }
        .helmet-detection { color: #4ecdc4; }
        .quality-control { color: #45b7d1; }
        .face-recognition { color: #96ceb4; }

        .service-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #333;
        }

        .service-card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .service-features {
            list-style: none;
            margin-bottom: 1.5rem;
        }

        .service-features li {
            padding: 0.25rem 0;
            color: #555;
            font-size: 0.9rem;
        }

        .service-features li:before {
            content: "✓";
            color: #4CAF50;
            font-weight: bold;
            margin-right: 0.5rem;
        }

        .launch-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            display: inline-block;
            transition: all 0.3s ease;
            width: 100%;
            text-align: center;
        }

        .launch-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .stats-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .footer {
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .header {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
            }

            .container {
                padding: 1rem;
            }

            .dashboard-title h1 {
                font-size: 2rem;
            }

            .services-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">
            <i class="fas fa-eye"></i>
            <span>Vigilant Eye</span>
        </div>
        <div class="user-info">
            <span>
                <i class="fas fa-user"></i>
                Welcome, {{ user.username }}
                <span class="user-role {{ 'admin-role' if user.role == 'admin' else 'viewer-role' }}">
                    {{ '👑 Admin' if user.role == 'admin' else '👁️ Viewer' }}
                </span>
            </span>
            <a href="/logout" class="logout-btn" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i>
                Logout
            </a>
        </div>
    </div>

    <div class="container">
        <div class="dashboard-title">
            <h1>AI Detection Dashboard</h1>
            <p>Advanced Computer Vision Solutions for Real-time Monitoring</p>
        </div>

        <div class="stats-section">
            <h2 style="margin-bottom: 1.5rem; color: #333;">System Overview</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">4</div>
                    <div class="stat-label">Active Services</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">24/7</div>
                    <div class="stat-label">Monitoring</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">AI</div>
                    <div class="stat-label">Powered</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">Real-time</div>
                    <div class="stat-label">Detection</div>
                </div>
            </div>
        </div>

        <div class="services-grid">
            <div class="service-card" onclick="window.open('/crowd_detection', '_blank')">
                <i class="fas fa-users service-icon crowd-detection"></i>
                <h3>Crowd Detection</h3>
                <p>Monitor crowd density and detect overcrowding situations in real-time using advanced AI algorithms.</p>
                <ul class="service-features">
                    <li>Real-time crowd counting</li>
                    <li>Density heatmaps</li>
                    <li>ROI-based monitoring</li>
                    <li>Alert notifications</li>
                </ul>
                <a href="/crowd_detection" class="launch-btn" target="_blank">
                    <i class="fas fa-rocket"></i> Launch Service
                </a>
            </div>

            <div class="service-card" onclick="window.open('/helmet_detection', '_blank')">
                <i class="fas fa-hard-hat service-icon helmet-detection"></i>
                <h3>PPE Detection</h3>
                <p>Ensure workplace safety by detecting personal protective equipment compliance automatically.</p>
                <ul class="service-features">
                    <li>Helmet detection</li>
                    <li>Safety compliance</li>
                    <li>Multi-camera support</li>
                    <li>Violation alerts</li>
                </ul>
                <a href="/helmet_detection" class="launch-btn" target="_blank">
                    <i class="fas fa-rocket"></i> Launch Service
                </a>
            </div>

            <div class="service-card" onclick="window.open('/quality_control', '_blank')">
                <i class="fas fa-cogs service-icon quality-control"></i>
                <h3>Quality Control</h3>
                <p>Automated quality inspection and defect detection for manufacturing and production lines.</p>
                <ul class="service-features">
                    <li>Defect detection</li>
                    <li>Quality scoring</li>
                    <li>Batch analysis</li>
                    <li>Statistical reports</li>
                </ul>
                <a href="/quality_control" class="launch-btn" target="_blank">
                    <i class="fas fa-rocket"></i> Launch Service
                </a>
            </div>

            <div class="service-card" onclick="window.open('/face_recognition', '_blank')">
                <i class="fas fa-user-check service-icon face-recognition"></i>
                <h3>Face Recognition</h3>
                <p>Advanced facial recognition system for access control and identity verification.</p>
                <ul class="service-features">
                    <li>Face identification</li>
                    <li>Access control</li>
                    <li>Attendance tracking</li>
                    <li>Security monitoring</li>
                </ul>
                <a href="/face_recognition" class="launch-btn" target="_blank">
                    <i class="fas fa-rocket"></i> Launch Service
                </a>
            </div>
        </div>

        <div class="footer">
            <p>&copy; 2024 Vigilant Eye. Advanced AI Detection System.</p>
        </div>
    </div>

    <script>
        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                window.location.href = '/logout';
            }
        }

        // Add click handlers for service cards
        document.querySelectorAll('.service-card').forEach(card => {
            card.addEventListener('click', function(e) {
                if (e.target.tagName !== 'A') {
                    const link = this.querySelector('.launch-btn');
                    if (link) {
                        window.open(link.href, '_blank');
                    }
                }
            });
        });
    </script>
</body>
</html>
