<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Registration - Face Recognition System</title>
    <link rel="stylesheet" href="{{ url_for('face_recognition_static', path='register.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script defer src="{{ url_for('face_recognition_static', path='script.js') }}"></script>
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #1abc9c;
            --danger-color: #e74c3c;
            --warning-color: #f39c12;
            --success-color: #2ecc71;
            --text-light: #ecf0f1;
            --text-dark: #2c3e50;
            --bg-light: #f5f7fa;
            --bg-dark: #34495e;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--bg-light);
            color: var(--text-dark);
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .header {
            background-color: var(--primary-color);
            color: var(--text-light);
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: var(--shadow);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 100;
            height: 60px;
        }

        .header-title {
            font-size: 22px;
            font-weight: 600;
            white-space: nowrap;
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 10px 15px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .btn-primary {
            background-color: var(--secondary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .btn-secondary {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .btn-secondary:hover {
            background-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .btn-success {
            background-color: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background-color: #27ae60;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background-color: #c0392b;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .container {
            max-width: 800px;
            margin: 100px auto 40px; /* Increased top margin to account for fixed header */
            padding: 0 20px;
            flex-grow: 1;
            position: relative;
            z-index: 5;
        }

        .card {
            background-color: white;
            border-radius: 10px;
            box-shadow: var(--shadow);
            padding: 30px;
        }

        .card-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .card-title {
            font-size: 24px;
            color: var(--primary-color);
            margin-bottom: 10px;
            position: relative;
            display: inline-block;
        }

        .card-title:after {
            content: '';
            position: absolute;
            width: 50px;
            height: 3px;
            background-color: var(--secondary-color);
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-dark);
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            transition: var(--transition);
            background-color: var(--bg-light);
        }

        .form-control:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
            outline: none;
        }

        .file-input-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .file-input-btn {
            background-color: var(--secondary-color);
            color: white;
            padding: 12px 15px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .file-input-btn:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
        }

        .file-status {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }

        .submit-btn {
            background-color: var(--success-color);
            color: white;
            padding: 14px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 18px;
            font-weight: 500;
            width: 100%;
            margin-top: 20px;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .submit-btn:hover {
            background-color: #27ae60;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .footer {
            display: flex;
            justify-content: center;
            margin-top: 30px;
            margin-bottom: 20px;
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(4px);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000; /* Ensure it's above everything else */
        }

        .modal-content {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 25px;
            width: 400px;
            max-width: 90%;
            position: relative;
            animation: modalFadeIn 0.3s ease;
        }

        @keyframes modalFadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .modal-content h3 {
            color: var(--primary-color);
            margin-bottom: 20px;
            text-align: center;
        }

        .modal-content button {
            width: 100%;
            margin: 10px 0;
            padding: 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        #upload-btn {
            background-color: var(--secondary-color);
            color: white;
        }

        #capture-btn {
            background-color: var(--accent-color);
            color: white;
        }

        #close-modal-btn {
            background-color: var(--bg-light);
            color: var(--text-dark);
        }

        #camera-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            z-index: 2000;
            transition: opacity 0.3s ease, visibility 0.3s ease;
            display: none; /* Initially hidden */
        }

        .camera-content {
            background-color: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            max-width: 500px;
            width: 90%;
            text-align: center;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .camera-content h3 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 24px;
        }

        .camera-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 15px;
            margin-bottom: 15px;
        }

        #video, #image-preview {
            width: 100%;
            max-width: 400px;
            border-radius: 8px;
            box-shadow: var(--shadow);
            background-color: #f0f0f0;
            margin: 0 auto;
            display: block;
        }

        #video {
            height: 300px;
            object-fit: cover;
        }

        #image-preview {
            margin-top: 15px;
            border: 3px solid var(--success-color);
            max-height: 300px;
            object-fit: contain;
        }

        #capture-image-btn {
            background-color: var(--accent-color);
            color: white;
        }

        #close-camera-btn {
            background-color: var(--danger-color);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-title">
            <i class="fas fa-user-plus"></i> User Registration
        </div>
        <div class="header-actions">
            <button class="btn btn-secondary" id="home-btn">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </button>
            <button class="btn btn-primary" id="users-btn">
                <i class="fas fa-users"></i> Manage Users
            </button>
        </div>
    </div>

    <div class="container">
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">User Information</h2>
                <p>Enter details and provide a profile image</p>
            </div>

            <form action="/face_recognition/register" method="POST" enctype="multipart/form-data" id="registerForm">
                <div class="form-group">
                    <label for="username"><i class="fas fa-user"></i> Username</label>
                    <input type="text" id="username" name="username" class="form-control" placeholder="Enter username" required>
                </div>

                <div class="form-group">
                    <label for="email"><i class="fas fa-envelope"></i> Email</label>
                    <input type="email" id="email" name="email" class="form-control" placeholder="Enter email address" required>
                </div>

                <div class="form-group">
                    <label for="profile_image"><i class="fas fa-camera"></i> Profile Image</label>
                    <div class="file-input-group">
                        <button type="button" id="choose-file-btn" class="file-input-btn">
                            <i class="fas fa-image"></i> Select Profile Image
                        </button>
                        <input type="file" id="upload-input" name="image_file" accept="image/*" style="display: none;" required>
                        <input type="hidden" id="captured_image" name="captured_image">
                        <div id="file-status" class="file-status">No file chosen</div>
                    </div>
                </div>

                <button type="submit" class="submit-btn">
                    <i class="fas fa-user-plus"></i> Register User
                </button>
            </form>
        </div>

        <!-- Footer removed to avoid redundancy with the Manage Users button in header -->
    </div>

    <!-- Modal for image selection -->
    <div id="modal" class="modal" style="display: none;">
        <div class="modal-content">
            <h3>Select Profile Image Source</h3>
            <button type="button" id="upload-btn" class="btn btn-primary">
                <i class="fas fa-upload"></i> Upload from Device
            </button>
            <button type="button" id="capture-btn" class="btn btn-success">
                <i class="fas fa-camera"></i> Take Picture with Camera
            </button>
            <button type="button" id="close-modal-btn" class="btn btn-secondary">
                <i class="fas fa-times"></i> Cancel
            </button>
        </div>
    </div>

    <!-- Camera Section -->
    <div id="camera-container">
        <div class="camera-content">
            <h3>Take Profile Picture</h3>
            <video id="video" autoplay playsinline></video>
            <div class="camera-buttons">
                <button type="button" id="capture-image-btn" class="btn btn-success">
                    <i class="fas fa-camera"></i> Capture Image
                </button>
                <button type="button" id="close-camera-btn" class="btn btn-danger">
                    <i class="fas fa-times"></i> Close Camera
                </button>
            </div>
            <canvas id="canvas" style="display: none;"></canvas>
            <img id="image-preview" alt="Captured Image Preview" style="display: none;">
        </div>
    </div>

    <script>
        // DOM Elements
        const homeBtn = document.getElementById("home-btn");
        const usersBtn = document.getElementById("users-btn");
        const registerForm = document.getElementById("registerForm");

        // Event listener for the "Back to Dashboard" button
        homeBtn.addEventListener("click", () => {
            window.location.href = "/face_recognition/face_recognition";
        });

        // Event listener for the "Manage Users" button
        usersBtn.addEventListener("click", () => {
            window.location.href = "/face_recognition/users_page";
        });

        // Form validation with improved feedback
        registerForm.addEventListener("submit", function(event) {
            const username = document.getElementById("username").value;
            const email = document.getElementById("email").value;
            const fileInput = document.getElementById("upload-input");
            const hasFile = fileInput.files.length > 0 || document.getElementById("captured_image").value;

            if (!username || !email || !hasFile) {
                event.preventDefault();

                // Create toast notification if it doesn't exist
                let toast = document.getElementById("toast");
                if (!toast) {
                    toast = document.createElement("div");
                    toast.id = "toast";
                    document.body.appendChild(toast);

                    // Add toast styling
                    toast.style.position = "fixed";
                    toast.style.bottom = "30px";
                    toast.style.left = "50%";
                    toast.style.transform = "translateX(-50%)";
                    toast.style.backgroundColor = "var(--danger-color)";
                    toast.style.color = "white";
                    toast.style.padding = "12px 25px";
                    toast.style.borderRadius = "8px";
                    toast.style.zIndex = "1200";
                    toast.style.fontWeight = "500";
                    toast.style.boxShadow = "0 5px 15px rgba(0, 0, 0, 0.2)";
                    toast.style.opacity = "0";
                    toast.style.transition = "opacity 0.3s, transform 0.3s";
                }

                // Set message based on what's missing
                let message = "Please fill in all fields";
                if (!hasFile) {
                    message += " and provide a profile image";
                }
                toast.textContent = message + ".";

                // Show the toast
                toast.style.opacity = "1";
                toast.style.transform = "translate(-50%, -10px)";

                // Hide after 3 seconds
                setTimeout(() => {
                    toast.style.opacity = "0";
                    toast.style.transform = "translateX(-50%)";
                }, 3000);
            }
        });
    </script>
</body>
</html>
