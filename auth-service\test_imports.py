#!/usr/bin/env python3

print("Testing imports...")

try:
    import fastapi
    print("✅ FastAPI imported successfully")
except ImportError as e:
    print(f"❌ FastAPI import failed: {e}")

try:
    import uvicorn
    print("✅ Uvicorn imported successfully")
except ImportError as e:
    print(f"❌ Uvicorn import failed: {e}")

try:
    import sqlalchemy
    print("✅ SQLAlchemy imported successfully")
except ImportError as e:
    print(f"❌ SQLAlchemy import failed: {e}")

try:
    import pymysql
    print("✅ PyMySQL imported successfully")
except ImportError as e:
    print(f"❌ PyMySQL import failed: {e}")

try:
    import jose
    print("✅ Jose imported successfully")
except ImportError as e:
    print(f"❌ Jose import failed: {e}")

try:
    import passlib
    print("✅ Passlib imported successfully")
except ImportError as e:
    print(f"❌ Passlib import failed: {e}")

try:
    import httpx
    print("✅ HTTPX imported successfully")
except ImportError as e:
    print(f"❌ HTTPX import failed: {e}")

print("\nTesting app imports...")

try:
    from app.database import User
    print("✅ Database User model imported successfully")
except ImportError as e:
    print(f"❌ Database User model import failed: {e}")

try:
    from app.auth import Token
    print("✅ Auth Token model imported successfully")
except ImportError as e:
    print(f"❌ Auth Token model import failed: {e}")

print("\nImport test completed!")
