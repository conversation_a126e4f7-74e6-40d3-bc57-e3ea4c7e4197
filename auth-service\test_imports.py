#!/usr/bin/env python3

print("🔧 Testing auth-service imports...")

try:
    print("1. Testing basic imports...")
    import fastapi
    print("✅ FastAPI imported")
    
    import uvicorn
    print("✅ Uvicorn imported")
    
    print("2. Testing app imports...")
    
    try:
        from app.database import get_db
        print("✅ Database imports working")
    except Exception as e:
        print(f"❌ Database import failed: {e}")
    
    try:
        from app.models import User
        print("✅ Models imports working")
    except Exception as e:
        print(f"❌ Models import failed: {e}")
    
    try:
        from app.request_models import Token
        print("✅ Request models imports working")
    except Exception as e:
        print(f"❌ Request models import failed: {e}")
    
    try:
        from app.utils import verify_password
        print("✅ Utils imports working")
    except Exception as e:
        print(f"❌ Utils import failed: {e}")
    
    try:
        from app.auth import authenticate_user
        print("✅ Auth imports working")
    except Exception as e:
        print(f"❌ Auth import failed: {e}")
    
    try:
        from app.routes import router
        print("✅ Routes imports working")
    except Exception as e:
        print(f"❌ Routes import failed: {e}")
    
    print("\n🎉 All imports successful!")
    
except Exception as e:
    print(f"❌ Import failed: {e}")
    import traceback
    traceback.print_exc()
