from fastapi import FastAP<PERSON>, Request
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
import uvicorn

print("🚀 Starting Vigilant Eye Backend Services...")

# Import routers conditionally
helmet_detection_router = None
crowd_detection_router = None
quality_control_router = None
face_recognition_router = None

try:
    from app.helmet_detection.routes import router as helmet_detection_router
    print("✅ Helmet Detection module loaded")
except Exception as e:
    print(f"⚠️ Helmet Detection module not available: {e}")

try:
    from app.crowd_detection.routes import router as crowd_detection_router
    print("✅ Crowd Detection module loaded")
except Exception as e:
    print(f"⚠️ Crowd Detection module not available: {e}")

try:
    from app.quality_control.routes import router as quality_control_router
    print("✅ Quality Control module loaded")
except Exception as e:
    print(f"⚠️ Quality Control module not available: {e}")

try:
    from app.face_recognition.routes import router as face_recognition_router
    print("✅ Face Recognition module loaded")
except Exception as e:
    print(f"⚠️ Face Recognition module not available: {e}")

# Initialize FastAPI app
app = FastAPI(title="Vigilant Eye Backend Services", version="1.0.0")

# Setup templates and static files
templates = Jinja2Templates(directory="app/templates")

# CORS middleware
from fastapi.middleware.cors import CORSMiddleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup static files
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# Mount static directories for modules (conditionally)
try:
    app.mount("/helmet_detection_static", StaticFiles(directory="app/helmet_detection/static"), name="helmet_detection_static")
    app.mount("/crowd_detection_static", StaticFiles(directory="app/crowd_detection/static"), name="crowd_detection_static")
    app.mount("/add_roi_static", StaticFiles(directory="app/crowd_detection/static"), name="add_roi_static")
    app.mount("/quality_control_static", StaticFiles(directory="app/quality_control/static"), name="quality_control_static")
    app.mount("/face_recognition_static", StaticFiles(directory="app/face_recognition/static"), name="face_recognition_static")
    print("✅ Module static directories mounted")
except Exception as e:
    print(f"⚠️ Some module static directories not available: {e}")

try:
    app.mount("/Dataset", StaticFiles(directory="Dataset"), name="dataset")
    app.mount("/cropped_faces", StaticFiles(directory="cropped_faces"), name="cropped_faces")
    print("✅ Dataset directories mounted")
except Exception as e:
    print(f"⚠️ Dataset directories not available: {e}")

# Mount the static/images directory for user profile images
import os
import shutil
from pathlib import Path

try:
    # Create the directory if it doesn't exist
    os.makedirs("static/images", exist_ok=True)

    # Copy cropped face images to static/images for users that don't have an original image
    cropped_faces_dir = Path("cropped_faces")
    if cropped_faces_dir.exists():
        static_images_dir = Path("static/images")
        static_images = [f.name for f in static_images_dir.glob("*.jpg")]

        for cropped_face in cropped_faces_dir.glob("*.jpg"):
            if cropped_face.name not in static_images:
                print(f"Copying {cropped_face.name} to static/images directory...")
                shutil.copy(cropped_face, static_images_dir / cropped_face.name)

    # Mount the static/images directory at the root level
    app.mount("/images", StaticFiles(directory="static/images"), name="user_images")
    print("✅ User images directory mounted")
except Exception as e:
    print(f"⚠️ User images directory setup failed: {e}")

# Include routers conditionally
if helmet_detection_router:
    app.include_router(helmet_detection_router, prefix="/helmet_detection", tags=["Helmet Detection"])
    print("✅ Helmet Detection routes included")

if crowd_detection_router:
    app.include_router(crowd_detection_router, prefix="/crowd_detection", tags=["Crowd Detection"])
    print("✅ Crowd Detection routes included")

if quality_control_router:
    app.include_router(quality_control_router, prefix="/quality_control", tags=["Quality Control"])
    print("✅ Quality Control routes included")

if face_recognition_router:
    app.include_router(face_recognition_router, prefix="/face_recognition", tags=["Face Recognition"])
    print("✅ Face Recognition routes included")

print("🎉 Backend service initialization complete!")

# Home page - main dashboard
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    return templates.TemplateResponse("home.html", {"request": request})

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "Vigilant Eye Backend"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)