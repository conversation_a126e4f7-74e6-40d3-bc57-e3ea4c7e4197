from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, HTTPException, status, Form
from fastapi.security import OAuth2PasswordRequestForm
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from datetime import timed<PERSON><PERSON>
from sqlalchemy.orm import Session

# Import authentication modules
from app.auth import (
    Token, User, UserCreate, authenticate_user, create_access_token,
    get_current_active_user, ACCESS_TOKEN_EXPIRE_MINUTES,
    get_user, create_user, verify_token
)
from app.database import get_db, create_tables
from app.helmet_detection.routes import router as helmet_detection_router
from app.crowd_detection.routes import router as crowd_detection_router
from app.quality_control.routes import router as quality_control_router
from app.face_recognition.routes import router as face_recognition_router

# Initialize FastAPI app
app = FastAPI(title="Vigilant Eye - Integrated System", version="1.0.0")

# Create database tables if they don't exist
create_tables()

# Setup templates and static files
templates = Jinja2Templates(directory="app/templates")

# CORS middleware already imported above
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup static files
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# Mount static directories for modules
app.mount("/helmet_detection_static", StaticFiles(directory="app/helmet_detection/static"), name="helmet_detection_static")
app.mount("/crowd_detection_static", StaticFiles(directory="app/crowd_detection/static"), name="crowd_detection_static")
app.mount("/add_roi_static", StaticFiles(directory="app/crowd_detection/static"), name="add_roi_static")
app.mount("/quality_control_static", StaticFiles(directory="app/quality_control/static"), name="quality_control_static")
app.mount("/face_recognition_static", StaticFiles(directory="app/face_recognition/static"), name="face_recognition_static")
app.mount("/Dataset", StaticFiles(directory="Dataset"), name="dataset")
app.mount("/cropped_faces", StaticFiles(directory="cropped_faces"), name="cropped_faces")

# Mount the static/images directory for user profile images
import os
import shutil
from pathlib import Path

# Create the directory if it doesn't exist
os.makedirs("static/images", exist_ok=True)

# Copy cropped face images to static/images for users that don't have an original image
cropped_faces_dir = Path("cropped_faces")
if cropped_faces_dir.exists():
    static_images_dir = Path("static/images")
    static_images = [f.name for f in static_images_dir.glob("*.jpg")]

    for cropped_face in cropped_faces_dir.glob("*.jpg"):
        if cropped_face.name not in static_images:
            print(f"Copying {cropped_face.name} to static/images directory...")
            shutil.copy(cropped_face, static_images_dir / cropped_face.name)

# Mount the static/images directory at the root level
app.mount("/images", StaticFiles(directory="static/images"), name="user_images")

# Include routers
app.include_router(helmet_detection_router, prefix="/helmet_detection", tags=["Helmet Detection"])
app.include_router(crowd_detection_router, prefix="/crowd_detection", tags=["Crowd Detection"])
app.include_router(quality_control_router, prefix="/quality_control", tags=["Quality Control"])
app.include_router(face_recognition_router, prefix="/face_recognition", tags=["Face Recognition"])

# Cookie-based authentication dependency
async def get_current_user_from_cookie(request: Request, db: Session = Depends(get_db)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    # Get token from cookie
    token = request.cookies.get("access_token")
    if not token:
        raise credentials_exception

    # Strip the "Bearer " prefix if present
    if token.startswith("Bearer "):
        token = token[7:]

    try:
        user = verify_token(token, db)
        if not user or user.disabled:
            raise credentials_exception
        return user
    except:
        raise credentials_exception

# Root redirect - start with login
@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    # Check if user is logged in
    token = request.cookies.get("access_token")
    if token:
        try:
            db = next(get_db())
            user = verify_token(token, db)
            if user:
                return RedirectResponse(url="/dashboard", status_code=status.HTTP_302_FOUND)
        except:
            pass

    # Not logged in, redirect to login
    return RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)

# Login page
@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

# Dashboard page - main page after login (replaces home.html)
@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request):
    # Simple check for token in cookie
    token = request.cookies.get("access_token")
    if not token:
        return RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)

    try:
        db = next(get_db())
        user = verify_token(token, db)
        if not user:
            return RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)
    except:
        return RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)

    return templates.TemplateResponse("dashboard.html", {
        "request": request,
        "user": user
    })

# Login form submission
@app.post("/login", response_class=HTMLResponse)
async def login(
    request: Request,
    username: str = Form(...),
    password: str = Form(...),
    remember: bool = Form(False),
    db: Session = Depends(get_db)
):
    user = authenticate_user(db, username, password)

    if not user:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Invalid username or password"}
        )

    # Create access token
    access_token_expires = timedelta(
        days=30 if remember else 1  # 30 days if remember is checked, 1 day otherwise
    )
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )

    # Create redirect response to dashboard
    response = RedirectResponse(url="/dashboard", status_code=status.HTTP_302_FOUND)

    # Set cookie with token
    response.set_cookie(
        key="access_token",
        value=f"Bearer {access_token}",
        httponly=True,
        max_age=int(access_token_expires.total_seconds()),
        expires=int(access_token_expires.total_seconds()),
        secure=False,  # Set to True in production with HTTPS
        samesite="lax"
    )

    return response

# User registration endpoint
@app.post("/register", response_class=HTMLResponse)
async def register(
    request: Request,
    username: str = Form(...),
    email: str = Form(...),
    full_name: str = Form(...),
    password: str = Form(...),
    db: Session = Depends(get_db)
):
    existing_user = get_user(db, username)
    if existing_user:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Username already registered"}
        )

    user_data = UserCreate(
        username=username,
        email=email,
        full_name=full_name,
        password=password
    )

    create_user(db, user_data)
    return templates.TemplateResponse(
        "login.html",
        {"request": request, "success": "Account created successfully! Please login."}
    )

# Logout endpoint
@app.get("/logout")
@app.post("/logout")
async def logout():
    response = RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)
    response.delete_cookie("access_token", path="/")
    return response

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "backend", "message": "Backend services are running"}