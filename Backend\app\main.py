from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, HTTPException, status, Form
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from datetime import timedelta
import uvicorn

# Import authentication components
from app.auth import (
    Token, User, UserCreate, authenticate_user, create_access_token,
    get_current_active_user, get_current_active_user_from_cookie, ACCESS_TOKEN_EXPIRE_MINUTES,
    get_user, get_user_by_email, create_user, verify_token
)
from app.database import get_db, init_database

# Import routers
from app.helmet_detection.routes import router as helmet_detection_router
from app.crowd_detection.routes import router as crowd_detection_router
from app.quality_control.routes import router as quality_control_router
from app.face_recognition.routes import router as face_recognition_router

# Initialize FastAPI app
app = FastAPI(title="Vigilant Eye - AI Detection System", version="1.0.0")

# Setup templates and static files
templates = Jinja2Templates(directory="app/templates")

# CORS middleware
from fastapi.middleware.cors import CORSMiddleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup static files
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# Mount static directories for modules
app.mount("/helmet_detection_static", StaticFiles(directory="app/helmet_detection/static"), name="helmet_detection_static")
app.mount("/crowd_detection_static", StaticFiles(directory="app/crowd_detection/static"), name="crowd_detection_static")
app.mount("/add_roi_static", StaticFiles(directory="app/crowd_detection/static"), name="add_roi_static")
app.mount("/quality_control_static", StaticFiles(directory="app/quality_control/static"), name="quality_control_static")
app.mount("/face_recognition_static", StaticFiles(directory="app/face_recognition/static"), name="face_recognition_static")
app.mount("/Dataset", StaticFiles(directory="Dataset"), name="dataset")
app.mount("/cropped_faces", StaticFiles(directory="cropped_faces"), name="cropped_faces")

# Mount the static/images directory for user profile images
import os
import shutil
from pathlib import Path

# Create the directory if it doesn't exist
os.makedirs("static/images", exist_ok=True)

# Copy cropped face images to static/images for users that don't have an original image
cropped_faces_dir = Path("cropped_faces")
if cropped_faces_dir.exists():
    static_images_dir = Path("static/images")
    static_images = [f.name for f in static_images_dir.glob("*.jpg")]

    for cropped_face in cropped_faces_dir.glob("*.jpg"):
        if cropped_face.name not in static_images:
            print(f"Copying {cropped_face.name} to static/images directory...")
            shutil.copy(cropped_face, static_images_dir / cropped_face.name)

# Mount the static/images directory at the root level
app.mount("/images", StaticFiles(directory="static/images"), name="user_images")

# Include routers
app.include_router(helmet_detection_router, prefix="/helmet_detection", tags=["Helmet Detection"])
app.include_router(crowd_detection_router, prefix="/crowd_detection", tags=["Crowd Detection"])
app.include_router(quality_control_router, prefix="/quality_control", tags=["Quality Control"])
app.include_router(face_recognition_router, prefix="/face_recognition", tags=["Face Recognition"])

# Initialize database on startup
@app.on_event("startup")
async def startup_event():
    print("🚀 Starting Vigilant Eye Backend...")
    init_database()
    print("✅ Backend startup complete!")

# Home page - redirects to login
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    return RedirectResponse(url="/login", status_code=302)

# Login page
@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

# Login endpoint
@app.post("/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

# Login form submission
@app.post("/login", response_class=HTMLResponse)
async def login(request: Request, username: str = Form(...), password: str = Form(...), db: Session = Depends(get_db)):
    user = authenticate_user(db, username, password)
    if not user:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Invalid username or password"}
        )

    # Create access token
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )

    # Redirect to dashboard with token in cookie
    response = RedirectResponse(url="/dashboard", status_code=status.HTTP_302_FOUND)
    response.set_cookie(
        key="access_token",
        value=f"Bearer {access_token}",
        httponly=True,
        max_age=ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        expires=ACCESS_TOKEN_EXPIRE_MINUTES * 60,
    )
    return response

# Dashboard page - main page after login
@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request, db: Session = Depends(get_db)):
    # Simple check for token in cookie
    token = request.cookies.get("access_token")
    if not token:
        return RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)

    # Strip Bearer prefix if present
    if token.startswith("Bearer "):
        token = token[7:]

    try:
        # Get actual user from token
        user_db = verify_token(token, db)
        if not user_db:
            return RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)

        # Create user object for template
        user = {
            "username": user_db.username,
            "email": user_db.email,
            "full_name": user_db.full_name,
            "role": getattr(user_db, 'role', 'viewer')
        }
    except:
        return RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)

    return templates.TemplateResponse("dashboard.html", {
        "request": request,
        "user": user
    })

# Logout endpoint
@app.get("/logout")
@app.post("/logout")
async def logout():
    response = RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)
    response.delete_cookie(key="access_token")
    return response

# User registration endpoint
@app.post("/register", response_class=HTMLResponse)
async def register(
    request: Request,
    username: str = Form(...),
    email: str = Form(...),
    full_name: str = Form(...),
    password: str = Form(...),
    role: str = Form("viewer"),  # Default to viewer role
    db: Session = Depends(get_db)
):
    # Check if username already exists
    existing_user = get_user(db, username)

    # Check if email already exists
    existing_email = get_user_by_email(db, email)

    # Handle different validation scenarios
    if existing_user and existing_email:
        # Case 2: Both username and email exist
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Username and Email ID already exist. Please go to login page."}
        )
    elif existing_user:
        # Case 3: Only username exists
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Username already exists. Please go to login page."}
        )
    elif existing_email:
        # Case 1: Only email exists
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Email ID already exists. Please go to login page."}
        )

    # If both username and email are available, create the user
    user_data = UserCreate(
        username=username,
        email=email,
        full_name=full_name,
        password=password,
        role=role
    )

    create_user(db, user_data)
    return templates.TemplateResponse(
        "login.html",
        {"request": request, "success": "Account created successfully! Please login."}
    )

# Token validation endpoint for other services
@app.get("/validate-token")
async def validate_token(request: Request, db: Session = Depends(get_db)):
    # Get token from Authorization header or cookie
    token = request.headers.get("Authorization")
    if not token:
        token = request.cookies.get("access_token")

    if not token:
        raise HTTPException(status_code=401, detail="No token provided")

    # Strip Bearer prefix if present
    if token.startswith("Bearer "):
        token = token[7:]

    try:
        user = verify_token(token, db)
        if user:
            return {
                "valid": True,
                "user": {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "full_name": user.full_name,
                    "role": getattr(user, 'role', 'viewer'),
                    "disabled": user.disabled
                }
            }
        else:
            return {"valid": False}
    except:
        return {"valid": False}

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "backend", "message": "Backend services are running"}