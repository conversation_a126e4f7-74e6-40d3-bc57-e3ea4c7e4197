from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from app.helmet_detection.routes import router as helmet_detection_router
from app.crowd_detection.routes import router as crowd_detection_router
from app.quality_control.routes import router as quality_control_router
from app.face_recognition.routes import router as face_recognition_router

# Initialize FastAPI app
app = FastAPI(title="Vigilant Eye Backend Services", version="1.0.0")

# Setup templates and static files
templates = Jinja2Templates(directory="app/templates")

# CORS middleware
from fastapi.middleware.cors import CORSMiddleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup static files
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# Mount static directories for modules
app.mount("/helmet_detection_static", StaticFiles(directory="app/helmet_detection/static"), name="helmet_detection_static")
app.mount("/crowd_detection_static", StaticFiles(directory="app/crowd_detection/static"), name="crowd_detection_static")
app.mount("/add_roi_static", StaticFiles(directory="app/crowd_detection/static"), name="add_roi_static")
app.mount("/quality_control_static", StaticFiles(directory="app/quality_control/static"), name="quality_control_static")
app.mount("/face_recognition_static", StaticFiles(directory="app/face_recognition/static"), name="face_recognition_static")
app.mount("/Dataset", StaticFiles(directory="Dataset"), name="dataset")
app.mount("/cropped_faces", StaticFiles(directory="cropped_faces"), name="cropped_faces")

# Mount the static/images directory for user profile images
import os
import shutil
from pathlib import Path

# Create the directory if it doesn't exist
os.makedirs("static/images", exist_ok=True)

# Copy cropped face images to static/images for users that don't have an original image
cropped_faces_dir = Path("cropped_faces")
if cropped_faces_dir.exists():
    static_images_dir = Path("static/images")
    static_images = [f.name for f in static_images_dir.glob("*.jpg")]

    for cropped_face in cropped_faces_dir.glob("*.jpg"):
        if cropped_face.name not in static_images:
            print(f"Copying {cropped_face.name} to static/images directory...")
            shutil.copy(cropped_face, static_images_dir / cropped_face.name)

# Mount the static/images directory at the root level
app.mount("/images", StaticFiles(directory="static/images"), name="user_images")

# Include routers
app.include_router(helmet_detection_router, prefix="/helmet_detection", tags=["Helmet Detection"])
app.include_router(crowd_detection_router, prefix="/crowd_detection", tags=["Crowd Detection"])
app.include_router(quality_control_router, prefix="/quality_control", tags=["Quality Control"])
app.include_router(face_recognition_router, prefix="/face_recognition", tags=["Face Recognition"])

# Authentication service URL
AUTH_SERVICE_URL = "http://localhost:8001"

# Home page - main dashboard
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    return templates.TemplateResponse("home.html", {"request": request})

# Redirect authentication endpoints to Authentication service
@app.get("/login")
async def login_redirect():
    return RedirectResponse(url=f"{AUTH_SERVICE_URL}/login", status_code=302)

@app.get("/logout")
@app.post("/logout")
async def logout_redirect():
    return RedirectResponse(url=f"{AUTH_SERVICE_URL}/logout", status_code=302)

@app.get("/dashboard")
async def dashboard_redirect():
    return RedirectResponse(url=f"{AUTH_SERVICE_URL}/dashboard", status_code=302)

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "backend", "message": "Backend services are running"}