from fastapi import <PERSON><PERSON><PERSON>, Request, Form, Depends, status, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.security import OAuth2Pass<PERSON>R<PERSON><PERSON>Form
from datetime import timed<PERSON><PERSON>
from sqlalchemy.orm import Session
from app.helmet_detection.routes import router as helmet_detection_router
from app.crowd_detection.routes import router as crowd_detection_router
from app.quality_control.routes import router as quality_control_router
from app.face_recognition.routes import router as face_recognition_router

# Import auth and database modules
from app.auth import (
    Token, User, UserCreate, authenticate_user, create_access_token,
    get_current_active_user, ACCESS_TOKEN_EXPIRE_MINUTES,
    get_user, create_user
)
from app.database import get_db, create_tables

# Import middleware
from app.middleware import auth_middleware

# Initialize FastAPI app
app = FastAPI()

# Create database tables if they don't exist
create_tables()

# Add middleware for authentication
app.middleware("http")(auth_middleware)

# CORS middleware
from fastapi.middleware.cors import CORSMiddleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup templates and static files
templates = Jinja2Templates(directory="app/templates")
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# Mount static directories for modules
app.mount("/helmet_detection_static", StaticFiles(directory="app/helmet_detection/static"), name="helmet_detection_static")
app.mount("/crowd_detection_static", StaticFiles(directory="app/crowd_detection/static"), name="crowd_detection_static")
app.mount("/add_roi_static", StaticFiles(directory="app/crowd_detection/static"), name="add_roi_static")
app.mount("/quality_control_static", StaticFiles(directory="app/quality_control/static"), name="quality_control_static")
app.mount("/face_recognition_static", StaticFiles(directory="app/face_recognition/static"), name="face_recognition_static")
app.mount("/Dataset", StaticFiles(directory="Dataset"), name="dataset")
app.mount("/cropped_faces", StaticFiles(directory="cropped_faces"), name="cropped_faces")

# Mount the static/images directory for user profile images
import os
import shutil
from pathlib import Path

# Create the directory if it doesn't exist
os.makedirs("static/images", exist_ok=True)

# Copy cropped face images to static/images for users that don't have an original image
cropped_faces_dir = Path("cropped_faces")
if cropped_faces_dir.exists():
    static_images_dir = Path("static/images")
    static_images = [f.name for f in static_images_dir.glob("*.jpg")]

    for cropped_face in cropped_faces_dir.glob("*.jpg"):
        if cropped_face.name not in static_images:
            print(f"Copying {cropped_face.name} to static/images directory...")
            shutil.copy(cropped_face, static_images_dir / cropped_face.name)

# Mount the static/images directory at the root level
app.mount("/images", StaticFiles(directory="static/images"), name="user_images")

# Include routers
app.include_router(helmet_detection_router, prefix="/helmet_detection", tags=["Helmet Detection"])
app.include_router(crowd_detection_router, prefix="/crowd_detection", tags=["Crowd Detection"])
app.include_router(quality_control_router, prefix="/quality_control", tags=["Quality Control"])
app.include_router(face_recognition_router, prefix="/face_recognition", tags=["Face Recognition"])



# Home page - protected by middleware
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    return templates.TemplateResponse("home.html", {"request": request})

# Login page
@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

# Login form submission
@app.post("/login", response_class=HTMLResponse)
async def login(
    request: Request,
    username: str = Form(...),
    password: str = Form(...),
    remember: bool = Form(False),
    db: Session = Depends(get_db)
):
    user = authenticate_user(db, username, password)

    if not user:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Invalid username or password"}
        )

    # Create access token
    access_token_expires = timedelta(
        days=30 if remember else 1  # 30 days if remember is checked, 1 day otherwise
    )
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )

    # Create redirect response
    response = RedirectResponse(url="/", status_code=status.HTTP_302_FOUND)

    # Set cookie with token
    response.set_cookie(
        key="access_token",
        value=f"Bearer {access_token}",
        httponly=True,
        max_age=int(access_token_expires.total_seconds()),
        expires=int(access_token_expires.total_seconds()),
        secure=False,  # Set to True in production with HTTPS
        samesite="lax"
    )

    return response

# Logout
@app.get("/logout")
async def logout():
    response = RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)
    response.delete_cookie("access_token")
    return response

# API token endpoint for programmatic access
@app.post("/token", response_model=Token)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )

    return {"access_token": access_token, "token_type": "bearer"}

# User registration endpoint (optional)
@app.post("/register", response_class=HTMLResponse)
async def register(
    request: Request,
    username: str = Form(...),
    email: str = Form(...),
    full_name: str = Form(...),
    password: str = Form(...),
    db: Session = Depends(get_db)
):
    existing_user = get_user(db, username)
    if existing_user:
        return templates.TemplateResponse(
            "register.html",  # You'll need to create this template
            {"request": request, "error": "Username already registered"}
        )

    user_data = UserCreate(
        username=username,
        email=email,
        full_name=full_name,
        password=password
    )

    create_user(db, user_data)
    return RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)