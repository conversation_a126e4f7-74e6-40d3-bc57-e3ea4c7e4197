opencv-python==*********
ultralytics==8.3.133
# "fastapi[standard]"
pydantic==2.11.4
PyMySQL==1.1.1
deepface==0.0.93
tf-keras==2.19.0
keras-facenet==0.3.2
deep-sort-realtime==1.3.2
huggingface-hub==0.31.2
# pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu126

torch==2.2.2+cpu
torchvision==0.17.2+cpu

# Authentication dependencies
fastapi==0.104.1
uvicorn==0.24.0
sqlalchemy==2.0.23
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
jinja2==3.1.2
email-validator==2.1.0
