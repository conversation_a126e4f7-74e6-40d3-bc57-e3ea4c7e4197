* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f4f4f4;
}

.container {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  grid-template-rows: auto; /* Adjust rows dynamically */
  gap: 10px;
  width: 100vw; /* Full viewport width */
  height: 100vh; /* Full viewport height */
  max-width: 100%;
  max-height: 100%;
  background: #fff;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  box-sizing: border-box; /* Ensure padding and border are included in the width/height */
  overflow: hidden; /* Prevent content overflow */
}



.sidebar {
  background-color: #202020;
  color: #fff;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  width: 300px;
  overflow: hidden;
}

.logo {
  /* font-size: 1.5rem; */
  margin-top: 30px;
  text-align: center;
  width: 100%;
}
.logo img{
  width: 100%;
  max-width: 150px;
  height: auto;
}

.menu-btn {
  margin: 10px 0;
  padding: 10px 20px;
  background-color: #555;
  color: #fff;
  border: none;
  cursor: pointer;
  width: 100%;
  text-align: left;
  border-radius: 10px;  
}

.menu-btn:hover {
  background-color: #333;

}
.homepage-btn {
  margin: 10px 0;
  padding: 10px 20px;
  background-color: #555;
  color: #fff;
  border: none;
  cursor: pointer;
  width: 100%;
  text-align: start;
  border-radius: 10px;
}

.homepage-btn:hover {
  background-color: #333;
}

.setting-btn {
  margin: 10px 0;
  padding: 10px 20px;
  background-color: #555;
  color: #fff;
  border: none;
  cursor: pointer;
  width: 100%;
  text-align: start;
  border-radius: 10px;
}

.setting-btn:hover {
  background-color: #333;
}

.main {
  display: flex;
  flex-direction: column;
  padding: 20px;
  max-height: 100dvh;
  overflow-y: auto;
}

.webcam-section {
  text-align: center;
  padding: 10px;
  background-color: #f0f0f0;
  margin-bottom: 20px;
}

.video-grid {
  display: flex;
  flex-wrap: wrap;
  grid-template-columns: repeat(2, 1fr);
  gap: 50px;
}

.camera-container{
  width: 100%;
}
.camera-container h3{
  text-align: center;
  font-size: 20px;
  font-weight: 600;
}
.camera-container img{
  width: 100%;
  height: auto;
  object-fit: contain;
  border-radius: 8px;
}

.video-box {
  background-color: #ddd;
  height: 100px;
  border-radius: 8px;
}

.live-observations {
  background-color: #f8f8f8;
  padding: 20px;
}

.live-observations h2 {
  margin-bottom: 20px;
  text-align: center;
}

.live-observations ul {
  list-style: none;
}

.live-observations li {
  margin-bottom: 10px;
  padding: 8px;
  background: #eee;
  border-radius: 5px;
}

.camera-feed{
  width: 400px;
  max-width: 80%;
  height: auto;
  max-height: 400px;
}
.camera-feed img{
  object-fit: contain;
  height: 100%;
  width: 100%;
}











.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: #fff;
  margin: 10% auto;
  padding: 30px;
  border: 1px solid #888;
  width: 60%;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.modal-content h2 {
  font-size: 1.8em;
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.close-btn {
  color: #aaa;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
  position: absolute;
  top: 10px;
  right: 10px;
}

.close-btn:hover {
  color: #000;
}

.camera-list {
  margin-top: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.camera-item {
  display: flex;
  justify-content: space-between;
  padding: 12px;
  border-bottom: 1px solid #ccc;
  background-color: #fff;
  border-radius: 5px;
  margin-bottom: 8px;
}

.camera-item:last-child {
  border-bottom: none;
}

.camera-item button {
  background-color: #f44336;
  color: white;
  padding: 8px 12px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
}

.camera-item button:hover {
  background-color: #d32f2f;
}

#cameraForm input {
  width: 100%;
  padding: 10px;
  margin: 10px 0;
  border-radius: 5px;
  border: 1px solid #ccc;
  font-size: 16px;
}

#cameraForm button {
  padding: 12px 20px;
  background-color: #191530;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  width: 100%;
  margin-top: 15px;
}

#cameraForm button:hover {
  background-color: #45a049;
}

#cameraList {
  margin-top: 20px;
  padding: 15px;
  border-radius: 5px;
  background-color: #f5f5f5;
}







/* Alert Management Modal Styles */
#alertStatusContainer {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  text-align: center;
}

#alertStatus {
  font-weight: bold;
  font-size: 1.2em;
}

.alert-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 10px;
}

.alert-buttons button {
  padding: 8px 16px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;
}

#startAlertBtn {
  background-color: #4CAF50;
  color: white;
}

#stopAlertBtn {
  background-color: #f44336;
  color: white;
}

.schedule-container, .notification-settings, .frequency-settings {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

h3 {
  margin-top: 20px;
  margin-bottom: 10px;
  color: #333;
}

.checkbox-group {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 10px;
  margin-top: 10px;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  gap: 5px;
}

.time-settings {
  display: flex;
  gap: 20px;
  margin-top: 15px;
}

.time-input {
  flex: 1;
}

.time-input input {
  width: 100%;
  padding: 8px;
  border-radius: 5px;
  border: 1px solid #ccc;
  margin-top: 5px;
}

.input-group {
  display: flex;
  margin-bottom: 10px;
}

.input-group input {
  flex: 1;
  padding: 8px;
  border-radius: 5px 0 0 5px;
  border: 1px solid #ccc;
}

.input-group button {
  padding: 8px 15px;
  background-color: #191530;
  color: white;
  border: none;
  border-radius: 0 5px 5px 0;
  cursor: pointer;
}

.recipient-list {
  max-height: 150px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 10px;
  background-color: white;
}

.recipient-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid #eee;
}

.recipient-item:last-child {
  border-bottom: none;
}

.remove-btn {
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 3px;
  padding: 3px 8px;
  cursor: pointer;
  font-size: 12px;
}

.frequency-settings input {
  width: 100%;
  padding: 8px;
  border-radius: 5px;
  border: 1px solid #ccc;
  margin-top: 5px;
}

.save-btn {
  background-color: #191530;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 12px 20px;
  width: 100%;
  cursor: pointer;
  font-size: 16px;
  margin-top: 20px;
}

.save-btn:hover {
  background-color: #45a049;
}