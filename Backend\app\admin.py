# create_admin.py
from sqlalchemy.orm import Session
from auth import get_password_hash
from database import SessionLocal, UserDB, create_tables

def create_admin_user():
    # Ensure tables exist
    create_tables()
    
    # Create a session
    db = SessionLocal()
    
    # Check if admin already exists
    admin = db.query(UserDB).filter(UserDB.username == "admin").first()
    if admin:
        print("Admin user already exists")
        db.close()
        return
    
    # Create admin user
    admin_user = UserDB(
        username="admin",
        email="<EMAIL>",
        full_name="vishnu",
        hashed_password=get_password_hash("admin123"),  # Change this password!
        disabled=False
    )
    
    db.add(admin_user)
    db.commit()
    db.close()
    
    print("Admin user created successfully")

if __name__ == "__main__":
    create_admin_user()