# 🔐 Authentication Service - Role-Based Access Control

## 📋 **Overview**

The Authentication Service now supports **two distinct user roles** with role-based access control:

- **👑 Admin**: Full administrative access
- **👁️ Viewer**: View-only access

## 🏗️ **Implementation Details**

### **Database Schema**
```sql
-- Users table with role column
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    full_name VARCHAR(100),
    hashed_password VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'viewer',  -- 'admin' or 'viewer'
    disabled BOOLEAN DEFAULT FALSE
);
```

### **Default Users Created**
The system automatically creates two default users:

| Role | Username | Password | Email | Access Level |
|------|----------|----------|-------|--------------|
| **👑 Admin** | `admin` | `admin123` | <EMAIL> | Full access |
| **👁️ Viewer** | `viewer` | `viewer123` | <EMAIL> | View access |

## 🔧 **Technical Implementation**

### **1. Database Model Updates**
- Added `role` column to `UserDB` model
- Default role is `viewer`
- Migration script handles existing databases

### **2. Authentication Flow**
- JWT tokens now include role information
- Role is validated on each request
- Dashboard displays user role with visual indicators

### **3. API Endpoints Enhanced**
- `/login` - Returns JWT with role information
- `/dashboard` - Shows role-specific UI elements
- `/register` - Allows role selection during registration
- `/validate-token` - Returns user info including role

### **4. Frontend Updates**
- Dashboard shows role badges (👑 Admin / 👁️ Viewer)
- Registration form includes role selection dropdown
- Role-specific styling and indicators

## 🚀 **Usage**

### **Starting the Service**
```bash
cd Authentication
python start.py
```

### **Access URLs**
- **Login**: http://localhost:8001/login
- **Dashboard**: http://localhost:8001/dashboard
- **Health Check**: http://localhost:8001/health

### **Login Credentials**
```
👑 Admin Access:
   Username: admin
   Password: admin123

👁️ Viewer Access:
   Username: viewer
   Password: viewer123
```

## 🔄 **Migration**

If you have an existing database without the role column:

```bash
cd Authentication
python migrate_add_role.py
```

This will:
- Add the `role` column to existing users table
- Set existing admin user role to 'admin'
- Set default role to 'viewer' for new users

## 🎯 **Role Differences**

### **👑 Admin Role**
- Full access to all features
- Can manage system settings
- Administrative privileges
- Golden badge display

### **👁️ Viewer Role**
- View-only access
- Cannot modify system settings
- Limited to viewing data
- Blue badge display

## 🔒 **Security Features**

- **JWT Tokens**: Include role information securely
- **Role Validation**: Server-side role checking
- **Password Hashing**: Bcrypt encryption
- **Session Management**: Secure cookie handling
- **Token Expiration**: Configurable token lifetime

## 📊 **API Response Format**

### **Token Validation Response**
```json
{
    "valid": true,
    "user": {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "full_name": "System Administrator",
        "role": "admin",
        "disabled": false
    }
}
```

### **Dashboard User Object**
```python
user = {
    "username": "admin",
    "email": "<EMAIL>",
    "full_name": "System Administrator",
    "role": "admin"
}
```

## 🎨 **UI Elements**

### **Role Badges**
- **Admin**: Golden badge with crown icon (👑)
- **Viewer**: Blue badge with eye icon (👁️)

### **Registration Form**
- Role selection dropdown
- Default selection: Viewer
- Options: Viewer, Admin

## ✅ **Features Implemented**

✅ **Database Schema**: Role column added to users table  
✅ **Default Users**: Admin and Viewer users created automatically  
✅ **JWT Integration**: Role information included in tokens  
✅ **Dashboard Display**: Role badges and indicators  
✅ **Registration**: Role selection during user creation  
✅ **Migration**: Automatic database migration for existing systems  
✅ **API Endpoints**: All endpoints updated to handle roles  
✅ **Security**: Role-based access control implemented  

## 🔄 **Future Enhancements**

- **Role Permissions**: Granular permission system
- **Role Management**: Admin interface for role assignment
- **Custom Roles**: Support for additional role types
- **Audit Logging**: Track role-based actions
- **Role-Based Routing**: Different UI flows per role

---

## 🎉 **Ready to Use!**

The role-based authentication system is now fully implemented and ready for production use. Both Admin and Viewer roles are supported with appropriate UI indicators and access controls.
