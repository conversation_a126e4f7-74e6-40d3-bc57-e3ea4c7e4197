#!/usr/bin/env python3

from fastapi import <PERSON>AP<PERSON>, Request, Form, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
import uvicorn

print("🚀 Starting Simple Vigilant Eye Authentication Service...")

# Initialize FastAPI app
app = FastAPI(title="Vigilant Eye - Authentication Service", version="1.0.0")

# Setup templates
templates = Jinja2Templates(directory="app/templates")

# Simple in-memory user storage (for testing)
users_db = {
    "admin": {"password": "admin123", "role": "admin", "full_name": "Administrator"},
    "viewer": {"password": "viewer123", "role": "viewer", "full_name": "Viewer User"}
}

# CORS middleware
from fastapi.middleware.cors import CORSMiddleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Home page - redirects to login
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    return RedirectResponse(url="/login", status_code=302)

# Login page
@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

# Login form submission
@app.post("/login", response_class=HTMLResponse)
async def login(request: Request, username: str = Form(...), password: str = Form(...)):
    # Simple authentication check
    if username in users_db and users_db[username]["password"] == password:
        # Successful login - redirect to dashboard
        response = RedirectResponse(url="/dashboard", status_code=302)
        response.set_cookie(key="user", value=username)  # Simple session
        return response
    else:
        # Failed login
        return templates.TemplateResponse(
            "login.html", 
            {"request": request, "error": "Invalid username or password"}
        )

# Dashboard page
@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request):
    # Check if user is logged in
    username = request.cookies.get("user")
    if not username or username not in users_db:
        return RedirectResponse(url="/login", status_code=302)
    
    # Get user info
    user = {
        "username": username,
        "full_name": users_db[username]["full_name"],
        "role": users_db[username]["role"]
    }
    
    return templates.TemplateResponse("dashboard.html", {
        "request": request,
        "user": user
    })

# Registration endpoint
@app.post("/register", response_class=HTMLResponse)
async def register(
    request: Request,
    username: str = Form(...),
    email: str = Form(...),
    full_name: str = Form(...),
    password: str = Form(...),
    role: str = Form("viewer")
):
    # Check if user already exists
    if username in users_db:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Username already exists"}
        )
    
    # Add new user
    users_db[username] = {
        "password": password,
        "role": role,
        "full_name": full_name,
        "email": email
    }
    
    return templates.TemplateResponse(
        "login.html",
        {"request": request, "success": "Account created successfully! Please login."}
    )

# Logout endpoint
@app.get("/logout")
@app.post("/logout")
async def logout():
    response = RedirectResponse(url="/login", status_code=302)
    response.delete_cookie(key="user")
    return response

# AI Module placeholder routes
@app.get("/crowd_detection/crowd_detection")
async def crowd_detection_placeholder(request: Request):
    username = request.cookies.get("user")
    if not username:
        return RedirectResponse(url="/login", status_code=302)
    
    return HTMLResponse("""
    <html>
        <head><title>Crowd Detection</title></head>
        <body style="font-family: Arial, sans-serif; padding: 20px;">
            <h1>🚀 Crowd Detection Module</h1>
            <p>This module is running within the Auth-Service.</p>
            <p>User: <strong>{}</strong></p>
            <p>Auth-Service is running on port 8001.</p>
            <a href="/dashboard" style="color: blue;">← Back to Dashboard</a>
        </body>
    </html>
    """.format(username))

@app.get("/helmet_detection/helmet_detection")
async def helmet_detection_placeholder(request: Request):
    username = request.cookies.get("user")
    if not username:
        return RedirectResponse(url="/login", status_code=302)
    
    return HTMLResponse("""
    <html>
        <head><title>PPE Detection</title></head>
        <body style="font-family: Arial, sans-serif; padding: 20px;">
            <h1>🚀 PPE Detection Module</h1>
            <p>This module is running within the Auth-Service.</p>
            <p>User: <strong>{}</strong></p>
            <p>Auth-Service is running on port 8001.</p>
            <a href="/dashboard" style="color: blue;">← Back to Dashboard</a>
        </body>
    </html>
    """.format(username))

@app.get("/quality_control/quality_control")
async def quality_control_placeholder(request: Request):
    username = request.cookies.get("user")
    if not username:
        return RedirectResponse(url="/login", status_code=302)
    
    return HTMLResponse("""
    <html>
        <head><title>Quality Control</title></head>
        <body style="font-family: Arial, sans-serif; padding: 20px;">
            <h1>🚀 Quality Control Module</h1>
            <p>This module is running within the Auth-Service.</p>
            <p>User: <strong>{}</strong></p>
            <p>Auth-Service is running on port 8001.</p>
            <a href="/dashboard" style="color: blue;">← Back to Dashboard</a>
        </body>
    </html>
    """.format(username))

@app.get("/face_recognition/face_recognition")
async def face_recognition_placeholder(request: Request):
    username = request.cookies.get("user")
    if not username:
        return RedirectResponse(url="/login", status_code=302)
    
    return HTMLResponse("""
    <html>
        <head><title>Face Recognition</title></head>
        <body style="font-family: Arial, sans-serif; padding: 20px;">
            <h1>🚀 Face Recognition Module</h1>
            <p>This module is running within the Auth-Service.</p>
            <p>User: <strong>{}</strong></p>
            <p>Auth-Service is running on port 8001.</p>
            <a href="/dashboard" style="color: blue;">← Back to Dashboard</a>
        </body>
    </html>
    """.format(username))

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "Vigilant Eye Auth Service"}

print("🎉 Simple Authentication service initialization complete!")
print("📡 Auth-Service available at: http://127.0.0.1:8001")
print("👤 Login credentials: admin/admin123 or viewer/viewer123")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)
