from sqlalchemy import create_engine, <PERSON>umn, Integer, <PERSON>, <PERSON><PERSON><PERSON>, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os
import logging

logger = logging.getLogger(__name__)

# Database connection URL - can be configured via environment variables
DATABASE_URL = os.getenv("DATABASE_URL", "mysql+pymysql://root:1234@localhost/vigilanteye")

# Create SQLAlchemy engine
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# User table model
class UserDB(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    full_name = Column(String(100))
    hashed_password = Column(String(255))
    disabled = Column(Bo<PERSON>an, default=False)

# Function to create database if it doesn't exist
def create_database_if_not_exists():
    """Create the database if it doesn't exist"""
    try:
        # Parse the database URL to get connection details
        import urllib.parse
        parsed = urllib.parse.urlparse(DATABASE_URL.replace("mysql+pymysql://", "mysql://"))

        # Extract connection details
        username = parsed.username or "root"
        password = parsed.password or "1234"
        host = parsed.hostname or "localhost"
        port = parsed.port or 3306
        database_name = parsed.path.lstrip('/')

        # Create connection without specifying database
        server_url = f"mysql+pymysql://{username}:{password}@{host}:{port}"
        server_engine = create_engine(server_url)

        # Check if database exists and create if not
        with server_engine.connect() as conn:
            result = conn.execute(text(f"SHOW DATABASES LIKE '{database_name}'"))
            if not result.fetchone():
                logger.info(f"Creating database: {database_name}")
                conn.execute(text(f"CREATE DATABASE {database_name}"))
                conn.commit()
                logger.info(f"Database {database_name} created successfully")
            else:
                logger.info(f"Database {database_name} already exists")

        server_engine.dispose()

    except Exception as e:
        logger.warning(f"Could not auto-create database: {e}")
        logger.warning("Please create the database manually")

# Create tables in the database
def create_tables():
    try:
        # First try to create database if it doesn't exist
        create_database_if_not_exists()
        # Then create tables
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Error creating tables: {e}")
        raise

# Database dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
