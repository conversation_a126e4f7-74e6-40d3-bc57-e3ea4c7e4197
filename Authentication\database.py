from sqlalchemy import create_engine, <PERSON><PERSON><PERSON>, Integer, String, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os
import logging

logger = logging.getLogger(__name__)

# Database connection URL - use the same database as Backend
DATABASE_URL = os.getenv("DATABASE_URL", "mysql+pymysql://root:1234@localhost/vigilanteye")

# Create SQLAlchemy engine
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# User table model
class UserDB(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    full_name = Column(String(100))
    hashed_password = Column(String(255))
    disabled = Column(Boolean, default=False)

# Create tables in the database
def create_tables():
    try:
        # Create tables in existing database
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.warning(f"Could not create tables: {e}")
        logger.warning("Database might not exist or connection failed")
        # Don't raise error, let the service continue

# Database dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
