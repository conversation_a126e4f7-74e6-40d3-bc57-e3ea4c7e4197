<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vigilant Eye - Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            color: white;
            font-size: 1.8rem;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
            color: white;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        .welcome-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            text-align: center;
        }

        .welcome-section h2 {
            color: #333;
            margin-bottom: 1rem;
        }

        .welcome-section p {
            color: #666;
            font-size: 1.1rem;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .service-card {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }

        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .service-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .service-card h3 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .service-card p {
            color: #666;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .service-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 5px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            text-decoration: none;
            display: inline-block;
        }

        .service-btn:hover {
            transform: translateY(-2px);
        }

        .footer {
            text-align: center;
            margin-top: 3rem;
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .header {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
            }

            .container {
                padding: 0 1rem;
            }

            .services-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-eye"></i> Vigilant Eye</h1>
        <div class="user-info">
            <span><i class="fas fa-user"></i> Welcome, {{ user.full_name or user.username }}</span>
            <button class="logout-btn" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i> Logout
            </button>
        </div>
    </div>

    <div class="container">
        <div class="welcome-section">
            <h2>Welcome to Vigilant Eye Dashboard</h2>
            <p>Choose a service below to get started with AI-powered monitoring and detection</p>
        </div>

        <div class="services-grid">
            <div class="service-card" onclick="openService('face_recognition')">
                <div class="service-icon">
                    <i class="fas fa-user-check"></i>
                </div>
                <h3>Face Recognition</h3>
                <p>Advanced facial recognition system for identity verification and access control</p>
                <a href="#" class="service-btn">Launch Service</a>
            </div>

            <div class="service-card" onclick="openService('helmet_detection')">
                <div class="service-icon">
                    <i class="fas fa-hard-hat"></i>
                </div>
                <h3>PPE Detection</h3>
                <p>Monitor workplace safety by detecting personal protective equipment compliance</p>
                <a href="#" class="service-btn">Launch Service</a>
            </div>

            <div class="service-card" onclick="openService('crowd_detection')">
                <div class="service-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h3>Crowd Detection</h3>
                <p>Real-time crowd monitoring and density analysis for public safety</p>
                <a href="#" class="service-btn">Launch Service</a>
            </div>

            <div class="service-card" onclick="openService('quality_control')">
                <div class="service-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h3>Quality Control</h3>
                <p>Automated quality inspection and defect detection for manufacturing</p>
                <a href="#" class="service-btn">Launch Service</a>
            </div>
        </div>

        <div class="footer">
            <p>&copy; 2024 Vigilant Eye - AI-Powered Monitoring Solutions</p>
        </div>
    </div>

    <script>
        function openService(serviceName) {
            // Open the service in a new tab
            const backendUrl = "{{ backend_url }}";
            const serviceUrl = `${backendUrl}/${serviceName}/`;
            window.open(serviceUrl, '_blank');
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                // Simple redirect to logout endpoint
                window.location.href = '/logout';
            }
        }

        // Add click handlers for service cards
        document.querySelectorAll('.service-card').forEach(card => {
            card.addEventListener('click', function(e) {
                if (e.target.tagName !== 'A') {
                    this.querySelector('.service-btn').click();
                }
            });
        });
    </script>
</body>
</html>
