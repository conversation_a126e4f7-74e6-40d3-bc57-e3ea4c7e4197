<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vigilant Eye - Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header h1 {
            color: #4a5568;
            font-size: 1.8rem;
            font-weight: 700;
        }

        .header h1 i {
            color: #667eea;
            margin-right: 0.5rem;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-info span {
            color: #4a5568;
            font-weight: 500;
        }

        .user-info i {
            color: #667eea;
            margin-right: 0.5rem;
        }

        .logout-btn {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .logout-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .welcome-section {
            text-align: center;
            margin-bottom: 3rem;
            color: white;
        }

        .welcome-section h2 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .welcome-section p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .service-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            text-decoration: none;
            color: inherit;
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-decoration: none;
            color: inherit;
        }

        .service-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 1.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }

        .service-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #4a5568;
            font-weight: 700;
        }

        .service-card p {
            color: #718096;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .service-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-block;
        }

        .service-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .footer {
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .header {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
            }

            .container {
                padding: 0 1rem;
            }

            .services-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <h1>
            <i class="fas fa-shield-alt"></i>
            Vigilant Eye
        </h1>
        <div class="user-info">
            <span>
                <i class="fas fa-user"></i>
                Welcome, {{ user.username }}
            </span>
            <a href="/logout" class="logout-btn" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i>
                Logout
            </a>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container">
        <!-- Welcome Section -->
        <section class="welcome-section">
            <h2>System Dashboard</h2>
            <p>Monitor and manage your AI-powered detection systems with real-time insights and comprehensive analytics.</p>
        </section>

        <!-- Services Grid -->
        <section class="services-grid">
            <a href="#" class="service-card" onclick="navigateToModule('crowd')">
                <div class="service-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h3>Crowd Detection</h3>
                <p>Monitor crowd density and movement patterns for safety management and crowd control.</p>
                <span class="service-btn">Launch Module</span>
            </a>

            <a href="#" class="service-card" onclick="navigateToModule('ppe')">
                <div class="service-icon">
                    <i class="fas fa-hard-hat"></i>
                </div>
                <h3>PPE Detection</h3>
                <p>Ensure proper personal protective equipment compliance in workplace environments.</p>
                <span class="service-btn">Launch Module</span>
            </a>

            <a href="#" class="service-card" onclick="navigateToModule('quality')">
                <div class="service-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3>Quality Control</h3>
                <p>Automated quality assurance and defect detection for manufacturing processes.</p>
                <span class="service-btn">Launch Module</span>
            </a>

            <a href="#" class="service-card" onclick="navigateToModule('face')">
                <div class="service-icon">
                    <i class="fas fa-user-check"></i>
                </div>
                <h3>Face Detection</h3>
                <p>Advanced facial recognition and identification system for security applications.</p>
                <span class="service-btn">Launch Module</span>
            </a>
        </section>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; 2024 Vigilant Eye. All rights reserved. | Powered by AI Detection Technology</p>
        </footer>
    </div>

    <script>
        function navigateToModule(moduleName) {
            // Backend service URL
            const backendUrl = "{{ backend_url }}";
            
            // Map module names to backend routes
            const moduleRoutes = {
                'crowd': 'crowd_detection/crowd_detection',
                'ppe': 'helmet_detection/helmet_detection',
                'quality': 'quality_control/quality_control',
                'face': 'face_recognition/face_recognition'
            };
            
            // Get the route for the module
            const route = moduleRoutes[moduleName];
            if (route) {
                const serviceUrl = `${backendUrl}/${route}`;
                window.open(serviceUrl, '_blank');
            }
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                window.location.href = '/logout';
            }
        }

        // Add click handlers for service cards
        document.querySelectorAll('.service-card').forEach(card => {
            card.addEventListener('click', function(e) {
                e.preventDefault();
                const onclick = this.getAttribute('onclick');
                if (onclick) {
                    eval(onclick);
                }
            });
        });
    </script>
</body>
</html>
