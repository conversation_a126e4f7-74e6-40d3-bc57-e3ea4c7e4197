<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vigilant Eye - Professional Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, sans-serif;
            background: #fafbfc;
            min-height: 100vh;
            color: #2c3e50;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            width: 280px;
            background: #ffffff;
            border-right: 1px solid #e8eaed;
            display: flex;
            flex-direction: column;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
            position: fixed;
            height: 100vh;
            overflow: hidden;
        }

        .logo {
            padding: 2rem;
            border-bottom: 1px solid #e8eaed;
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
        }

        .logo h1 {
            font-size: 1.75rem;
            font-weight: 700;
            color: #ffffff;
            text-align: center;
        }

        .nav-menu {
            list-style: none;
            padding: 1.5rem 0;
            flex: 1;
        }

        .nav-item {
            margin-bottom: 0.25rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 2rem;
            text-decoration: none;
            color: #64748b;
            font-weight: 500;
            transition: all 0.2s ease;
            position: relative;
        }

        .nav-link:hover {
            color: #1e3a8a;
            background: #f1f5f9;
        }

        .nav-link.active {
            color: #1e3a8a;
            background: #eff6ff;
            border-right: 3px solid #3b82f6;
            font-weight: 600;
        }

        .nav-link i {
            margin-right: 1rem;
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
        }

        .user-profile {
            padding: 1.5rem 2rem;
            border-top: 1px solid #e8eaed;
            background: #f8fafc;
            display: flex;
            align-items: center;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: linear-gradient(135deg, #1e3a8a, #3b82f6);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            margin-right: 0.75rem;
            font-size: 0.9rem;
        }

        .user-info h4 {
            font-size: 0.9rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 0.25rem;
        }

        .user-info p {
            font-size: 0.75rem;
            color: #64748b;
        }

        /* Main Content */
        .main-content {
            margin-left: 280px;
            flex: 1;
            padding: 2rem;
            background: #fafbfc;
            min-height: 100vh;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            background: #ffffff;
            padding: 1.5rem 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
            border: 1px solid #e8eaed;
        }

        .header h2 {
            font-size: 1.875rem;
            font-weight: 700;
            color: #1e293b;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .search-container {
            position: relative;
        }

        .logout-btn {
            padding: 0.75rem 1.5rem;
            background: #ef4444;
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .logout-btn:hover {
            background: #dc2626;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        }

        .search-input {
            padding: 0.75rem 1rem 0.75rem 2.75rem;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            background: #ffffff;
            font-size: 0.9rem;
            width: 320px;
            transition: all 0.2s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
        }

        /* Overall Results Section */
        .overview-section {
            margin-bottom: 2rem;
        }

        .overview-card {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            padding: 2.5rem;
            border-radius: 16px;
            color: white;
            box-shadow: 0 8px 25px rgba(30, 58, 138, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .overview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .overview-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
        }

        .overview-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .overview-stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 2rem;
        }

        .overview-stat {
            text-align: center;
        }

        .overview-stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .overview-stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        /* Detection Modules Grid */
        .modules-section {
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 1.5rem;
        }

        .modules-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1.5rem;
        }

        .module-card {
            background: #ffffff;
            padding: 2.5rem 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
            border: 1px solid #e8eaed;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            text-align: center;
            text-decoration: none;
            color: inherit;
        }

        .module-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--module-color);
        }

        .module-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
            text-decoration: none;
            color: inherit;
        }

        .module-card.crowd { --module-color: #10b981; }
        .module-card.ppe { --module-color: #f59e0b; }
        .module-card.quality { --module-color: #3b82f6; }
        .module-card.face { --module-color: #8b5cf6; }
        .module-icon {
            width: 70px;
            height: 70px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            margin: 0 auto 1.5rem;
        }

        .module-icon.crowd { background: #10b981; }
        .module-icon.ppe { background: #f59e0b; }
        .module-icon.quality { background: #3b82f6; }
        .module-icon.face { background: #8b5cf6; }

        .module-title {
            font-size: 1.1rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.75rem;
        }

        .module-description {
            font-size: 0.9rem;
            color: #64748b;
            line-height: 1.5;
        }

        .module-status {
            position: absolute;
            top: 1rem;
            right: 1rem;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #10b981;
        }

        @media (max-width: 1200px) {
            .modules-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .overview-stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                position: relative;
                height: auto;
            }

            .main-content {
                margin-left: 0;
                padding: 1rem;
            }

            .modules-grid {
                grid-template-columns: 1fr;
            }

            .overview-stats {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="logo">
                <h1>Vigilant Eye</h1>
            </div>

            <nav>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#" class="nav-link active">
                            <i class="fas fa-home"></i>
                            Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-video"></i>
                            Live Feed
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-chart-line"></i>
                            Analytics
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-bell"></i>
                            Alerts
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-shield-alt"></i>
                            Detection Modules
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-cog"></i>
                            Settings
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-history"></i>
                            History
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="fas fa-question-circle"></i>
                            Help & Support
                        </a>
                    </li>
                </ul>
            </nav>

            <div class="user-profile">
                <div class="user-avatar">{{ (user.full_name or user.username)[:2].upper() }}</div>
                <div class="user-info">
                    <h4>{{ user.full_name or user.username }}</h4>
                    <p>Administrator</p>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <h2>System Dashboard</h2>
                <div class="header-right">
                    <div class="search-container">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="Search alerts, cameras, or locations...">
                    </div>
                    <a href="/logout" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </div>
            </header>

            <!-- Overall Results -->
            <section class="overview-section">
                <div class="overview-card">
                    <div class="overview-header">
                        <h3 class="overview-title">System Overview</h3>
                        <div class="overview-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                    </div>
                    <div class="overview-stats">
                        <div class="overview-stat">
                            <div class="overview-stat-value">24</div>
                            <div class="overview-stat-label">Active Alerts</div>
                        </div>
                        <div class="overview-stat">
                            <div class="overview-stat-value">1,283</div>
                            <div class="overview-stat-label">Total Detections</div>
                        </div>
                        <div class="overview-stat">
                            <div class="overview-stat-value">16</div>
                            <div class="overview-stat-label">Active Cameras</div>
                        </div>
                        <div class="overview-stat">
                            <div class="overview-stat-value">99.8%</div>
                            <div class="overview-stat-label">System Uptime</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Detection Modules -->
            <section class="modules-section">
                <h3 class="section-title">Detection Modules</h3>
                <div class="modules-grid">
                    <a href="#" class="module-card crowd" onclick="navigateToModule('crowd')">
                        <div class="module-status"></div>
                        <div class="module-icon crowd">
                            <i class="fas fa-users"></i>
                        </div>
                        <h4 class="module-title">Crowd Detection</h4>
                        <p class="module-description">Monitor crowd density and movement patterns for safety management</p>
                    </a>

                    <a href="#" class="module-card ppe" onclick="navigateToModule('ppe')">
                        <div class="module-status"></div>
                        <div class="module-icon ppe">
                            <i class="fas fa-hard-hat"></i>
                        </div>
                        <h4 class="module-title">PPE Detection</h4>
                        <p class="module-description">Ensure proper personal protective equipment compliance</p>
                    </a>

                    <a href="#" class="module-card quality" onclick="navigateToModule('quality')">
                        <div class="module-status"></div>
                        <div class="module-icon quality">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h4 class="module-title">Quality Control</h4>
                        <p class="module-description">Automated quality assurance and defect detection</p>
                    </a>

                    <a href="#" class="module-card face" onclick="navigateToModule('face')">
                        <div class="module-status"></div>
                        <div class="module-icon face">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <h4 class="module-title">Face Detection</h4>
                        <p class="module-description">Advanced facial recognition and identification system</p>
                    </a>
                </div>
            </section>
        </main>
    </div>

    <script>
        function navigateToModule(moduleName) {
            // Backend service URL
            const backendUrl = "{{ backend_url }}";

            // Map module names to backend routes
            const moduleRoutes = {
                'crowd': 'crowd_detection/crowd_detection',
                'ppe': 'helmet_detection/helmet_detection',
                'quality': 'quality_control/quality_control',
                'face': 'face_recognition/face_recognition'
            };

            // Get the route for the module
            const route = moduleRoutes[moduleName];
            if (route) {
                const serviceUrl = `${backendUrl}/${route}`;
                window.open(serviceUrl, '_blank');
            }
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                window.location.href = '/logout';
            }
        }

        // Add click handlers for module cards
        document.querySelectorAll('.module-card').forEach(card => {
            card.addEventListener('click', function(e) {
                e.preventDefault();
                const moduleName = this.classList.contains('crowd') ? 'crowd' :
                                 this.classList.contains('ppe') ? 'ppe' :
                                 this.classList.contains('quality') ? 'quality' :
                                 this.classList.contains('face') ? 'face' : '';
                if (moduleName) {
                    navigateToModule(moduleName);
                }
            });
        });
    </script>
</body>
</html>
