from deepface import DeepFace
from scipy.spatial.distance import cosine
import cv2
from mtcnn import MTCNN
import os
import asyncio
from .models import Encoding, User
import math
from keras_facenet import FaceNet
import numpy as np
from pathlib import Path
import json
import time


embedder = FaceNet()
CAMERAS_FILE_PATH = "app/face_recognition/cameras.json"

def generate_encoding(face_image):
        """Generates a face encoding using FaceNet."""
        face_image = cv2.resize(face_image, (160, 160))
        face_image = np.expand_dims(face_image, axis=0)
        return embedder.embeddings(face_image)[0]


def crop_face(image_input):
    """
    Crop the face from an image.

    Args:
        image_input: Either a file path (string) or an image array (numpy.ndarray)

    Returns:
        The cropped face image or None if no face is detected
    """
    # Check if input is a file path or an image array
    if isinstance(image_input, str):
        image = cv2.imread(image_input)
    else:
        image = image_input

    # Initialize the MTCNN detector
    detector = MTCNN()

    # Detect faces
    faces = detector.detect_faces(image)

    # Check if any faces were detected
    if not faces:
        return None

    # Get the first face
    face = faces[0]
    x, y, w, h = face['box']

    # Ensure coordinates are valid (not negative)
    x = max(0, x)
    y = max(0, y)

    # Crop the face
    cropped_face = image[y:y+h, x:x+w]
    return cropped_face

def calculate_cosine_similarity(embedding1, embedding2):
    return 1-cosine(embedding1, embedding2)

def load_cameras():
    if Path(CAMERAS_FILE_PATH).exists():
        with open(CAMERAS_FILE_PATH, "r") as file:
            return json.load(file)
    return {}

# Save cameras data to the file
def save_cameras(cameras_data):
    with open(CAMERAS_FILE_PATH, "w") as file:
        json.dump(cameras_data, file, indent=4)

def get_available_devices():
    print("Fetching available devices...")
    index = 0
    available_devices = []
    while True:
        cap = cv2.VideoCapture(index)
        if not cap.read()[0]:
            break
        else:
            available_devices.append(index)
        cap.release()
        index += 1
    return available_devices
# devices = get_available_devices()

# print(devices)

def get_username_from_id(user_id, db_users_cache):
    """Function to extract the username from the user_id from cached data of user."""
    user = next((user for user in db_users_cache if user.id == user_id), None)
    if user:
        return user.username
    return None



# to save images with a delay mechanism
# This is to prevent saving too many images in a short time

last_saved_time = {}
SAVE_DELAY = 10  # seconds

def save_person_image(face_image, username):
    """Save face image to per-user folder with a delay mechanism."""
    global last_saved_time

    # Validate face image before saving
    if face_image is None or face_image.size == 0:
        print(f"[INFO] Skipped saving invalid image for {username}")
        return False
        
    # Add minimum face size check
    if face_image.shape[0] < 50 or face_image.shape[1] < 50:
        print(f"[INFO] Skipped saving too small face for {username}")
        return False

    current_time = time.time()
    user_folder = os.path.join("Dataset", username)
    os.makedirs(user_folder, exist_ok=True)

    last_time = last_saved_time.get(username, 0)
    if current_time - last_time >= SAVE_DELAY:
        timestamp = int(current_time)
        image_path = os.path.join(user_folder, f"{username}_{timestamp}.jpg")
        cv2.imwrite(image_path, face_image)
        last_saved_time[username] = current_time
        print(f"[INFO] Saved image for {username} at {image_path}")
        return True
    else:
        print(f"[INFO] Skipped saving image for {username} (within delay window)")
        return False



def save_unknown_image(face_image, persistent_id):
    """Save unknown face image with delay based on persistent_id."""
    global last_saved_time

    current_time = time.time()
    folder_path = os.path.join("Dataset", "unknown")
    os.makedirs(folder_path, exist_ok=True)

    # Ensure the face image is valid
    if face_image is None or face_image.size == 0:
        print(f"[ERROR] Invalid face image for {persistent_id}")
        return False

    # Ensure the persistent_id is valid
    if not persistent_id or not isinstance(persistent_id, str):
        print(f"[ERROR] Invalid persistent_id: {persistent_id}")
        return False

    last_time = last_saved_time.get(persistent_id, 0)
    if current_time - last_time >= SAVE_DELAY:
        timestamp = int(current_time)
        image_path = os.path.join(folder_path, f"unknown_{persistent_id}_{timestamp}.jpg")

        try:
            success = cv2.imwrite(image_path, face_image)
            if success:
                last_saved_time[persistent_id] = current_time
                print(f"[INFO] Saved unknown image for {persistent_id} at {image_path}")
                return True
            else:
                print(f"[ERROR] Failed to save image for {persistent_id} at {image_path}")
                return False
        except Exception as e:
            print(f"[ERROR] Exception saving image for {persistent_id}: {str(e)}")
            return False
    else:
        print(f"[INFO] Skipped saving image for {persistent_id} (within delay window)")
        return False
