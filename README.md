# 🔐 Vigilant Eye - Complete Authentication System

A comprehensive AI-powered monitoring system with separate authentication service and multiple detection modules.

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   User Browser  │    │ Authentication  │    │   Backend       │
│                 │    │   Service       │    │   Services      │
│  (Dashboard)    │    │  (Port 8001)    │    │  (Port 8000)    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          │ 1. Login             │ 2. <PERSON><PERSON><PERSON>          │
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                                 ▼
                    ┌─────────────────┐
                    │   MySQL         │
                    │   Database      │
                    │  (vigilanteye)  │
                    └─────────────────┘
```

## 🚀 Quick Start

### **Option 1: Automatic Startup (Recommended)**

```bash
# Run the startup script
.\start_services.bat
```

This will:
- Fix any dependency issues
- Start authentication service (Port 8001)
- Start backend services (Port 8000)
- Show you all the URLs and credentials

### **Option 2: Manual Startup**

**Terminal 1 - Authentication Service:**
```bash
cd authentication
python start.py
```

**Terminal 2 - Backend Services:**
```bash
cd Backend
venv\scripts\activate
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

## 🔑 Default Login

- **URL**: http://localhost:8001
- **Username**: `admin`
- **Password**: `admin123`
- **Email**: `<EMAIL>`

⚠️ **Please change the password after first login!**

## 📋 How to Use

1. **Access the System**: Go to http://localhost:8001
2. **Login**: Use admin/admin123 credentials
3. **Dashboard**: You'll see the main dashboard with 4 services
4. **Choose Service**: Click on any service card:
   - 👤 **Face Recognition**
   - 🦺 **PPE Detection** 
   - 👥 **Crowd Detection**
   - 🔍 **Quality Control**
5. **Service Opens**: Each service opens in a new tab
6. **Logout**: Click logout button when done

## 🛠️ Services

### **Authentication Service (Port 8001)**
- **Login/Logout**: User authentication
- **Dashboard**: Main service selection interface
- **User Management**: Admin user management
- **Session Management**: JWT token handling

### **Backend Services (Port 8000)**
- **Face Recognition**: `/face_recognition/`
- **PPE Detection**: `/helmet_detection/`
- **Crowd Detection**: `/crowd_detection/`
- **Quality Control**: `/quality_control/`

## 📁 Project Structure

```
VigilantEye UI/
├── authentication/              # 🔐 Separate Authentication Service
│   ├── main.py                 # FastAPI authentication app
│   ├── auth.py                 # Authentication logic
│   ├── database.py             # Database models
│   ├── start.py                # Startup script
│   ├── requirements.txt        # Dependencies
│   └── templates/
│       ├── login.html          # Login page
│       └── dashboard.html      # Main dashboard
│
├── Backend/                     # ⚙️ Backend Services (No Auth)
│   ├── app/
│   │   ├── main.py             # Main FastAPI app
│   │   ├── face_recognition/   # Face recognition module
│   │   ├── helmet_detection/   # PPE detection module
│   │   ├── crowd_detection/    # Crowd detection module
│   │   └── quality_control/    # Quality control module
│   └── venv/                   # Virtual environment
│
├── start_services.bat          # 🚀 Complete system startup
└── README.md                   # This file
```

## 🔧 Configuration

### **Database Configuration**
- **Database**: `vigilanteye` (MySQL)
- **Connection**: `mysql+pymysql://root:1234@localhost/vigilanteye`
- **Auto-Setup**: Tables created automatically

### **Service URLs**
- **Authentication**: http://localhost:8001
- **Backend API**: http://localhost:8000
- **Health Checks**: 
  - http://localhost:8001/health
  - http://localhost:8000/health

## 🔒 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Password Hashing**: bcrypt for secure password storage
- **Session Management**: Configurable token expiration
- **CORS Protection**: Proper cross-origin configuration
- **HTTP-Only Cookies**: Secure cookie handling

## 🛠️ Development

### **Authentication Service Development**
```bash
cd authentication
pip install -r requirements.txt
python start.py
```

### **Backend Services Development**
```bash
cd Backend
venv\scripts\activate
uvicorn app.main:app --reload
```

## 📊 API Endpoints

### **Authentication Service (8001)**
- `GET /` - Root (redirects to dashboard)
- `GET /login` - Login page
- `POST /login` - Login form submission
- `GET /dashboard` - Main dashboard
- `GET /logout` - Logout
- `POST /token` - API token endpoint
- `GET /health` - Health check

### **Backend Services (8000)**
- `GET /health` - Health check
- `GET /helmet_detection/*` - PPE detection routes
- `GET /crowd_detection/*` - Crowd detection routes
- `GET /face_recognition/*` - Face recognition routes
- `GET /quality_control/*` - Quality control routes

## 🐛 Troubleshooting

### **Common Issues**

1. **Pydantic Version Error**:
   ```bash
   cd Backend
   .\fix_dependencies.bat
   ```

2. **Database Connection Error**:
   - Make sure MySQL is running
   - Check database credentials in `authentication/database.py`
   - Ensure `vigilanteye` database exists

3. **Port Already in Use**:
   - Check if ports 8000 or 8001 are in use
   - Kill existing processes or change ports

4. **Authentication Service Not Starting**:
   - Check MySQL connection
   - Verify database exists
   - Check Python dependencies

### **Health Checks**
- Authentication: http://localhost:8001/health
- Backend: http://localhost:8000/health

## 🔄 Updates and Maintenance

### **Adding New Users**
1. Login as admin
2. Use the user management endpoints
3. Or add directly to database

### **Changing Default Password**
1. Login with admin/admin123
2. Go to user management
3. Update admin user password

### **Database Backup**
```sql
mysqldump -u root -p vigilanteye > backup.sql
```

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Verify all services are running
3. Check the console logs for errors
4. Ensure database connectivity

---

**🎉 Enjoy using Vigilant Eye - Your AI-Powered Monitoring Solution!**
