#!/usr/bin/env python3

from fastapi import FastAP<PERSON>, Request, Form, HTTPException, Depends
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy import create_engine, Column, Integer, <PERSON>, <PERSON><PERSON><PERSON>, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from passlib.context import CryptContext
from datetime import datetime
import uvicorn

print("🚀 Starting Vigilant Eye Authentication Service...")

# Database setup
DATABASE_URL = "mysql+pymysql://root:1234@localhost/surveillance_system"
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def get_password_hash(password):
    return pwd_context.hash(password)

def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

# User model
class User(Base):
    __tablename__ = "users"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    full_name = Column(String(100))
    hashed_password = Column(String(255))
    role = Column(String(20), default="viewer")
    disabled = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# Database dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Initialize FastAPI app
app = FastAPI(title="Vigilant Eye - Authentication Service", version="1.0.0")

# Setup templates
templates = Jinja2Templates(directory="app/templates")

# CORS middleware
from fastapi.middleware.cors import CORSMiddleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Database functions
def get_user(db: Session, username: str):
    return db.query(User).filter(User.username == username).first()

def get_user_by_email(db: Session, email: str):
    return db.query(User).filter(User.email == email).first()

def create_user(db: Session, username: str, password: str, email: str, full_name: str, role: str = "viewer"):
    hashed_password = get_password_hash(password)
    db_user = User(
        username=username,
        email=email,
        full_name=full_name,
        hashed_password=hashed_password,
        role=role
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

def authenticate_user(db: Session, username: str, password: str):
    user = get_user(db, username)
    if not user or not verify_password(password, user.hashed_password):
        return False
    return user

# Initialize database
def init_database():
    try:
        Base.metadata.create_all(bind=engine)
        print("✅ Database tables created")
        
        # Create default users
        db = SessionLocal()
        
        # Admin user
        admin_user = get_user(db, "admin")
        if not admin_user:
            create_user(db, "admin", "admin123", "<EMAIL>", "Administrator", "admin")
            print("✅ Default admin user created (admin/admin123)")
        
        # Viewer user
        viewer_user = get_user(db, "viewer")
        if not viewer_user:
            create_user(db, "viewer", "viewer123", "<EMAIL>", "Viewer User", "viewer")
            print("✅ Default viewer user created (viewer/viewer123)")
        
        db.close()
        
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")

# Startup event
@app.on_event("startup")
async def startup_event():
    init_database()

# Helper function
def check_auth_cookie(request: Request, db: Session):
    username = request.cookies.get("user")
    if username:
        user = get_user(db, username)
        if user:
            return {
                "username": user.username,
                "role": user.role,
                "full_name": user.full_name,
                "email": user.email
            }
    return None

# Routes
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    return RedirectResponse(url="/login", status_code=302)

@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

@app.post("/login", response_class=HTMLResponse)
async def login(request: Request, username: str = Form(...), password: str = Form(...), db: Session = Depends(get_db)):
    user = authenticate_user(db, username, password)
    if not user:
        return templates.TemplateResponse(
            "login.html", 
            {"request": request, "error": "Invalid username or password"}
        )
    
    response = RedirectResponse(url="/dashboard", status_code=302)
    response.set_cookie(key="user", value=username)
    return response

@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request, db: Session = Depends(get_db)):
    user = check_auth_cookie(request, db)
    if not user:
        return RedirectResponse(url="/login", status_code=302)
    
    return templates.TemplateResponse("dashboard.html", {
        "request": request,
        "user": user
    })

@app.get("/logout")
@app.post("/logout")
async def logout():
    response = RedirectResponse(url="/login", status_code=302)
    response.delete_cookie(key="user")
    return response

@app.post("/register", response_class=HTMLResponse)
async def register(
    request: Request,
    username: str = Form(...),
    email: str = Form(...),
    full_name: str = Form(...),
    password: str = Form(...),
    role: str = Form("viewer"),
    db: Session = Depends(get_db)
):
    # Check existing users
    existing_user = get_user(db, username)
    existing_email = get_user_by_email(db, email)
    
    # Handle validation cases as requested
    if existing_email and existing_user:
        # Case 2: Both exist
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "already existed email and username"}
        )
    elif existing_email:
        # Case 1: Email exists
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "already existed email and go to login page"}
        )
    elif existing_user:
        # Case 3: Username exists
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "already existed username"}
        )
    
    # Create new user
    try:
        create_user(db, username, password, email, full_name, role)
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "success": "Account created successfully! Please login."}
        )
    except Exception as e:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": f"Registration failed: {str(e)}"}
        )

# AI Module routes - redirect to Backend
@app.get("/crowd_detection/crowd_detection")
async def crowd_detection_module(request: Request, db: Session = Depends(get_db)):
    user = check_auth_cookie(request, db)
    if not user:
        return RedirectResponse(url="/login", status_code=302)
    return RedirectResponse(url="http://127.0.0.1:8000/crowd_detection/crowd_detection", status_code=302)

@app.get("/helmet_detection/helmet_detection")
async def helmet_detection_module(request: Request, db: Session = Depends(get_db)):
    user = check_auth_cookie(request, db)
    if not user:
        return RedirectResponse(url="/login", status_code=302)
    return RedirectResponse(url="http://127.0.0.1:8000/helmet_detection/helmet_detection", status_code=302)

@app.get("/quality_control/quality_control")
async def quality_control_module(request: Request, db: Session = Depends(get_db)):
    user = check_auth_cookie(request, db)
    if not user:
        return RedirectResponse(url="/login", status_code=302)
    return RedirectResponse(url="http://127.0.0.1:8000/quality_control/quality_control", status_code=302)

@app.get("/face_recognition/face_recognition")
async def face_recognition_module(request: Request, db: Session = Depends(get_db)):
    user = check_auth_cookie(request, db)
    if not user:
        return RedirectResponse(url="/login", status_code=302)
    return RedirectResponse(url="http://127.0.0.1:8000/face_recognition/face_recognition", status_code=302)

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "Vigilant Eye Auth Service"}

print("🎉 Authentication service initialization complete!")
print("📡 Auth-Service available at: http://127.0.0.1:8001")
print("🔗 AI Modules redirect to Backend services (port 8000)")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)
