import json
import os
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import datetime, time
import threading
import time as timer
from pathlib import Path
import logging
import queue

# Path to the alert settings file
ALERT_SETTINGS_FILE = "app/helmet_detection/ppe_alert_settings.json"
ALERT_LOG_FILE = "app/helmet_detection/ppe_alert_log.json"

# PPE Alert system class
class PPEAlertSystem:
    def __init__(self):
        # First, initialize empty settings
        self.settings = {}
        # Then load settings from file
        self.settings = self.load_settings()
        # Now set active flag based on settings
        self.active = self.settings.get("active", False)
        # Initialize other attributes
        self.alert_thread = None
        self.stop_event = threading.Event()
        self.last_alert_time = {} # Dictionary to track last alert time for each camera and violation type
        
        # Create a queue for email sending
        self.email_queue = queue.Queue()
        # Start a worker thread for email sending
        self.email_worker = threading.Thread(target=self._email_worker, daemon=True)
        self.email_worker.start()

        # Start monitoring if active in settings
        if self.active:
            self.start()

    def load_settings(self):
        """Load alert settings from file"""
        try:
            if Path(ALERT_SETTINGS_FILE).exists():
                with open(ALERT_SETTINGS_FILE, "r") as file:
                    content = file.read().strip()
                    if content:  # Check if file is not empty
                        settings = json.loads(content)
                        return settings
            # Return default settings if file doesn't exist or is empty
            default_settings = {
                "active": False,
                "days": [],
                "startTime": "",
                "endTime": "",
                "alertFrequency": 5,  # Default 5 minutes
                "recipients": []
            }
            return default_settings
        except json.JSONDecodeError as e:
            # If there's an error, return default settings
            return {
                "active": False,
                "days": [],
                "startTime": "",
                "endTime": "",
                "alertFrequency": 5,
                "recipients": []
            }
    
    def save_settings(self, settings):
        """Save alert settings to file"""
        os.makedirs(os.path.dirname(ALERT_SETTINGS_FILE), exist_ok=True)
        with open(ALERT_SETTINGS_FILE, "w") as file:
            json.dump(settings, file, indent=4)
        self.settings = settings
        # Update active state based on new settings
        self.active = settings.get("active", False)
    
    def start(self):
        """Start the alert system"""
        if not self.active:
            self.active = True
            self.stop_event.clear()
            self.settings["active"] = True
            self.save_settings(self.settings)
            return True
        return False
    
    def stop(self):
        """Stop the alert system"""
        if self.active:
            self.active = False
            self.settings["active"] = False
            self.save_settings(self.settings)
            self.stop_event.set()
            return True
        return False
    
    def _should_check_alerts(self):
        """Check if alerts should be active based on day and time settings"""
        if not self.active:
            return False
            
        # Check day of the week
        now = datetime.now()
        current_day = now.strftime("%A")  # Monday, Tuesday, etc.
        
        # Check time range if set
        if self.settings["startTime"] and self.settings["endTime"]:
            try:
                start_time = datetime.strptime(self.settings["startTime"], "%H:%M").time()
                end_time = datetime.strptime(self.settings["endTime"], "%H:%M").time()
                current_time = now.time()
         
                # Handle cases including overnight monitoring
                if start_time <= end_time:
                    if not (start_time <= current_time <= end_time):
                        return False
                else:  # e.g., 22:00 - 06:00
                    if not (current_time >= start_time or current_time <= end_time):
                        return False
            except ValueError as e:
                return False
        
        return True
    
    def check_and_alert(self, camera_name, violation_type, count):
        """Check if alert should be sent and send it if needed"""
        # Don't alert if count is zero or negative
        if count <= 0:
            return False
            
        # Only continue if alert conditions are met
        if not self._should_check_alerts():
            return False
            
        # Check if we've already sent an alert recently for this camera/violation
        camera_violation_key = f"{camera_name}_{violation_type}"
        current_time = timer.time()
        
        if camera_violation_key in self.last_alert_time:
            # Check if enough time has passed since the last alert
            elapsed_minutes = (current_time - self.last_alert_time[camera_violation_key]) / 60
            if elapsed_minutes < self.settings["alertFrequency"]:
                return False

        # Queue the alert for sending in background thread
        self.email_queue.put((camera_name, violation_type, count))

        # Update last alert time
        self.last_alert_time[camera_violation_key] = current_time
        # Log alert
        self._log_alert(camera_name, violation_type, count)
        return True

    def _email_worker(self):
        """Worker thread to process email queue"""
        while True:
            try:
                # Get item from queue, with a timeout
                try:
                    camera_name, violation_type, count = self.email_queue.get(timeout=1)
                except queue.Empty:
                    # Check if we should exit
                    if self.stop_event.is_set():
                        break
                    continue
                
                # Send the email
                success = self._send_alert(camera_name, violation_type, count)
                
                # Mark task as done
                self.email_queue.task_done()
                
            except Exception as e:
                # Sleep briefly to avoid spinning if there's a persistent error
                timer.sleep(1)

    def _send_alert(self, camera_name, violation_type, count):
        """Send alert email"""
        if not self.settings["recipients"]:
            return False
            
        try:
            # Email settings - should be configured in production
            smtp_server = "smtp.gmail.com"
            port = 587
            sender_email = "<EMAIL>"  # Update this
            password = "lljb owqm pyrj pwnq"  # Update this - Use app password for Gmail
            
            # Create message
            message = MIMEMultipart("alternative")
            message["Subject"] = f"PPE Safety Alert: {violation_type} Detected - {camera_name}"
            message["From"] = sender_email
            
            # Email content
            html = f"""
            <html>
              <body>
                <h2>PPE Safety Alert</h2>
                <p>This is an automated alert from your PPE Monitoring System.</p>
                <p><strong>Alert Details:</strong></p>
                <ul>
                  <li><strong>Camera:</strong> {camera_name}</li>
                  <li><strong>Violation Type:</strong> {violation_type}</li>
                  <li><strong>People Count:</strong> {count}</li>
                  <li><strong>Time:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</li>
                </ul>
                <p>Please check your monitoring system for more details.</p>
              </body>
            </html>
            """
            
            # Attach HTML content
            part = MIMEText(html, "html")
            message.attach(part)
            
            # Create a secure SSL context
            context = ssl.create_default_context()
            
            # Connect to server
            with smtplib.SMTP(smtp_server, port) as server:
                server.starttls(context=context)
                server.login(sender_email, password)
                for recipient in self.settings["recipients"]:
                    message["To"] = recipient
                    server.sendmail(sender_email, recipient, message.as_string())
            
            return True
            
        except Exception as e:
            return False
    
    def _log_alert(self, camera_name, violation_type, count):
        """Log alert to file"""
        try:
            logs = []
            if Path(ALERT_LOG_FILE).exists():
                with open(ALERT_LOG_FILE, "r") as file:
                    logs = json.load(file)
            
            logs.append({
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "camera": camera_name,
                "violation_type": violation_type,
                "count": count
            })
            
            # Keep only the last 1000 logs
            if len(logs) > 1000:
                logs = logs[-1000:]
                
            with open(ALERT_LOG_FILE, "w") as file:
                json.dump(logs, file, indent=4)
                
        except Exception as e:
            pass

    def cleanup(self):
        """Cleanup resources when shutting down"""
        self.stop_event.set()
        if self.email_worker and self.email_worker.is_alive():
            self.email_worker.join(timeout=5)
