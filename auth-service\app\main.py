from fastapi import <PERSON><PERSON><PERSON>, Request, Form, HTTPException, status, Depends
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jin<PERSON>2Templates
from sqlalchemy.orm import Session
import uvicorn

# Import database and authentication components
from app.database import get_db, init_database
from app.auth import authenticate_user, create_access_token, get_user, get_user_by_email, create_user, decode_access_token
from app.utils import get_password_hash

print("🚀 Starting Vigilant Eye Authentication Service...")

# Initialize FastAPI app
app = FastAPI(title="Vigilant Eye - Authentication & Backend System", version="1.0.0")

# Setup templates
templates = Jinja2Templates(directory="app/templates")

# CORS middleware
from fastapi.middleware.cors import CORSMiddleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize database on startup
@app.on_event("startup")
async def startup_event():
    init_database()

print("✅ Authentication service initialized!")
print("👤 Default users: admin/admin123, viewer/viewer123")

# Helper function to check authentication from cookie
def check_auth_cookie(request: Request, db: Session):
    """Check authentication from cookie"""
    token = request.cookies.get("access_token")
    if not token:
        return None

    if token.startswith("Bearer "):
        token = token[7:]

    try:
        username = decode_access_token(token)
        if username:
            user = get_user(db, username)
            if user:
                return {
                    "username": user.username,
                    "role": user.role if hasattr(user, 'role') else "viewer",
                    "full_name": user.full_name or user.username,
                    "email": user.email
                }
    except:
        pass
    return None

# Home page - redirects to login
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    return RedirectResponse(url="/login", status_code=302)

# Login page
@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

# Login form submission
@app.post("/login", response_class=HTMLResponse)
async def login(request: Request, username: str = Form(...), password: str = Form(...), db: Session = Depends(get_db)):
    # Authenticate user with database
    user = authenticate_user(db, username, password)
    if not user:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Invalid username or password"}
        )

    # Create access token
    access_token = create_access_token(data={"sub": user.username})

    # Successful login - redirect to dashboard
    response = RedirectResponse(url="/dashboard", status_code=302)
    response.set_cookie(
        key="access_token",
        value=f"Bearer {access_token}",
        httponly=True,
        max_age=1800,  # 30 minutes
    )
    return response

# Dashboard page
@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request, db: Session = Depends(get_db)):
    # Check authentication
    user = check_auth_cookie(request, db)
    if not user:
        return RedirectResponse(url="/login", status_code=302)

    return templates.TemplateResponse("dashboard.html", {
        "request": request,
        "user": user
    })

# Logout endpoint
@app.get("/logout")
@app.post("/logout")
async def logout():
    response = RedirectResponse(url="/login", status_code=302)
    response.delete_cookie(key="access_token")
    return response

# Registration endpoint with specific validation cases
@app.post("/register", response_class=HTMLResponse)
async def register(
    request: Request,
    username: str = Form(...),
    email: str = Form(...),
    full_name: str = Form(...),
    password: str = Form(...),
    role: str = Form("viewer"),
    db: Session = Depends(get_db)
):
    # Check if username already exists
    existing_user = get_user(db, username)

    # Check if email already exists
    existing_email = get_user_by_email(db, email)

    # Handle different validation scenarios as requested
    if existing_email and existing_user:
        # Case 2: Both email and username exist
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "already existed email and username"}
        )
    elif existing_email:
        # Case 1: Only email exists - show error and redirect to login
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "already existed email and go to login page"}
        )
    elif existing_user:
        # Case 3: Only username exists
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "already existed username"}
        )

    # If both username and email are available, create the user
    try:
        new_user = create_user(db, username, password, email, full_name)

        # Set role if provided
        if hasattr(new_user, 'role'):
            new_user.role = role
            db.commit()

        return templates.TemplateResponse(
            "login.html",
            {"request": request, "success": "Account created successfully! Please login."}
        )
    except Exception as e:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": f"Registration failed: {str(e)}"}
        )

# AI Module routes with authentication check - Navigate to actual Backend modules
@app.get("/crowd_detection/crowd_detection")
async def crowd_detection_module(request: Request, db: Session = Depends(get_db)):
    user = check_auth_cookie(request, db)
    if not user:
        return RedirectResponse(url="/login", status_code=302)

    # Redirect to Backend crowd detection module
    return RedirectResponse(url="http://127.0.0.1:8000/crowd_detection/crowd_detection", status_code=302)

@app.get("/helmet_detection/helmet_detection")
async def helmet_detection_module(request: Request, db: Session = Depends(get_db)):
    user = check_auth_cookie(request, db)
    if not user:
        return RedirectResponse(url="/login", status_code=302)

    # Redirect to Backend helmet detection module
    return RedirectResponse(url="http://127.0.0.1:8000/helmet_detection/helmet_detection", status_code=302)

@app.get("/quality_control/quality_control")
async def quality_control_module(request: Request, db: Session = Depends(get_db)):
    user = check_auth_cookie(request, db)
    if not user:
        return RedirectResponse(url="/login", status_code=302)

    # Redirect to Backend quality control module
    return RedirectResponse(url="http://127.0.0.1:8000/quality_control/quality_control", status_code=302)

@app.get("/face_recognition/face_recognition")
async def face_recognition_module(request: Request, db: Session = Depends(get_db)):
    user = check_auth_cookie(request, db)
    if not user:
        return RedirectResponse(url="/login", status_code=302)

    # Redirect to Backend face recognition module
    return RedirectResponse(url="http://127.0.0.1:8000/face_recognition/face_recognition", status_code=302)

# Simple Authentication API Routes
@app.get("/auth/users/me")
async def read_users_me():
    """Get current user information"""
    return {"message": "User info endpoint", "service": "auth-service"}

@app.post("/auth/verify-token")
async def verify_token(token: str):
    """Verify token (simplified)"""
    return {"message": "Token verification endpoint", "service": "auth-service"}

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "Vigilant Eye Auth & Backend System"}

print("🎉 Authentication service initialization complete!")
print("📡 Auth-Service available at: http://127.0.0.1:8000")
print("🔗 AI Modules redirect to Backend services")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
