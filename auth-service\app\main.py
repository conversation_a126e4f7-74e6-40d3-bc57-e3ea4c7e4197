from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, HTTPException, status
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.security import <PERSON>A<PERSON>2<PERSON><PERSON><PERSON><PERSON>earer
from sqlalchemy.orm import Session
import uvicorn

# Import authentication components
from app.database import get_db, init_database
from app.routes import router as auth_router

print("🚀 Starting Vigilant Eye Authentication Service...")

# Initialize FastAPI app
app = FastAPI(title="Vigilant Eye - Authentication & Backend System", version="1.0.0")

# Setup templates
templates = Jinja2Templates(directory="app/templates")

# Include authentication routes
app.include_router(auth_router, prefix="/auth", tags=["Authentication"])

# CORS middleware
from fastapi.middleware.cors import CORSMiddleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize database on startup
@app.on_event("startup")
async def startup_event():
    init_database()

print("✅ Authentication service initialized!")
print("👤 Default users: admin/admin123, viewer/viewer123")

# Helper function to check authentication from cookie
def check_auth_cookie(request: Request, db: Session = Depends(get_db)):
    """Check authentication from cookie"""
    token = request.cookies.get("access_token")
    if not token:
        return None

    if token.startswith("Bearer "):
        token = token[7:]

    try:
        from app.auth import decode_access_token, get_user
        username = decode_access_token(token)
        if username:
            user = get_user(db, username)
            return user
    except:
        pass
    return None

# Home page - redirects to login
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    return RedirectResponse(url="/login", status_code=302)

# Login page
@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

# Login form submission
@app.post("/login", response_class=HTMLResponse)
async def login(request: Request, username: str = Form(...), password: str = Form(...), db: Session = Depends(get_db)):
    from app.auth import authenticate_user, create_access_token

    # Authenticate user
    user = authenticate_user(db, username, password)
    if not user:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Invalid username or password"}
        )

    # Create access token
    access_token = create_access_token(data={"sub": user.username})

    # Successful login - redirect to dashboard
    response = RedirectResponse(url="/dashboard", status_code=302)
    response.set_cookie(
        key="access_token",
        value=f"Bearer {access_token}",
        httponly=True,
        max_age=1800,  # 30 minutes
    )
    return response

# Dashboard page
@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request, db: Session = Depends(get_db)):
    # Check authentication
    user = check_auth_cookie(request, db)
    if not user:
        return RedirectResponse(url="/login", status_code=302)

    # Get user roles
    user_roles = [role.name for role in user.roles] if user.roles else ["viewer"]
    primary_role = user_roles[0] if user_roles else "viewer"

    # Create user object for template
    user_data = {
        "username": user.username,
        "full_name": user.full_name or user.username,
        "role": primary_role,
        "email": user.email
    }

    return templates.TemplateResponse("dashboard.html", {
        "request": request,
        "user": user_data
    })

# Logout endpoint
@app.get("/logout")
@app.post("/logout")
async def logout():
    response = RedirectResponse(url="/login", status_code=302)
    response.delete_cookie(key="access_token")
    return response

# Registration endpoint
@app.post("/register", response_class=HTMLResponse)
async def register(
    request: Request,
    username: str = Form(...),
    email: str = Form(...),
    full_name: str = Form(...),
    password: str = Form(...),
    role: str = Form("viewer"),
    db: Session = Depends(get_db)
):
    from app.auth import get_user, get_user_by_email, create_user
    from app.models import Role

    # Check if username already exists
    existing_user = get_user(db, username)
    if existing_user:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Username already exists"}
        )

    # Check if email already exists
    existing_email = get_user_by_email(db, email)
    if existing_email:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Email already exists"}
        )

    # Create new user
    try:
        new_user = create_user(db, username, password, email, full_name)

        # Assign role
        user_role = db.query(Role).filter(Role.name == role).first()
        if user_role and user_role not in new_user.roles:
            new_user.roles.append(user_role)
            db.commit()

        return templates.TemplateResponse(
            "login.html",
            {"request": request, "success": "Account created successfully! Please login."}
        )
    except Exception as e:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": f"Registration failed: {str(e)}"}
        )

# AI Module routes with authentication check
@app.get("/crowd_detection/crowd_detection")
async def crowd_detection_module(request: Request, db: Session = Depends(get_db)):
    user = check_auth_cookie(request, db)
    if not user:
        return RedirectResponse(url="/login", status_code=302)

    return HTMLResponse("""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Crowd Detection - Vigilant Eye</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #eee; }
            .title { color: #333; margin: 0; }
            .nav-btn { background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; transition: background 0.3s; }
            .nav-btn:hover { background: #5a6fd8; }
            .module-content { text-align: center; padding: 50px 0; }
            .status { background: #e8f5e8; color: #2d5a2d; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1 class="title">🚀 Crowd Detection Module</h1>
                <a href="/dashboard" class="nav-btn">← Back to Dashboard</a>
            </div>
            <div class="module-content">
                <h2>Crowd Detection System</h2>
                <div class="status">✅ Module is running within Auth-Service</div>
                <p>Real-time crowd monitoring and density analysis with intelligent region-of-interest detection.</p>
                <p><strong>User:</strong> """ + user.username + """</p>
                <p><strong>Service:</strong> Auth-Service (Port 8001)</p>
                <p><em>This is a placeholder - actual AI functionality can be integrated here.</em></p>
            </div>
        </div>
    </body>
    </html>
    """)

@app.get("/helmet_detection/helmet_detection")
async def helmet_detection_module(request: Request, db: Session = Depends(get_db)):
    user = check_auth_cookie(request, db)
    if not user:
        return RedirectResponse(url="/login", status_code=302)

    return HTMLResponse("""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>PPE Detection - Vigilant Eye</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #eee; }
            .title { color: #333; margin: 0; }
            .nav-btn { background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; transition: background 0.3s; }
            .nav-btn:hover { background: #5a6fd8; }
            .module-content { text-align: center; padding: 50px 0; }
            .status { background: #e8f5e8; color: #2d5a2d; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1 class="title">🚀 PPE Detection Module</h1>
                <a href="/dashboard" class="nav-btn">← Back to Dashboard</a>
            </div>
            <div class="module-content">
                <h2>Personal Protective Equipment Detection</h2>
                <div class="status">✅ Module is running within Auth-Service</div>
                <p>Ensure proper personal protective equipment compliance with automated safety monitoring.</p>
                <p><strong>User:</strong> """ + user.username + """</p>
                <p><strong>Service:</strong> Auth-Service (Port 8001)</p>
                <p><em>This is a placeholder - actual AI functionality can be integrated here.</em></p>
            </div>
        </div>
    </body>
    </html>
    """)

@app.get("/quality_control/quality_control")
async def quality_control_module(request: Request, db: Session = Depends(get_db)):
    user = check_auth_cookie(request, db)
    if not user:
        return RedirectResponse(url="/login", status_code=302)

    return HTMLResponse("""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Quality Control - Vigilant Eye</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #eee; }
            .title { color: #333; margin: 0; }
            .nav-btn { background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; transition: background 0.3s; }
            .nav-btn:hover { background: #5a6fd8; }
            .module-content { text-align: center; padding: 50px 0; }
            .status { background: #e8f5e8; color: #2d5a2d; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1 class="title">🚀 Quality Control Module</h1>
                <a href="/dashboard" class="nav-btn">← Back to Dashboard</a>
            </div>
            <div class="module-content">
                <h2>Automated Quality Assurance</h2>
                <div class="status">✅ Module is running within Auth-Service</div>
                <p>Automated quality assurance and defect detection for manufacturing processes.</p>
                <p><strong>User:</strong> """ + user.username + """</p>
                <p><strong>Service:</strong> Auth-Service (Port 8001)</p>
                <p><em>This is a placeholder - actual AI functionality can be integrated here.</em></p>
            </div>
        </div>
    </body>
    </html>
    """)

@app.get("/face_recognition/face_recognition")
async def face_recognition_module(request: Request, db: Session = Depends(get_db)):
    user = check_auth_cookie(request, db)
    if not user:
        return RedirectResponse(url="/login", status_code=302)

    return HTMLResponse("""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Face Recognition - Vigilant Eye</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #eee; }
            .title { color: #333; margin: 0; }
            .nav-btn { background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; transition: background 0.3s; }
            .nav-btn:hover { background: #5a6fd8; }
            .module-content { text-align: center; padding: 50px 0; }
            .status { background: #e8f5e8; color: #2d5a2d; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1 class="title">🚀 Face Recognition Module</h1>
                <a href="/dashboard" class="nav-btn">← Back to Dashboard</a>
            </div>
            <div class="module-content">
                <h2>Advanced Facial Recognition System</h2>
                <div class="status">✅ Module is running within Auth-Service</div>
                <p>Advanced facial recognition and identification system for security applications.</p>
                <p><strong>User:</strong> """ + user.username + """</p>
                <p><strong>Service:</strong> Auth-Service (Port 8001)</p>
                <p><em>This is a placeholder - actual AI functionality can be integrated here.</em></p>
            </div>
        </div>
    </body>
    </html>
    """)

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "Vigilant Eye Auth & Backend System"}

print("🎉 Authentication service initialization complete!")
print("📡 Auth-Service available at: http://127.0.0.1:8001")
print("🔗 AI Modules integrated within auth-service")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)
