from fastapi import <PERSON><PERSON><PERSON>, Request, Form, HTTPException, status
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import <PERSON><PERSON>2<PERSON>emplates
import uvicorn

print("🚀 Starting Vigilant Eye Authentication Service...")

# Initialize FastAPI app
app = Fast<PERSON>I(title="Vigilant Eye - Authentication & Backend System", version="1.0.0")

# Setup templates
templates = Jinja2Templates(directory="app/templates")

# Simple in-memory user storage (for testing)
users_db = {
    "admin": {"password": "admin123", "role": "admin", "full_name": "Administrator", "email": "<EMAIL>"},
    "viewer": {"password": "viewer123", "role": "viewer", "full_name": "Viewer User", "email": "<EMAIL>"}
}

# CORS middleware
from fastapi.middleware.cors import CORSMiddleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

print("✅ Authentication service initialized!")
print("👤 Default users: admin/admin123, viewer/viewer123")

# Helper function to check authentication from cookie
def check_auth_cookie(request: Request):
    """Check authentication from cookie"""
    username = request.cookies.get("user")
    if username and username in users_db:
        return {"username": username, "role": users_db[username]["role"], "full_name": users_db[username]["full_name"]}
    return None

# Home page - redirects to login
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    return RedirectResponse(url="/login", status_code=302)

# Login page
@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

# Login form submission
@app.post("/login", response_class=HTMLResponse)
async def login(request: Request, username: str = Form(...), password: str = Form(...)):
    # Simple authentication check
    if username in users_db and users_db[username]["password"] == password:
        # Successful login - redirect to dashboard
        response = RedirectResponse(url="/dashboard", status_code=302)
        response.set_cookie(key="user", value=username)  # Simple session
        return response
    else:
        # Failed login
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Invalid username or password"}
        )

# Dashboard page
@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request):
    # Check authentication
    user = check_auth_cookie(request)
    if not user:
        return RedirectResponse(url="/login", status_code=302)

    return templates.TemplateResponse("dashboard.html", {
        "request": request,
        "user": user
    })

# Logout endpoint
@app.get("/logout")
@app.post("/logout")
async def logout():
    response = RedirectResponse(url="/login", status_code=302)
    response.delete_cookie(key="user")
    return response

# Registration endpoint
@app.post("/register", response_class=HTMLResponse)
async def register(
    request: Request,
    username: str = Form(...),
    email: str = Form(...),
    full_name: str = Form(...),
    password: str = Form(...),
    role: str = Form("viewer")
):
    # Check if user already exists
    if username in users_db:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Username already exists"}
        )

    # Add new user
    users_db[username] = {
        "password": password,
        "role": role,
        "full_name": full_name,
        "email": email
    }

    return templates.TemplateResponse(
        "login.html",
        {"request": request, "success": "Account created successfully! Please login."}
    )

# AI Module routes with authentication check
@app.get("/crowd_detection/crowd_detection")
async def crowd_detection_module(request: Request):
    user = check_auth_cookie(request)
    if not user:
        return RedirectResponse(url="/login", status_code=302)

    return HTMLResponse("""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Crowd Detection - Vigilant Eye</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #eee; }
            .title { color: #333; margin: 0; }
            .nav-btn { background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; transition: background 0.3s; }
            .nav-btn:hover { background: #5a6fd8; }
            .module-content { text-align: center; padding: 50px 0; }
            .status { background: #e8f5e8; color: #2d5a2d; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1 class="title">🚀 Crowd Detection Module</h1>
                <a href="/dashboard" class="nav-btn">← Back to Dashboard</a>
            </div>
            <div class="module-content">
                <h2>Crowd Detection System</h2>
                <div class="status">✅ Module is running within Auth-Service</div>
                <p>Real-time crowd monitoring and density analysis with intelligent region-of-interest detection.</p>
                <p><strong>User:</strong> """ + user["username"] + """</p>
                <p><strong>Service:</strong> Auth-Service (Port 8000)</p>
                <p><em>This is a placeholder - actual AI functionality can be integrated here.</em></p>
            </div>
        </div>
    </body>
    </html>
    """)

@app.get("/helmet_detection/helmet_detection")
async def helmet_detection_module(request: Request):
    user = check_auth_cookie(request)
    if not user:
        return RedirectResponse(url="/login", status_code=302)

    return HTMLResponse("""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>PPE Detection - Vigilant Eye</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #eee; }
            .title { color: #333; margin: 0; }
            .nav-btn { background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; transition: background 0.3s; }
            .nav-btn:hover { background: #5a6fd8; }
            .module-content { text-align: center; padding: 50px 0; }
            .status { background: #e8f5e8; color: #2d5a2d; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1 class="title">🚀 PPE Detection Module</h1>
                <a href="/dashboard" class="nav-btn">← Back to Dashboard</a>
            </div>
            <div class="module-content">
                <h2>Personal Protective Equipment Detection</h2>
                <div class="status">✅ Module is running within Auth-Service</div>
                <p>Ensure proper personal protective equipment compliance with automated safety monitoring.</p>
                <p><strong>User:</strong> """ + user["username"] + """</p>
                <p><strong>Service:</strong> Auth-Service (Port 8000)</p>
                <p><em>This is a placeholder - actual AI functionality can be integrated here.</em></p>
            </div>
        </div>
    </body>
    </html>
    """)

@app.get("/quality_control/quality_control")
async def quality_control_module(request: Request):
    user = check_auth_cookie(request)
    if not user:
        return RedirectResponse(url="/login", status_code=302)

    return HTMLResponse("""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Quality Control - Vigilant Eye</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #eee; }
            .title { color: #333; margin: 0; }
            .nav-btn { background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; transition: background 0.3s; }
            .nav-btn:hover { background: #5a6fd8; }
            .module-content { text-align: center; padding: 50px 0; }
            .status { background: #e8f5e8; color: #2d5a2d; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1 class="title">🚀 Quality Control Module</h1>
                <a href="/dashboard" class="nav-btn">← Back to Dashboard</a>
            </div>
            <div class="module-content">
                <h2>Automated Quality Assurance</h2>
                <div class="status">✅ Module is running within Auth-Service</div>
                <p>Automated quality assurance and defect detection for manufacturing processes.</p>
                <p><strong>User:</strong> """ + user["username"] + """</p>
                <p><strong>Service:</strong> Auth-Service (Port 8000)</p>
                <p><em>This is a placeholder - actual AI functionality can be integrated here.</em></p>
            </div>
        </div>
    </body>
    </html>
    """)

@app.get("/face_recognition/face_recognition")
async def face_recognition_module(request: Request):
    user = check_auth_cookie(request)
    if not user:
        return RedirectResponse(url="/login", status_code=302)

    return HTMLResponse("""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Face Recognition - Vigilant Eye</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #eee; }
            .title { color: #333; margin: 0; }
            .nav-btn { background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; transition: background 0.3s; }
            .nav-btn:hover { background: #5a6fd8; }
            .module-content { text-align: center; padding: 50px 0; }
            .status { background: #e8f5e8; color: #2d5a2d; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1 class="title">🚀 Face Recognition Module</h1>
                <a href="/dashboard" class="nav-btn">← Back to Dashboard</a>
            </div>
            <div class="module-content">
                <h2>Advanced Facial Recognition System</h2>
                <div class="status">✅ Module is running within Auth-Service</div>
                <p>Advanced facial recognition and identification system for security applications.</p>
                <p><strong>User:</strong> """ + user["username"] + """</p>
                <p><strong>Service:</strong> Auth-Service (Port 8000)</p>
                <p><em>This is a placeholder - actual AI functionality can be integrated here.</em></p>
            </div>
        </div>
    </body>
    </html>
    """)

# Simple Authentication API Routes
@app.get("/auth/users/me")
async def read_users_me():
    """Get current user information"""
    return {"message": "User info endpoint", "service": "auth-service"}

@app.post("/auth/verify-token")
async def verify_token(token: str):
    """Verify token (simplified)"""
    return {"message": "Token verification endpoint", "service": "auth-service"}

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "Vigilant Eye Auth & Backend System"}

print("🎉 Authentication service initialization complete!")
print("📡 Auth-Service available at: http://127.0.0.1:8000")
print("🔗 AI Modules integrated within auth-service")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
