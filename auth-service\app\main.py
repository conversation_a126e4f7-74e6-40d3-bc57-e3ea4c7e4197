from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, HTTPException, status, Form
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.security import OAuth2PasswordRe<PERSON>Form
from sqlalchemy.orm import Session
from datetime import timedelta
import uvicorn
import httpx
import os

from app.auth import (
    authenticate_user,
    create_access_token,
    get_current_user,
    get_current_active_user
)

from app.schemas import (
    Token,
    UserCreate,
    TokenData
)

from app.utils import verify_password, get_password_hash
from app.database import get_db, init_database
from app.models import UserDB


print("🚀 Starting Vigilant Eye Authentication Service...")

# Initialize FastAPI app
app = FastAPI(title="Vigilant Eye - Authentication & Backend System", version="1.0.0")

# Setup templates and static files
templates = Jinja2Templates(directory="app/templates")

# CORS middleware
from fastapi.middleware.cors import CORSMiddleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize database on startup
@app.on_event("startup")
async def startup_event():
    init_database()

# Home page - redirects to login
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    return RedirectResponse(url="/login", status_code=302)

# Login page
@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

# Login endpoint
@app.post("/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

# Login form submission
@app.post("/login", response_class=HTMLResponse)
async def login(request: Request, username: str = Form(...), password: str = Form(...), db: Session = Depends(get_db)):
    user = authenticate_user(db, username, password)
    if not user:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Invalid username or password"}
        )

    # Create access token
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )

    # Redirect to dashboard with token in cookie
    response = RedirectResponse(url="/dashboard", status_code=status.HTTP_302_FOUND)
    response.set_cookie(
        key="access_token",
        value=f"Bearer {access_token}",
        httponly=True,
        max_age=ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        expires=ACCESS_TOKEN_EXPIRE_MINUTES * 60,
    )
    return response

# Dashboard page - main page after login
@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request, db: Session = Depends(get_db)):
    # Simple check for token in cookie
    token = request.cookies.get("access_token")
    if not token:
        return RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)

    # Strip Bearer prefix if present
    if token.startswith("Bearer "):
        token = token[7:]

    try:
        # Get actual user from token
        user_db = verify_token(token, db)
        if not user_db:
            return RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)

        # Create user object for template
        user = {
            "username": user_db.username,
            "email": user_db.email,
            "full_name": user_db.full_name,
            "role": getattr(user_db, 'role', 'viewer')
        }
    except:
        return RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)

    return templates.TemplateResponse("dashboard.html", {
        "request": request,
        "user": user
    })

# Logout endpoint
@app.get("/logout")
@app.post("/logout")
async def logout():
    response = RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)
    response.delete_cookie(key="access_token")
    return response

# User registration endpoint
@app.post("/register", response_class=HTMLResponse)
async def register(
    request: Request,
    username: str = Form(...),
    email: str = Form(...),
    full_name: str = Form(...),
    password: str = Form(...),
    role: str = Form("viewer"),  # Default to viewer role
    db: Session = Depends(get_db)
):
    # Check if username already exists
    existing_user = get_user(db, username)

    # Check if email already exists
    existing_email = get_user_by_email(db, email)

    # Handle different validation scenarios
    if existing_user and existing_email:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Username and Email ID already exist. Please go to login page."}
        )
    elif existing_user:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Username already exists. Please go to login page."}
        )
    elif existing_email:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Email ID already exists. Please go to login page."}
        )

    # If both username and email are available, create the user
    user_data = UserCreate(
        username=username,
        email=email,
        full_name=full_name,
        password=password,
        role=role
    )

    create_user(db, user_data)
    return templates.TemplateResponse(
        "login.html",
        {"request": request, "success": "Account created successfully! Please login."}
    )

# Add placeholder routes for AI modules (these will be replaced with actual modules later)
@app.get("/crowd_detection/crowd_detection")
async def crowd_detection_placeholder():
    return HTMLResponse("""
    <html>
        <head><title>Crowd Detection</title></head>
        <body>
            <h1>Crowd Detection Module</h1>
            <p>This module is running within the Auth-Service.</p>
            <p>Auth-Service is running on port 8001.</p>
            <a href="/dashboard">← Back to Dashboard</a>
        </body>
    </html>
    """)

@app.get("/helmet_detection/helmet_detection")
async def helmet_detection_placeholder():
    return HTMLResponse("""
    <html>
        <head><title>PPE Detection</title></head>
        <body>
            <h1>PPE Detection Module</h1>
            <p>This module is running within the Auth-Service.</p>
            <p>Auth-Service is running on port 8001.</p>
            <a href="/dashboard">← Back to Dashboard</a>
        </body>
    </html>
    """)

@app.get("/quality_control/quality_control")
async def quality_control_placeholder():
    return HTMLResponse("""
    <html>
        <head><title>Quality Control</title></head>
        <body>
            <h1>Quality Control Module</h1>
            <p>This module is running within the Auth-Service.</p>
            <p>Auth-Service is running on port 8001.</p>
            <a href="/dashboard">← Back to Dashboard</a>
        </body>
    </html>
    """)

@app.get("/face_recognition/face_recognition")
async def face_recognition_placeholder():
    return HTMLResponse("""
    <html>
        <head><title>Face Recognition</title></head>
        <body>
            <h1>Face Recognition Module</h1>
            <p>This module is running within the Auth-Service.</p>
            <p>Auth-Service is running on port 8001.</p>
            <a href="/dashboard">← Back to Dashboard</a>
        </body>
    </html>
    """)

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "Vigilant Eye Auth & Backend System"}

print("🎉 Authentication service initialization complete!")
print("📡 Auth-Service available at: http://127.0.0.1:8001")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)
