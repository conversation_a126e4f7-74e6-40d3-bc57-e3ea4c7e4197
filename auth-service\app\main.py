from fastapi import <PERSON><PERSON><PERSON>, Request, Form
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import <PERSON><PERSON><PERSON><PERSON>emplates
import uvicorn

print("🚀 Starting Vigilant Eye Authentication Service...")

# Initialize FastAPI app
app = FastAPI(title="Vigilant Eye - Authentication Service", version="1.0.0")

# Setup templates
templates = Jinja2Templates(directory="app/templates")

# Simple in-memory user storage for testing
users_db = {
    "admin": {
        "password": "admin123",
        "email": "<EMAIL>",
        "full_name": "Administrator",
        "role": "admin"
    },
    "viewer": {
        "password": "viewer123",
        "email": "<EMAIL>",
        "full_name": "Viewer User",
        "role": "viewer"
    }
}

# CORS middleware
from fastapi.middleware.cors import CORSMiddleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

print("✅ Authentication service initialized!")
print("👤 Default users: admin/admin123, viewer/viewer123")

# Helper functions
def check_auth_cookie(request: Request):
    """Check authentication from cookie"""
    username = request.cookies.get("user")
    if username and username in users_db:
        user_data = users_db[username]
        return {
            "username": username,
            "role": user_data["role"],
            "full_name": user_data["full_name"],
            "email": user_data["email"]
        }
    return None

def email_exists(email: str):
    """Check if email exists in users_db"""
    for username, user_data in users_db.items():
        if user_data["email"] == email:
            return True
    return False

def username_exists(username: str):
    """Check if username exists in users_db"""
    return username in users_db

# Home page - redirects to login
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    return RedirectResponse(url="/login", status_code=302)

# Login page
@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

# Login form submission
@app.post("/login", response_class=HTMLResponse)
async def login(request: Request, username: str = Form(...), password: str = Form(...)):
    # Simple authentication check
    if username in users_db and users_db[username]["password"] == password:
        # Successful login - redirect to dashboard
        response = RedirectResponse(url="/dashboard", status_code=302)
        response.set_cookie(key="user", value=username)
        return response
    else:
        # Failed login
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Invalid username or password"}
        )

# Dashboard page
@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request):
    # Check authentication
    user = check_auth_cookie(request)
    if not user:
        return RedirectResponse(url="/login", status_code=302)

    return templates.TemplateResponse("dashboard.html", {
        "request": request,
        "user": user
    })

# Logout endpoint
@app.get("/logout")
@app.post("/logout")
async def logout():
    response = RedirectResponse(url="/login", status_code=302)
    response.delete_cookie(key="user")
    return response

# Registration endpoint with specific validation cases
@app.post("/register", response_class=HTMLResponse)
async def register(
    request: Request,
    username: str = Form(...),
    email: str = Form(...),
    full_name: str = Form(...),
    password: str = Form(...),
    role: str = Form("viewer")
):
    # Check existing users - EXACT validation as requested
    existing_email = email_exists(email)
    existing_username = username_exists(username)

    # Handle the 3 specific validation cases as requested
    if existing_email and existing_username:
        # Case 2: Both email and username exist
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "already existed email and username"}
        )
    elif existing_email:
        # Case 1: Only email exists - show error and redirect to login
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "already existed email and go to login page"}
        )
    elif existing_username:
        # Case 3: Only username exists
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "already existed username"}
        )

    # If both username and email are available, create the user
    users_db[username] = {
        "password": password,
        "email": email,
        "full_name": full_name,
        "role": role
    }

    return templates.TemplateResponse(
        "login.html",
        {"request": request, "success": "Account created successfully! Please login."}
    )

# AI Module routes with authentication check - Navigate to actual Backend modules
@app.get("/crowd_detection/crowd_detection")
async def crowd_detection_module(request: Request):
    user = check_auth_cookie(request)
    if not user:
        return RedirectResponse(url="/login", status_code=302)

    # Redirect to Backend crowd detection module
    return RedirectResponse(url="http://127.0.0.1:8000/crowd_detection/crowd_detection", status_code=302)

@app.get("/helmet_detection/helmet_detection")
async def helmet_detection_module(request: Request):
    user = check_auth_cookie(request)
    if not user:
        return RedirectResponse(url="/login", status_code=302)

    # Redirect to Backend helmet detection module
    return RedirectResponse(url="http://127.0.0.1:8000/helmet_detection/helmet_detection", status_code=302)

@app.get("/quality_control/quality_control")
async def quality_control_module(request: Request):
    user = check_auth_cookie(request)
    if not user:
        return RedirectResponse(url="/login", status_code=302)

    # Redirect to Backend quality control module
    return RedirectResponse(url="http://127.0.0.1:8000/quality_control/quality_control", status_code=302)

@app.get("/face_recognition/face_recognition")
async def face_recognition_module(request: Request):
    user = check_auth_cookie(request)
    if not user:
        return RedirectResponse(url="/login", status_code=302)

    # Redirect to Backend face recognition module
    return RedirectResponse(url="http://127.0.0.1:8000/face_recognition/face_recognition", status_code=302)

# Simple Authentication API Routes
@app.get("/auth/users/me")
async def read_users_me():
    """Get current user information"""
    return {"message": "User info endpoint", "service": "auth-service"}

@app.post("/auth/verify-token")
async def verify_token(token: str):
    """Verify token (simplified)"""
    return {"message": "Token verification endpoint", "service": "auth-service"}

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "Vigilant Eye Auth & Backend System"}

print("🎉 Authentication service initialization complete!")
print("📡 Auth-Service available at: http://127.0.0.1:8001")
print("🔗 AI Modules redirect to Backend services (port 8000)")
print("👤 Test users: admin/admin123, viewer/viewer123")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)
