from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, HTTPException, status, Form
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.security import OAuth2Pass<PERSON>Re<PERSON>Form
from sqlalchemy.orm import Session
from datetime import timedelta
import uvicorn
import httpx
import os

# Import authentication components
from app.auth import (
    Token, User, UserCreate, authenticate_user, create_access_token,
    get_current_active_user, get_current_active_user_from_cookie, ACCESS_TOKEN_EXPIRE_MINUTES,
    get_user, get_user_by_email, create_user, verify_token
)
from app.database import get_db, init_database

# Initialize FastAPI app
app = FastAPI(title="Vigilant Eye - Authentication & Backend System", version="1.0.0")

# Setup templates and static files
templates = Jinja2Templates(directory="app/templates")

# CORS middleware
from fastapi.middleware.cors import CORSMiddleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Backend service URL
BACKEND_SERVICE_URL = "http://127.0.0.1:8000"

# Initialize database on startup
@app.on_event("startup")
async def startup_event():
    init_database()

# Home page - redirects to login
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    return RedirectResponse(url="/login", status_code=302)

# Login page
@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

# Login endpoint
@app.post("/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

# Login form submission
@app.post("/login", response_class=HTMLResponse)
async def login(request: Request, username: str = Form(...), password: str = Form(...), db: Session = Depends(get_db)):
    user = authenticate_user(db, username, password)
    if not user:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Invalid username or password"}
        )

    # Create access token
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )

    # Redirect to dashboard with token in cookie
    response = RedirectResponse(url="/dashboard", status_code=status.HTTP_302_FOUND)
    response.set_cookie(
        key="access_token",
        value=f"Bearer {access_token}",
        httponly=True,
        max_age=ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        expires=ACCESS_TOKEN_EXPIRE_MINUTES * 60,
    )
    return response

# Dashboard page - main page after login with backend proxy
@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request, db: Session = Depends(get_db)):
    # Simple check for token in cookie
    token = request.cookies.get("access_token")
    if not token:
        return RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)

    # Strip Bearer prefix if present
    if token.startswith("Bearer "):
        token = token[7:]

    try:
        # Get actual user from token
        user_db = verify_token(token, db)
        if not user_db:
            return RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)

        # Create user object for template
        user = {
            "username": user_db.username,
            "email": user_db.email,
            "full_name": user_db.full_name,
            "role": getattr(user_db, 'role', 'viewer')
        }
    except:
        return RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)

    return templates.TemplateResponse("dashboard.html", {
        "request": request,
        "user": user
    })

# Logout endpoint
@app.get("/logout")
@app.post("/logout")
async def logout():
    response = RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)
    response.delete_cookie(key="access_token")
    return response

# User registration endpoint
@app.post("/register", response_class=HTMLResponse)
async def register(
    request: Request,
    username: str = Form(...),
    email: str = Form(...),
    full_name: str = Form(...),
    password: str = Form(...),
    role: str = Form("viewer"),  # Default to viewer role
    db: Session = Depends(get_db)
):
    # Check if username already exists
    existing_user = get_user(db, username)

    # Check if email already exists
    existing_email = get_user_by_email(db, email)

    # Handle different validation scenarios
    if existing_user and existing_email:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Username and Email ID already exist. Please go to login page."}
        )
    elif existing_user:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Username already exists. Please go to login page."}
        )
    elif existing_email:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Email ID already exists. Please go to login page."}
        )

    # If both username and email are available, create the user
    user_data = UserCreate(
        username=username,
        email=email,
        full_name=full_name,
        password=password,
        role=role
    )

    create_user(db, user_data)
    return templates.TemplateResponse(
        "login.html",
        {"request": request, "success": "Account created successfully! Please login."}
    )

# Token validation endpoint for other services
@app.get("/validate-token")
async def validate_token(request: Request, db: Session = Depends(get_db)):
    # Get token from Authorization header or cookie
    token = request.headers.get("Authorization")
    if not token:
        token = request.cookies.get("access_token")

    if not token:
        raise HTTPException(status_code=401, detail="No token provided")

    # Strip Bearer prefix if present
    if token.startswith("Bearer "):
        token = token[7:]

    try:
        user = verify_token(token, db)
        if user:
            return {
                "valid": True,
                "user": {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "full_name": user.full_name,
                    "role": getattr(user, 'role', 'viewer'),
                    "disabled": user.disabled
                }
            }
        else:
            return {"valid": False}
    except:
        return {"valid": False}

# Backend Proxy Routes - Proxy requests to backend service
@app.get("/crowd_detection/{path:path}")
async def proxy_crowd_detection(request: Request, path: str):
    """Proxy crowd detection requests to backend service"""
    return await proxy_to_backend(request, f"crowd_detection/{path}")

@app.post("/crowd_detection/{path:path}")
async def proxy_crowd_detection_post(request: Request, path: str):
    """Proxy crowd detection POST requests to backend service"""
    return await proxy_to_backend(request, f"crowd_detection/{path}")

@app.get("/helmet_detection/{path:path}")
async def proxy_helmet_detection(request: Request, path: str):
    """Proxy helmet detection requests to backend service"""
    return await proxy_to_backend(request, f"helmet_detection/{path}")

@app.post("/helmet_detection/{path:path}")
async def proxy_helmet_detection_post(request: Request, path: str):
    """Proxy helmet detection POST requests to backend service"""
    return await proxy_to_backend(request, f"helmet_detection/{path}")

@app.get("/quality_control/{path:path}")
async def proxy_quality_control(request: Request, path: str):
    """Proxy quality control requests to backend service"""
    return await proxy_to_backend(request, f"quality_control/{path}")

@app.post("/quality_control/{path:path}")
async def proxy_quality_control_post(request: Request, path: str):
    """Proxy quality control POST requests to backend service"""
    return await proxy_to_backend(request, f"quality_control/{path}")

@app.get("/face_recognition/{path:path}")
async def proxy_face_recognition(request: Request, path: str):
    """Proxy face recognition requests to backend service"""
    return await proxy_to_backend(request, f"face_recognition/{path}")

@app.post("/face_recognition/{path:path}")
async def proxy_face_recognition_post(request: Request, path: str):
    """Proxy face recognition POST requests to backend service"""
    return await proxy_to_backend(request, f"face_recognition/{path}")

async def proxy_to_backend(request: Request, path: str):
    """Helper function to proxy requests to backend service"""
    try:
        # Build the target URL
        target_url = f"{BACKEND_SERVICE_URL}/{path}"

        # Get query parameters
        query_params = str(request.url.query)
        if query_params:
            target_url += f"?{query_params}"

        # Forward the request to backend service
        async with httpx.AsyncClient() as client:
            if request.method == "GET":
                response = await client.get(target_url, headers=dict(request.headers))
            elif request.method == "POST":
                body = await request.body()
                response = await client.post(target_url, content=body, headers=dict(request.headers))
            else:
                response = await client.request(
                    request.method, target_url,
                    content=await request.body(),
                    headers=dict(request.headers)
                )

        # Return the response from backend
        if response.headers.get("content-type", "").startswith("text/html"):
            return HTMLResponse(content=response.content, status_code=response.status_code)
        else:
            return response.content

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Backend service error: {str(e)}")

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "Vigilant Eye Auth & Backend System"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)
