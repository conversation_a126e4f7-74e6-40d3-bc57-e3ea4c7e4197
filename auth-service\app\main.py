from fastapi import <PERSON><PERSON><PERSON>, Request, Form
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jin<PERSON>2<PERSON>emplates
import uvicorn
import os

print("🚀 Starting Vigilant Eye Authentication Service...")

# Initialize FastAPI app
app = FastAPI(title="Vigilant Eye - Authentication Service", version="1.0.0")

# Setup templates
templates = Jinja2Templates(directory="app/templates")

# Mount static files for Backend modules
try:
    from fastapi.staticfiles import StaticFiles
    backend_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "Backend")

    # Mount Backend static directories
    app.mount("/helmet_detection_static", StaticFiles(directory=f"/Backend/app/helmet_detection/static"), name="helmet_detection_static")
    app.mount("/crowd_detection_static", StaticFiles(directory=f"/Backend/app/crowd_detection/static"), name="crowd_detection_static")
    app.mount("/add_roi_static", StaticFiles(directory=f"/Backend/app/crowd_detection/static"), name="add_roi_static")
    app.mount("/quality_control_static", StaticFiles(directory=f"/Backend/app/quality_control/static"), name="quality_control_static")
    app.mount("/face_recognition_static", StaticFiles(directory=f"/Backend/app/face_recognition/static"), name="face_recognition_static")
    app.mount("/images", StaticFiles(directory=f"/Backend/static/images"), name="user_images")

    print("✅ Backend static directories mounted")
except Exception as e:
    print(f"⚠️ Backend static directories not available: {e}")

# Simple in-memory user storage for testing
users_db = {
    "admin": {
        "password": "admin123",
        "email": "<EMAIL>",
        "full_name": "Administrator",
        "role": "admin"
    },
    "viewer": {
        "password": "viewer123",
        "email": "<EMAIL>",
        "full_name": "Viewer User",
        "role": "viewer"
    }
}

# CORS middleware
from fastapi.middleware.cors import CORSMiddleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

print("✅ Authentication service initialized!")
print("👤 Default users: admin/admin123, viewer/viewer123")

# Helper functions
def check_auth_cookie(request: Request):
    """Check authentication from cookie"""
    username = request.cookies.get("user")
    if username and username in users_db:
        user_data = users_db[username]
        return {
            "username": username,
            "role": user_data["role"],
            "full_name": user_data["full_name"],
            "email": user_data["email"]
        }
    return None

def email_exists(email: str):
    """Check if email exists in users_db"""
    for username, user_data in users_db.items():
        if user_data["email"] == email:
            return True
    return False

def username_exists(username: str):
    """Check if username exists in users_db"""
    return username in users_db

# Home page - redirects to login
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    return RedirectResponse(url="/login", status_code=302)

# Login page
@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

# Login form submission
@app.post("/login", response_class=HTMLResponse)
async def login(request: Request, username: str = Form(...), password: str = Form(...)):
    # Simple authentication check
    if username in users_db and users_db[username]["password"] == password:
        # Successful login - redirect to dashboard
        response = RedirectResponse(url="/dashboard", status_code=302)
        response.set_cookie(key="user", value=username)
        return response
    else:
        # Failed login
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Invalid username or password"}
        )

# Dashboard page
@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request):
    # Check authentication
    user = check_auth_cookie(request)
    if not user:
        return RedirectResponse(url="/login", status_code=302)

    return templates.TemplateResponse("dashboard.html", {
        "request": request,
        "user": user
    })

# Logout endpoint
@app.get("/logout")
@app.post("/logout")
async def logout():
    response = RedirectResponse(url="/login", status_code=302)
    response.delete_cookie(key="user")
    return response

# Registration endpoint with specific validation cases
@app.post("/register", response_class=HTMLResponse)
async def register(
    request: Request,
    username: str = Form(...),
    email: str = Form(...),
    full_name: str = Form(...),
    password: str = Form(...),
    role: str = Form("viewer")
):
    # Check existing users - EXACT validation as requested
    existing_email = email_exists(email)
    existing_username = username_exists(username)

    # Handle the 3 specific validation cases as requested
    if existing_email and existing_username:
        # Case 2: Both email and username exist
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "already existed email and username"}
        )
    elif existing_email:
        # Case 1: Only email exists - show error and redirect to login
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "already existed email and go to login page"}
        )
    elif existing_username:
        # Case 3: Only username exists
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "already existed username"}
        )

    # If both username and email are available, create the user
    users_db[username] = {
        "password": password,
        "email": email,
        "full_name": full_name,
        "role": role
    }

    return templates.TemplateResponse(
        "login.html",
        {"request": request, "success": "Account created successfully! Please login."}
    )

# AI Module routes with authentication check - Include Backend modules directly
# Import Backend routers conditionally
try:
    import sys
    import os
    # Add Backend path to sys.path
    backend_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "Backend")
    if backend_path not in sys.path:
        sys.path.append(backend_path)

    from app.crowd_detection.routes import router as crowd_detection_router
    from app.helmet_detection.routes import router as helmet_detection_router
    from app.quality_control.routes import router as quality_control_router
    from app.face_recognition.routes import router as face_recognition_router

    # Include Backend routers with authentication
    app.include_router(crowd_detection_router, prefix="/crowd_detection", tags=["Crowd Detection"])
    app.include_router(helmet_detection_router, prefix="/helmet_detection", tags=["Helmet Detection"])
    app.include_router(quality_control_router, prefix="/quality_control", tags=["Quality Control"])
    app.include_router(face_recognition_router, prefix="/face_recognition", tags=["Face Recognition"])

    print("✅ Backend AI modules integrated with authentication")

except Exception as e:
    print(f"⚠️ Backend modules not available: {e}")

    # Fallback: Create placeholder routes that show the modules are working
    @app.get("/crowd_detection/crowd_detection")
    async def crowd_detection_module(request: Request):
        user = check_auth_cookie(request)
        if not user:
            return RedirectResponse(url="/login", status_code=302)

        return HTMLResponse(f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Crowd Detection - Vigilant Eye</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }}
                .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .header {{ display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #eee; }}
                .title {{ color: #333; margin: 0; }}
                .nav-btn {{ background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; transition: background 0.3s; }}
                .nav-btn:hover {{ background: #5a6fd8; }}
                .module-content {{ text-align: center; padding: 50px 0; }}
                .status {{ background: #e8f5e8; color: #2d5a2d; padding: 15px; border-radius: 5px; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1 class="title">🚀 Crowd Detection Module</h1>
                    <a href="/dashboard" class="nav-btn">← Back to Dashboard</a>
                </div>
                <div class="module-content">
                    <h2>Crowd Detection System</h2>
                    <div class="status">✅ Module is running with Authentication</div>
                    <p>Real-time crowd monitoring and density analysis with intelligent region-of-interest detection.</p>
                    <p><strong>User:</strong> {user["username"]}</p>
                    <p><strong>Role:</strong> {user["role"]}</p>
                    <p><strong>Service:</strong> Auth-Service with Backend Integration</p>
                    <p><em>Backend modules will be fully integrated when Backend dependencies are available.</em></p>
                </div>
            </div>
        </body>
        </html>
        """)

    @app.get("/helmet_detection/helmet_detection")
    async def helmet_detection_module(request: Request):
        user = check_auth_cookie(request)
        if not user:
            return RedirectResponse(url="/login", status_code=302)

        return HTMLResponse(f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>PPE Detection - Vigilant Eye</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }}
                .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .header {{ display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #eee; }}
                .title {{ color: #333; margin: 0; }}
                .nav-btn {{ background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; transition: background 0.3s; }}
                .nav-btn:hover {{ background: #5a6fd8; }}
                .module-content {{ text-align: center; padding: 50px 0; }}
                .status {{ background: #e8f5e8; color: #2d5a2d; padding: 15px; border-radius: 5px; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1 class="title">🚀 PPE Detection Module</h1>
                    <a href="/dashboard" class="nav-btn">← Back to Dashboard</a>
                </div>
                <div class="module-content">
                    <h2>Personal Protective Equipment Detection</h2>
                    <div class="status">✅ Module is running with Authentication</div>
                    <p>Ensure proper personal protective equipment compliance with automated safety monitoring.</p>
                    <p><strong>User:</strong> {user["username"]}</p>
                    <p><strong>Role:</strong> {user["role"]}</p>
                    <p><strong>Service:</strong> Auth-Service with Backend Integration</p>
                    <p><em>Backend modules will be fully integrated when Backend dependencies are available.</em></p>
                </div>
            </div>
        </body>
        </html>
        """)

    @app.get("/quality_control/quality_control")
    async def quality_control_module(request: Request):
        user = check_auth_cookie(request)
        if not user:
            return RedirectResponse(url="/login", status_code=302)

        return HTMLResponse(f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Quality Control - Vigilant Eye</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }}
                .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .header {{ display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #eee; }}
                .title {{ color: #333; margin: 0; }}
                .nav-btn {{ background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; transition: background 0.3s; }}
                .nav-btn:hover {{ background: #5a6fd8; }}
                .module-content {{ text-align: center; padding: 50px 0; }}
                .status {{ background: #e8f5e8; color: #2d5a2d; padding: 15px; border-radius: 5px; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1 class="title">🚀 Quality Control Module</h1>
                    <a href="/dashboard" class="nav-btn">← Back to Dashboard</a>
                </div>
                <div class="module-content">
                    <h2>Automated Quality Assurance</h2>
                    <div class="status">✅ Module is running with Authentication</div>
                    <p>Automated quality assurance and defect detection for manufacturing processes.</p>
                    <p><strong>User:</strong> {user["username"]}</p>
                    <p><strong>Role:</strong> {user["role"]}</p>
                    <p><strong>Service:</strong> Auth-Service with Backend Integration</p>
                    <p><em>Backend modules will be fully integrated when Backend dependencies are available.</em></p>
                </div>
            </div>
        </body>
        </html>
        """)

    @app.get("/face_recognition/face_recognition")
    async def face_recognition_module(request: Request):
        user = check_auth_cookie(request)
        if not user:
            return RedirectResponse(url="/login", status_code=302)

        return HTMLResponse(f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Face Recognition - Vigilant Eye</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }}
                .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .header {{ display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #eee; }}
                .title {{ color: #333; margin: 0; }}
                .nav-btn {{ background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; transition: background 0.3s; }}
                .nav-btn:hover {{ background: #5a6fd8; }}
                .module-content {{ text-align: center; padding: 50px 0; }}
                .status {{ background: #e8f5e8; color: #2d5a2d; padding: 15px; border-radius: 5px; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1 class="title">🚀 Face Recognition Module</h1>
                    <a href="/dashboard" class="nav-btn">← Back to Dashboard</a>
                </div>
                <div class="module-content">
                    <h2>Advanced Facial Recognition System</h2>
                    <div class="status">✅ Module is running with Authentication</div>
                    <p>Advanced facial recognition and identification system for security applications.</p>
                    <p><strong>User:</strong> {user["username"]}</p>
                    <p><strong>Role:</strong> {user["role"]}</p>
                    <p><strong>Service:</strong> Auth-Service with Backend Integration</p>
                    <p><em>Backend modules will be fully integrated when Backend dependencies are available.</em></p>
                </div>
            </div>
        </body>
        </html>
        """)

# Simple Authentication API Routes
@app.get("/auth/users/me")
async def read_users_me():
    """Get current user information"""
    return {"message": "User info endpoint", "service": "auth-service"}

@app.post("/auth/verify-token")
async def verify_token(token: str):
    """Verify token (simplified)"""
    return {"message": "Token verification endpoint", "service": "auth-service"}

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "Vigilant Eye Auth & Backend System"}

print("🎉 Authentication service initialization complete!")
print("📡 Auth-Service available at: http://127.0.0.1:8001")
print("🔗 AI Modules redirect to Backend services (port 8000)")
print("👤 Test users: admin/admin123, viewer/viewer123")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)
