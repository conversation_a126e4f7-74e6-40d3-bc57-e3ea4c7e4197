/* Reset styles */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f9f9fb;
    color: #333;
}

/* Header styles - same as home page */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: #fff;
    border-bottom: 1px solid #ddd;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo {
    height: 50px;
}

.company-name {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    letter-spacing: 1px;
}

/* Login Container */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: calc(100vh - 100px);
    padding: 20px;
}

.login-box {
    width: 100%;
    max-width: 400px;
    background-color: #fff;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.login-box h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
}

.input-group {
    margin-bottom: 20px;
}

.input-group label {
    display: block;
    margin-bottom: 5px;
    color: #666;
    font-weight: 500;
}

.input-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.input-group input:focus {
    border-color: #43cea2;
    outline: none;
    box-shadow: 0 0 0 2px rgba(67, 206, 162, 0.1);
}

.remember-me {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.remember-me input {
    margin-right: 8px;
}

.login-button {
    width: 100%;
    padding: 12px;
    background: linear-gradient(135deg, #43cea2 0%, #185a9d 100%);
    border: none;
    border-radius: 6px;
    color: white;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.login-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(67, 206, 162, 0.2);
}

.error-message {
    background-color: #ffebee;
    color: #c62828;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 20px;
    text-align: center;
}

/* Responsive design */
@media (max-width: 768px) {
    .login-box {
        padding: 20px;
    }
}