<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vigilant Eye - PPE Detection Dashboard</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <style>
    :root {
      --primary: #1e88e5;
      --primary-dark: #1565c0;
      --success: #2e7d32;
      --danger: #d32f2f;
      --warning: #ff9800;
      --light: #f5f5f5;
      --dark: #212121;
      --gray: #757575;
      --card-bg: #ffffff;
      --sidebar-bg: #1a237e;
      --sidebar-hover: #303f9f;
      --text-muted: #7a7a7a;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    body {
      background-color: #f0f2f5;
      color: var(--dark);
      display: flex;
      min-height: 100vh;
    }

    .sidebar {
      width: 280px;
      background: linear-gradient(180deg, var(--sidebar-bg) 0%, #0d1b42 100%);
      color: white;
      padding: 20px 0;
      display: flex;
      flex-direction: column;
      box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
      position: fixed;
      top: 0;
      left: 0;
      height: 100%;
      overflow-y: auto;
    }

    .logo {
      padding: 20px 25px;
      font-size: 24px;
      font-weight: bold;
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      letter-spacing: 0.5px;
    }

    .logo svg {
      margin-right: 12px;
    }

    .nav-item {
      padding: 12px 25px;
      display: flex;
      align-items: center;
      color: rgba(255, 255, 255, 0.85);
      text-decoration: none;
      font-size: 15px;
      transition: all 0.2s;
      border-left: 4px solid transparent;
      cursor: pointer;
      border: none;
      background: none;
      width: 100%;
      text-align: left;
    }

    .nav-item svg {
      margin-right: 12px;
      width: 22px;
      height: 22px;
    }

    .nav-item:hover {
      background-color: var(--sidebar-hover);
      color: white;
    }

    .nav-active {
      background-color: rgba(255, 255, 255, 0.1);
      border-left: 4px solid var(--primary);
      color: white;
      font-weight: 500;
    }

    .system-status {
      margin-top: auto;
      padding: 20px 25px;
      font-size: 12px;
      color: rgba(255, 255, 255, 0.7);
    }

    .status-indicator {
      display: inline-block;
      width: 8px;
      height: 8px;
      background-color: #4CAF50;
      border-radius: 50%;
      margin-right: 8px;
    }

    .content {
      flex: 1;
      padding: 25px;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      margin-left: 280px;
    }

    .normal-view {
      display: flex;
      flex-direction: column;
      flex: 1;
    }

    .normal-view.hidden {
      display: none;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 25px;
    }

    .page-title {
      display: flex;
      align-items: center;
      font-size: 24px;
      font-weight: 600;
      color: var(--dark);
    }

    .page-title svg {
      margin-right: 12px;
    }

    .action-buttons {
      display: flex;
      gap: 10px;
    }

    .btn {
      padding: 10px 20px;
      border-radius: 6px;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s;
      border: none;
      font-size: 14px;
    }

    .btn svg {
      margin-right: 8px;
    }

    .btn-primary {
      background-color: var(--primary);
      color: white;
    }

    .btn-primary:hover {
      background-color: var(--primary-dark);
    }

    .btn-secondary {
      background-color: #d01736;
      color: white;
    }

    .btn-secondary:hover {
      background-color: #f74d6f;
    }

    .btn-outline {
      background-color: transparent;
      border: 1px solid var(--primary);
      color: var(--primary);
    }

    .btn-outline:hover {
      background-color: rgba(30, 136, 229, 0.1);
    }

    .stat-cards {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20px;
      margin-bottom: 30px;
    }

    .stat-card {
      background: var(--card-bg);
      padding: 24px;
      border-radius: 12px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
      border-left: 4px solid transparent;
    }

    .stat-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .stat-card.total-detections {
      border-left-color: var(--primary);
    }

    .stat-card.no-helmet {
      border-left-color: var(--danger);
    }

    .stat-card.no-vest {
      border-left-color: var(--warning);
    }

    .stat-card.no-gloves {
      border-left-color: var(--success);
    }

    .stat-title {
      font-size: 14px;
      color: var(--gray);
      margin-bottom: 8px;
      font-weight: 500;
    }

    .stat-value {
      font-size: 32px;
      font-weight: 700;
      color: var(--dark);
      margin-bottom: 4px;
    }

    .stat-subtitle {
      font-size: 12px;
      color: var(--gray);
    }

    .section-title {
      font-size: 20px;
      font-weight: 600;
      color: var(--dark);
      margin-bottom: 20px;
      display: flex;
      align-items: center;
    }

    .camera-feeds {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 24px;
      margin-bottom: 30px;
    }

    .camera-card {
      background: var(--card-bg);
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
    }

    .camera-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .camera-feed {
      position: relative;
      height: 240px;
      background: #f0f0f0;
      overflow: hidden;
    }

    .camera-feed img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .status-badge {
      position: absolute;
      top: 12px;
      right: 12px;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .live-badge {
      background-color: rgba(76, 175, 80, 0.9);
      color: white;
    }

    .offline-badge {
      background-color: rgba(244, 67, 54, 0.9);
      color: white;
    }

    .stream-status {
      position: absolute;
      bottom: 12px;
      left: 12px;
      padding: 4px 8px;
      border-radius: 6px;
      font-size: 10px;
      font-weight: 600;
      text-transform: uppercase;
    }

    .streaming {
      background-color: rgba(76, 175, 80, 0.9);
      color: white;
    }

    .not-streaming {
      background-color: rgba(244, 67, 54, 0.9);
      color: white;
    }

    .camera-info {
      padding: 20px;
    }

    .camera-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--dark);
      margin-bottom: 8px;
    }

    .camera-meta {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
      font-size: 13px;
      color: var(--gray);
    }

    .camera-actions {
      display: flex;
      gap: 8px;
    }

    .action-btn {
      flex: 1;
      padding: 8px 12px;
      border: 1px solid #e0e0e0;
      background: white;
      border-radius: 6px;
      font-size: 12px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
    }

    .action-btn:hover {
      background-color: #f5f5f5;
      border-color: #d0d0d0;
    }

    .expand-btn:hover {
      background-color: rgba(30, 136, 229, 0.1);
      border-color: var(--primary);
      color: var(--primary);
    }

    .alert-btn:hover {
      background-color: rgba(255, 152, 0, 0.1);
      border-color: var(--warning);
      color: var(--warning);
    }

    /* Expanded view styles */
    .expanded-view {
      display: none;
      flex: 1;
    }

    .expanded-view.active {
      display: flex;
      flex-direction: column;
    }

    .content-area {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 24px;
      flex: 1;
    }

    .panel {
      background-color: var(--card-bg);
      border-radius: 12px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid #eaeaea;
    }

    .panel-title {
      font-size: 18px;
      font-weight: 500;
    }

    .panel-actions {
      display: flex;
      gap: 12px;
    }

    .panel-body {
      padding: 20px;
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .webcam-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      position: relative;
      background-color: #f0f0f0;
      border-radius: 4px;
      min-height: 400px;
    }

    .webcam-placeholder {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      color: var(--gray);
    }

    .webcam-placeholder i {
      font-size: 40px;
      margin-bottom: 16px;
    }

    .crowd-metrics {
      display: flex;
      flex-direction: column;
      width: 100%;
    }

    .metric-card {
      background: #fff;
      border-radius: 8px;
      padding: 14px 16px;
      margin-bottom: 12px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
      transition: all 0.2s ease;
    }

    .metric-card:hover {
      box-shadow: 0 3px 6px rgba(0, 0, 0, 0.12);
      transform: translateY(-2px);
    }

    .metric-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }

    .metric-title {
      font-size: 14px;
      font-weight: 500;
      color: var(--gray);
    }

    .metric-icon {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .metric-value {
      font-size: 22px;
      font-weight: 600;
      margin-bottom: 4px;
      color: var(--dark);
    }

    .metric-subtitle {
      font-size: 13px;
      color: var(--gray);
    }

    .metric-counts {
      display: flex;
      gap: 12px;
      margin-top: 8px;
    }

    .count-item {
      flex: 1;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 6px;
      text-align: center;
    }

    .count-label {
      font-size: 12px;
      color: var(--text-muted);
      margin-bottom: 4px;
    }

    .count-value {
      font-size: 18px;
      font-weight: 600;
      color: var(--dark);
    }

    .green-count {
      color: var(--success);
    }

    .amber-count {
      color: var(--warning);
    }

    .blue-bg {
      background-color: rgba(30, 136, 229, 0.15);
    }

    .blue-icon {
      color: #1e88e5;
    }

    .status-active {
      background-color: rgba(76, 175, 80, 0.1);
      color: #4CAF50;
    }

    .controls {
      display: flex;
      gap: 10px;
      margin-top: 10px;
    }

    .control-btn {
      background-color: #f5f5f5;
      border: none;
      width: 36px;
      height: 36px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s;
    }

    .control-btn:hover {
      background-color: #e0e0e0;
    }
  </style>
</head>
<body>
  <div class="sidebar">
    <div class="logo">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <circle cx="12" cy="12" r="10"></circle>
        <circle cx="12" cy="12" r="3"></circle>
        <line x1="12" y1="2" x2="12" y2="5"></line>
        <line x1="12" y1="19" x2="12" y2="22"></line>
        <line x1="2" y1="12" x2="5" y2="12"></line>
        <line x1="19" y1="12" x2="22" y2="12"></line>
      </svg>
      Vigilant Eye
    </div>



    <button class="nav-item" id="manageCameraBtn">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
        <circle cx="12" cy="13" r="4"></circle>
      </svg>
      Camera Management
    </button>

    <button class="nav-item" id="managealert">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
        <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
      </svg>
      Alert Management
    </button>

    <a href="/" class="nav-item" id="homepageBtn">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
        <polyline points="9 22 9 12 15 12 15 22"></polyline>
      </svg>
      Home
    </a>

    <a href="/helmet_detection" class="nav-item nav-active">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"></path>
      </svg>
      PPE Detection
    </a>

    <a href="/crowd_detection" class="nav-item">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
        <circle cx="9" cy="7" r="4"></circle>
        <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
      </svg>
      Crowd Detection
    </a>

    <div class="system-status">
      <div class="status-indicator"></div>
      System Online
    </div>
  </div>

  <div class="content">
    <!-- Normal View -->
    <div class="normal-view" id="normalView">
      <div class="header">
        <div class="page-title">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"></path>
          </svg>
          PPE Detection Dashboard
        </div>
        <div class="action-buttons">
          <button class="btn btn-outline" id="stopBtn">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="6" y="4" width="4" height="16"></rect>
              <rect x="14" y="4" width="4" height="16"></rect>
            </svg>
            Stop Monitoring
          </button>
          <button class="btn btn-primary" id="startBtn">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polygon points="5 3 19 12 5 21 5 3"></polygon>
            </svg>
            Start Monitoring
          </button>
        </div>
      </div>

      <!-- Statistics Cards -->
      <div class="stat-cards" id="statCards">
        <div class="stat-card total-detections">
          <div class="stat-title">Total People Detected</div>
          <div class="stat-value" id="totalDetectionsValue">0</div>
          <div class="stat-subtitle">Across all cameras</div>
        </div>
        <div class="stat-card no-helmet">
          <div class="stat-title">No Helmet Violations</div>
          <div class="stat-value" id="noHelmetValue">0</div>
          <div class="stat-subtitle">Safety violations</div>
        </div>
        <div class="stat-card no-vest">
          <div class="stat-title">No Vest Violations</div>
          <div class="stat-value" id="noVestValue">0</div>
          <div class="stat-subtitle">Safety violations</div>
        </div>
        <div class="stat-card no-gloves">
          <div class="stat-title">No Gloves Violations</div>
          <div class="stat-value" id="noGlovesValue">0</div>
          <div class="stat-subtitle">Safety violations</div>
        </div>
      </div>

      <!-- Camera Feeds Section -->
      <div class="section">
        <h2 class="section-title">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
            <circle cx="12" cy="13" r="4"></circle>
          </svg>
          Live Camera Feeds
        </h2>
        <div class="camera-feeds" id="cameraFeeds">
          <!-- Camera cards will be dynamically inserted here -->
        </div>
      </div>
    </div>

    <!-- Expanded View -->
    <div class="expanded-view" id="expandedView">
      <div class="header">
        <div class="page-title">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"></path>
          </svg>
          <span id="expandedCameraTitle">PPE Detection - Camera View</span>
        </div>
        <div class="action-buttons">
          <button class="btn btn-secondary" id="backBtn">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
            Back to Dashboard
          </button>
        </div>
      </div>

      <div class="content-area">
        <!-- Video Panel -->
        <div class="panel">
          <div class="panel-header">
            <h2 class="panel-title">Live Video Feed</h2>
            <div class="panel-actions">
              <div class="status-badge live-badge">LIVE</div>
            </div>
          </div>
          <div class="panel-body">
            <div class="webcam-container">
              <img id="expandedVideoFeed" style="width: 100%; height: 100%; object-fit: contain; display: none;" />
              <div class="webcam-placeholder" id="expandedPlaceholder">
                <i class="fas fa-camera"></i>
                <p>Camera feed not active</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Live Observations Panel -->
        <div class="panel">
          <div class="panel-header">
            <h2 class="panel-title">Live Observations</h2>
            <div class="status-badge live-badge">Live</div>
            <!-- <div class="panel-actions">
              <div class="status-badge live-badge">Live</div>
            </div> -->
          </div>
          <div class="panel-body">
            <div class="crowd-metrics">
              <div class="metric-card">
                <div class="metric-header">
                  <div class="metric-title">Camera Name</div>
                  <div class="metric-icon blue-bg">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="blue-icon">
                      <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
                      <circle cx="12" cy="13" r="4"></circle>
                    </svg>
                  </div>
                </div>
                <div class="metric-value" id="expandedCameraName">Main Entrance</div>
                <div class="metric-subtitle" id="expandedLastUpdate">Updated just now</div>

                <div class="metric-counts">
                  <div class="count-item">
                    <div class="count-label">Total Count</div>
                    <div class="count-value" id="total-count-1">0</div>
                  </div>
                </div>

                <div class="metric-counts">
                  <div class="count-item">
                    <div class="count-label">Without Helmet</div>
                    <div class="count-value green-count" id="no-helmet-count-1">0</div>
                  </div>

                  <div class="count-item">
                    <div class="count-label">Without</div>
                    <div class="count-label">Vest</div>
                    <div class="count-value amber-count" id="no-vest-count-1">0</div>
                  </div>

                  <div class="count-item">
                    <div class="count-label">Without Gloves</div>
                    <div class="count-value amber-count" id="no-gloves-count-1">0</div>
                  </div>
                </div>
              </div>

              <div style="margin-top: 15px; text-align: center;">
                <button class="btn btn-primary" style="width: 100%;">
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                    <polyline points="17 21 17 13 7 13 7 21"></polyline>
                    <polyline points="7 3 7 8 15 8"></polyline>
                  </svg>
                  Save Changes
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>



  <script>
    // Global variables
    let websocket = null;
    let isStreaming = false;
    let currentExpandedCamera = null;
    let latestCameraData = {};
    let availableCameras = {};

    // DOM elements
    const startBtn = document.getElementById('startBtn');
    const stopBtn = document.getElementById('stopBtn');
    const manageCameraBtn = document.getElementById('manageCameraBtn');
    const managealertBtn = document.getElementById('managealert');
    const backBtn = document.getElementById('backBtn');
    const normalView = document.getElementById('normalView');
    const expandedView = document.getElementById('expandedView');

    // Initialize the application - exactly like crowd_detection
    document.addEventListener('DOMContentLoaded', async function() {
      initializeApp();
      setupEventListeners();
      await loadCameraCards();
      if (sessionStorage.getItem("ppeStreaming") === "true") {
        startStream();
      }
    });

    function initializeApp() {
      // Check if streaming was active before page reload
      if (sessionStorage.getItem("ppeStreaming") === "true") {
        startStream();
      }
    }

    function setupEventListeners() {
      startBtn.addEventListener('click', startStream);
      stopBtn.addEventListener('click', stopStream);
      managealertBtn.addEventListener('click', openAlertModal);
      backBtn.addEventListener('click', backToNormal);
    }

    // Camera loading and management functions - exactly like crowd_detection
    async function loadCameraCards() {
      const container = document.querySelector('.camera-feeds');
      container.innerHTML = ""; // Clear existing cards

      try {
        const response = await fetch("/helmet_detection/get-cameras");
        const cameras = await response.json();
        availableCameras = cameras;

        if (Object.keys(cameras).length === 0) {
          container.innerHTML = '<p style="text-align: center; color: #777; grid-column: 1 / -1;">No cameras available. Add cameras to start monitoring.</p>';
          return;
        }

        let cameraId = 1;
        for (const [cameraName, rtspData] of Object.entries(cameras)) {
          const card = createCameraCard(cameraId, cameraName);
          container.appendChild(card);
          cameraId++;
        }
      } catch (error) {
        console.error("Failed to load camera cards:", error);
        showNotification('Failed to load cameras', 'error');
      }
    }

    function createCameraCard(cameraId, cameraName) {
      const cameraCard = document.createElement('div');
      cameraCard.className = 'camera-card';
      cameraCard.innerHTML = `
        <div class="camera-feed" id="camera-${cameraId}">
          <img id="video-${cameraId}" style="width: 100%; height: 100%; object-fit: cover;" />
          <div class="status-badge live-badge">LIVE</div>
          <div class="stream-status not-streaming" id="stream-status-${cameraId}">NOT STREAMING</div>
        </div>
        <div class="camera-info">
          <div class="camera-title">${cameraName}</div>
          <div class="camera-meta">
            <div id="last-detection-${cameraId}">Last detection: Never</div>
            <div id="detection-count-${cameraId}">0 people detected</div>
          </div>
          <div class="camera-actions">
            <button class="action-btn expand-btn" onclick="expandCamera('${cameraName}', ${cameraId})">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="15 3 21 3 21 9"></polyline>
                <polyline points="9 21 3 21 3 15"></polyline>
                <line x1="21" y1="3" x2="14" y2="10"></line>
                <line x1="3" y1="21" x2="10" y2="14"></line>
              </svg>
              Expand
            </button>
            <button class="action-btn alert-btn">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
              </svg>
              Set Alert
            </button>
          </div>
        </div>
      `;
      return cameraCard;
    }

    // Expand functionality - exactly like crowd_detection
    function expandCamera(cameraId, cameraName) {
      console.log('Expanding camera:', cameraId, cameraName);

      // Hide normal view and show expanded view
      document.getElementById('normalView').classList.add('hidden');
      document.getElementById('expandedView').classList.add('active');

      // Update expanded view title and camera name
      document.getElementById('expandedCameraTitle').textContent = 'PPE Detection - Video Monitoring';
      document.getElementById('expandedCameraName').textContent = cameraName;

      // Store current expanded camera
      currentExpandedCamera = { id: cameraId, name: cameraName };

      // Update expanded view with existing data
      updateExpandedViewWithExistingData(cameraId);

      // Show the current video frame in expanded view
      showCurrentVideoInExpandedView(cameraId);

      // Navigate to the specific camera
      navigateToCamera(cameraName);
    }

    function backToNormal() {
      // Hide expanded view and show normal view
      document.getElementById('expandedView').classList.remove('active');
      document.getElementById('normalView').classList.remove('hidden');

      // Clear current expanded camera
      currentExpandedCamera = null;

      // Navigate back to all cameras
      navigateToAllCameras();
    }

    // Navigation functions
    async function navigateToCamera(cameraName) {
      try {
        const response = await fetch(`/helmet_detection/select-camera/${encodeURIComponent(cameraName)}`, {
          method: 'POST'
        });
        if (!response.ok) {
          throw new Error('Failed to select camera');
        }
      } catch (error) {
        console.error('Error selecting camera:', error);
        showNotification('Failed to select camera', 'error');
      }
    }

    async function navigateToAllCameras() {
      try {
        const response = await fetch('/helmet_detection/select-all-cameras', {
          method: 'POST'
        });
        if (!response.ok) {
          throw new Error('Failed to select all cameras');
        }
      } catch (error) {
        console.error('Error selecting all cameras:', error);
        showNotification('Failed to select all cameras', 'error');
      }
    }







    // Stream control functions
    async function startStream() {
      if (isStreaming) return;

      try {
        startBtn.disabled = true;
        startBtn.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="fa-spin">
            <circle cx="12" cy="12" r="10"></circle>
            <path d="M16 12h2"></path>
            <path d="M8 12h2"></path>
            <path d="M12 16v2"></path>
            <path d="M12 8v2"></path>
          </svg>
          Starting...
        `;

        const response = await fetch('/helmet_detection/start-stream', {
          method: 'POST'
        });

        if (response.ok) {
          console.log("Stream started successfully");
          startWebSocketFeed();

          // Update status indicators for all cameras
          const cameras = document.querySelectorAll('[id^="stream-status-"]');
          cameras.forEach(statusElement => {
            statusElement.textContent = 'STREAMING';
            statusElement.classList.remove('not-streaming');
            statusElement.classList.add('streaming');
          });

          isStreaming = true;
          sessionStorage.setItem("ppeStreaming", "true");
          startBtn.disabled = false;
          startBtn.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"></path>
            </svg>
            Monitoring Active
          `;
          showNotification("Stream started successfully", "success");
        } else {
          console.error("Failed to start stream");
          showNotification('Failed to start streaming', 'error');
          startBtn.disabled = false;
          startBtn.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polygon points="5 3 19 12 5 21 5 3"></polygon>
            </svg>
            Start Monitoring
          `;
        }
      } catch (error) {
        console.error("Error starting stream:", error);
        showNotification('Network error while starting stream', 'error');
        startBtn.disabled = false;
        startBtn.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polygon points="5 3 19 12 5 21 5 3"></polygon>
          </svg>
          Start Monitoring
        `;
      }
    }

    async function stopStream() {
      if (!isStreaming) return;

      try {
        stopBtn.disabled = true;
        stopBtn.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="fa-spin">
            <circle cx="12" cy="12" r="10"></circle>
            <path d="M16 12h2"></path>
            <path d="M8 12h2"></path>
            <path d="M12 16v2"></path>
            <path d="M12 8v2"></path>
          </svg>
          Stopping...
        `;

        if (websocket) {
          websocket.close(); // Close the WebSocket connection
        }

        const response = await fetch('/helmet_detection/stop-stream', {
          method: 'POST'
        });

        if (response.ok) {
          console.log("Stream stopped successfully");

          // Clear video feeds and reset status indicators - like crowd_detection
          const cameras = document.querySelectorAll('[id^="video-"]');
          cameras.forEach(imgElement => {
            imgElement.src = '';
          });

          const statusElements = document.querySelectorAll('[id^="stream-status-"]');
          statusElements.forEach(statusElement => {
            statusElement.textContent = 'NOT STREAMING';
            statusElement.classList.remove('streaming');
            statusElement.classList.add('not-streaming');
          });

          isStreaming = false;
          sessionStorage.removeItem("ppeStreaming");

          // Reset stats
          document.getElementById('totalDetectionsValue').textContent = '0';
          document.getElementById('noHelmetValue').textContent = '0';
          document.getElementById('noVestValue').textContent = '0';
          document.getElementById('noGlovesValue').textContent = '0';

          // Clear latest camera data
          latestCameraData = {};

          showNotification("Stream stopped successfully", "success");
        } else {
          console.error("Failed to stop stream");
          showNotification('Failed to stop streaming', 'error');
        }

        stopBtn.disabled = false;
        stopBtn.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <rect x="6" y="4" width="4" height="16"></rect>
            <rect x="14" y="4" width="4" height="16"></rect>
          </svg>
          Stop Monitoring
        `;
        startBtn.disabled = false;
        startBtn.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polygon points="5 3 19 12 5 21 5 3"></polygon>
          </svg>
          Start Monitoring
        `;
      } catch (error) {
        console.error("Error stopping stream:", error);
        showNotification('Network error while stopping stream', 'error');
        stopBtn.disabled = false;
        stopBtn.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <rect x="6" y="4" width="4" height="16"></rect>
            <rect x="14" y="4" width="4" height="16"></rect>
          </svg>
          Stop Monitoring
        `;
        startBtn.disabled = false;
        startBtn.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polygon points="5 3 19 12 5 21 5 3"></polygon>
          </svg>
          Start Monitoring
        `;
      }
    }

    function resetUI() {
      // Reset statistics
      document.getElementById('totalDetectionsValue').textContent = '0';
      document.getElementById('noHelmetValue').textContent = '0';
      document.getElementById('noVestValue').textContent = '0';
      document.getElementById('noGlovesValue').textContent = '0';

      // Reset expanded view
      if (currentExpandedCamera) {
        document.getElementById('total-count-1').textContent = '0';
        document.getElementById('no-helmet-count-1').textContent = '0';
        document.getElementById('no-vest-count-1').textContent = '0';
        document.getElementById('no-gloves-count-1').textContent = '0';
      }

      // Reset camera feeds
      Object.keys(availableCameras).forEach((cameraName, index) => {
        const videoElement = document.getElementById(`video-${index}`);
        const statusElement = document.getElementById(`stream-status-${index}`);
        const detectionElement = document.getElementById(`detection-count-${index}`);
        const lastDetectionElement = document.getElementById(`last-detection-${index}`);

        if (videoElement) videoElement.src = '';
        if (statusElement) {
          statusElement.textContent = 'NOT STREAMING';
          statusElement.className = 'stream-status not-streaming';
        }
        if (detectionElement) detectionElement.textContent = '0 people detected';
        if (lastDetectionElement) lastDetectionElement.textContent = 'Last detection: Never';
      });

      // Clear expanded video
      const expandedVideo = document.getElementById('expanded-video-feed');
      const placeholder = document.getElementById('webcam-placeholder');
      if (expandedVideo && placeholder) {
        expandedVideo.style.display = 'none';
        placeholder.style.display = 'flex';
      }

      // Clear latest camera data
      latestCameraData = {};
    }

    // WebSocket functions - exactly like crowd_detection but for PPE detection
    function startWebSocketFeed() {
      websocket = new WebSocket("ws://localhost:8000/helmet_detection/ws");
      websocket.binaryType = 'arraybuffer';

      let lastMetadata = null;

      websocket.onopen = () => {
        console.log("WebSocket connection established");
        showNotification("WebSocket connection established", "success");
      };

      websocket.onmessage = (event) => {
        if (typeof event.data === 'string') {
          lastMetadata = event.data.split(':');
        } else if (event.data instanceof ArrayBuffer) {
          if (lastMetadata) {
            const [cameraId, cam_name, personCount, noHelmetCount, noVestCount, noGlovesCount] = lastMetadata;

            // Store latest data for this camera
            latestCameraData[cameraId] = {
              cam_name: cam_name,
              personCount: parseInt(personCount) || 0,
              noHelmetCount: parseInt(noHelmetCount) || 0,
              noVestCount: parseInt(noVestCount) || 0,
              noGlovesCount: parseInt(noGlovesCount) || 0,
              timestamp: new Date()
            };

            // Convert ArrayBuffer to Blob and create URL
            const blob = new Blob([event.data], { type: 'image/jpeg' });
            const url = URL.createObjectURL(blob);

            // Update video feed
            const imgElement = document.getElementById(`video-${cameraId}`);
            if (imgElement) {
              imgElement.src = url;

              // Update camera info
              const cameraDetectionCount = document.querySelector(`#camera-${cameraId} + .camera-info .detection-count`);
              const cameraLastDetection = document.querySelector(`#camera-${cameraId} + .camera-info .camera-last-detection`);

              if (cameraDetectionCount) {
                cameraDetectionCount.textContent = `${personCount} people detected`;
              }

              if (cameraLastDetection) {
                cameraLastDetection.textContent = `Last detection: just now`;
              }

              // Update status to show we're streaming
              const statusElement = document.getElementById(`stream-status-${cameraId}`);
              if (statusElement) {
                statusElement.textContent = 'STREAMING';
                statusElement.classList.remove('not-streaming');
                statusElement.classList.add('streaming');
              }
            }

            // Update expanded view if this camera is currently expanded
            if (currentExpandedCamera && currentExpandedCamera.id === cameraId) {
              updateExpandedView(url, personCount, noHelmetCount, noVestCount, noGlovesCount);
            }

            // Update global stats
            updateGlobalPPEStats();

            lastMetadata = null;
          }
        }
      };

      websocket.onerror = (error) => {
        console.error("WebSocket error:", error);
        showNotification("WebSocket connection error", "error");
      };

      websocket.onclose = () => {
        console.log("WebSocket closed");
      };
    }

    function disconnectWebSocket() {
      if (websocket) {
        websocket.close();
        websocket = null;
      }
    }

    function handleWebSocketMessage(data) {
      if (data.type === 'video_frame') {
        updateVideoFeed(data.camera_name, data.image_url, data.metadata);
      } else if (data.type === 'detection_data') {
        updateDetectionData(data.camera_name, data.data);
      }
    }

    function updateVideoFeed(cameraName, imageUrl, metadata) {
      // Update camera grid video
      const cameraIndex = Object.keys(availableCameras).indexOf(cameraName);
      if (cameraIndex !== -1) {
        const videoElement = document.getElementById(`video-${cameraIndex}`);
        const statusElement = document.getElementById(`stream-status-${cameraIndex}`);

        if (videoElement) {
          videoElement.src = imageUrl;
        }

        if (statusElement) {
          statusElement.textContent = 'STREAMING';
          statusElement.className = 'stream-status streaming';
        }
      }

      // Update expanded view if this is the current camera
      if (currentExpandedCamera === cameraName) {
        const expandedVideo = document.getElementById('expanded-video-feed');
        const placeholder = document.getElementById('webcam-placeholder');

        if (expandedVideo && placeholder) {
          expandedVideo.src = imageUrl;
          expandedVideo.style.display = 'block';
          placeholder.style.display = 'none';
        }
      }

      // Update detection data if provided
      if (metadata) {
        updateDetectionData(cameraName, metadata);
      }
    }

    function updateDetectionData(cameraName, data) {
      // Store latest data
      latestCameraData[cameraName] = {
        personCount: data.person_count || 0,
        noHelmetCount: data.no_helmet_count || 0,
        noVestCount: data.no_vest_count || 0,
        noGlovesCount: data.no_gloves_count || 0,
        timestamp: new Date()
      };

      // Update camera card
      const cameraIndex = Object.keys(availableCameras).indexOf(cameraName);
      if (cameraIndex !== -1) {
        const detectionElement = document.getElementById(`detection-count-${cameraIndex}`);
        const lastDetectionElement = document.getElementById(`last-detection-${cameraIndex}`);

        if (detectionElement) {
          detectionElement.textContent = `${data.person_count || 0} people detected`;
        }

        if (lastDetectionElement) {
          const timestamp = new Date().toLocaleTimeString();
          lastDetectionElement.textContent = `Last detection: ${timestamp}`;
        }
      }

      // Update expanded view if this is the current camera
      if (currentExpandedCamera === cameraName) {
        updateExpandedView(data);
      }

      // Update global statistics
      updateGlobalStats();
    }

    function updateExpandedView(data) {
      document.getElementById('total-count-1').textContent = data.person_count || 0;
      document.getElementById('no-helmet-count-1').textContent = data.no_helmet_count || 0;
      document.getElementById('no-vest-count-1').textContent = data.no_vest_count || 0;
      document.getElementById('no-gloves-count-1').textContent = data.no_gloves_count || 0;

      const timestamp = new Date().toLocaleTimeString();
      document.getElementById('expandedLastUpdate').textContent = `Updated at ${timestamp}`;
    }

    function updateExpandedViewWithExistingData(cameraName) {
      const cameraData = latestCameraData[cameraName];

      if (cameraData) {
        document.getElementById('total-count-1').textContent = cameraData.personCount;
        document.getElementById('no-helmet-count-1').textContent = cameraData.noHelmetCount;
        document.getElementById('no-vest-count-1').textContent = cameraData.noVestCount;
        document.getElementById('no-gloves-count-1').textContent = cameraData.noGlovesCount;

        const timestamp = cameraData.timestamp.toLocaleTimeString();
        document.getElementById('expandedLastUpdate').textContent = `Updated at ${timestamp}`;
      } else {
        // Set default values
        document.getElementById('total-count-1').textContent = '0';
        document.getElementById('no-helmet-count-1').textContent = '0';
        document.getElementById('no-vest-count-1').textContent = '0';
        document.getElementById('no-gloves-count-1').textContent = '0';
        document.getElementById('expandedLastUpdate').textContent = 'No data available';
      }
    }

    function updateGlobalPPEStats() {
      let totalPeople = 0;
      let totalNoHelmet = 0;
      let totalNoVest = 0;
      let totalNoGloves = 0;

      // Sum up stats from all cameras
      Object.values(latestCameraData).forEach(data => {
        totalPeople += data.personCount;
        totalNoHelmet += data.noHelmetCount;
        totalNoVest += data.noVestCount;
        totalNoGloves += data.noGlovesCount;
      });

      // Update the display
      document.getElementById('totalDetectionsValue').textContent = totalPeople;
      document.getElementById('noHelmetValue').textContent = totalNoHelmet;
      document.getElementById('noVestValue').textContent = totalNoVest;
      document.getElementById('noGlovesValue').textContent = totalNoGloves;
    }

    function updateExpandedViewWithPPEData(url, personCount, noHelmetCount, noVestCount, noGlovesCount) {
      // Update expanded video feed
      const expandedVideo = document.getElementById('expanded-video-feed');
      const placeholder = document.getElementById('webcam-placeholder');

      if (expandedVideo && placeholder) {
        expandedVideo.src = url;
        expandedVideo.style.display = 'block';
        placeholder.style.display = 'none';
      }

      // Update PPE statistics in expanded view
      document.getElementById('total-count-1').textContent = personCount;
      document.getElementById('no-helmet-count-1').textContent = noHelmetCount;
      document.getElementById('no-vest-count-1').textContent = noVestCount;
      document.getElementById('no-gloves-count-1').textContent = noGlovesCount;

      const timestamp = new Date().toLocaleTimeString();
      document.getElementById('expandedLastUpdate').textContent = `Updated at ${timestamp}`;
    }

    // Function to update expanded view with existing data - like crowd_detection
    function updateExpandedViewWithExistingData(cameraId) {
      const cameraData = latestCameraData[cameraId];

      if (cameraData) {
        document.getElementById('total-count-1').textContent = cameraData.personCount || "0";
        document.getElementById('no-helmet-count-1').textContent = cameraData.noHelmetCount || "0";
        document.getElementById('no-vest-count-1').textContent = cameraData.noVestCount || "0";
        document.getElementById('no-gloves-count-1').textContent = cameraData.noGlovesCount || "0";

        const timestamp = new Date().toLocaleTimeString();
        document.getElementById('expandedLastUpdate').textContent = `Updated at ${timestamp}`;
      } else {
        // Set default values if no data available
        document.getElementById('total-count-1').textContent = "0";
        document.getElementById('no-helmet-count-1').textContent = "0";
        document.getElementById('no-vest-count-1').textContent = "0";
        document.getElementById('no-gloves-count-1').textContent = "0";
        document.getElementById('expandedLastUpdate').textContent = "No data available";
      }
    }

    // Function to show current video frame in expanded view - like crowd_detection
    function showCurrentVideoInExpandedView(cameraId) {
      const normalVideoElement = document.getElementById(`video-${cameraId}`);
      const expandedVideoElement = document.getElementById("expanded-video-feed");
      const placeholder = document.getElementById("webcam-placeholder");

      if (normalVideoElement && normalVideoElement.src) {
        // Copy the current frame from normal view to expanded view
        expandedVideoElement.src = normalVideoElement.src;
        expandedVideoElement.style.display = "block";
        placeholder.style.display = "none";
      } else {
        // Show placeholder if no video available
        expandedVideoElement.style.display = "none";
        placeholder.style.display = "flex";
      }
    }

    // Utility functions
    function showNotification(message, type = 'info') {
      // Create notification element
      const notification = document.createElement('div');
      notification.className = `notification notification-${type}`;
      notification.textContent = message;

      // Style the notification
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 6px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
      `;

      // Set background color based on type
      switch (type) {
        case 'success':
          notification.style.backgroundColor = '#4CAF50';
          break;
        case 'error':
          notification.style.backgroundColor = '#f44336';
          break;
        case 'warning':
          notification.style.backgroundColor = '#ff9800';
          break;
        default:
          notification.style.backgroundColor = '#2196F3';
      }

      // Add to page
      document.body.appendChild(notification);

      // Animate in
      setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
      }, 100);

      // Remove after 3 seconds
      setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
          if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
          }
        }, 300);
      }, 3000);
    }

    // Add camera management functionality - exactly like crowd_detection
    document.addEventListener('DOMContentLoaded', function() {
      // Create modal element for camera management
      const modalHTML = `
        <div id="manageCameraModal" class="modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.4);">
          <div class="modal-content" style="background-color: #fefefe; margin: 5% auto; padding: 20px; border-radius: 10px; width: 50%; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);">
            <span class="close-btn" id="closeModalBtn" style="color: #aaa; float: right; font-size: 28px; font-weight: bold; cursor: pointer;">&times;</span>
            <h2><i class="fas fa-video" style="margin-right: 10px;"></i>Add RTSP Camera</h2>
            <form id="cameraForm">
              <div style="margin-bottom: 15px;">
                <label for="cameraName" style="display: block; margin-bottom: 5px; font-weight: 500;">Camera Name (Unique):</label>
                <input type="text" id="cameraName" name="cameraName" placeholder="Enter camera name" required style="width: 100%; padding: 8px; border-radius: 5px; border: 1px solid #ddd;">
              </div>

              <div style="margin-bottom: 20px;">
                <label for="rtspUrl" style="display: block; margin-bottom: 5px; font-weight: 500;">RTSP URL:</label>
                <input type="text" id="rtspUrl" name="rtspUrl" placeholder="rtsp://" required style="width: 100%; padding: 8px; border-radius: 5px; border: 1px solid #ddd;">
              </div>

              <div style="display: flex; gap: 10px;">
                <button type="submit" style="background-color: var(--primary); color: white; border: none; padding: 10px 15px; border-radius: 5px; cursor: pointer;">Add Camera</button>
                <button type="button" onclick="toggleCameraList()" style="background-color: transparent; border: 1px solid var(--primary); color: var(--primary); padding: 10px 15px; border-radius: 5px; cursor: pointer;">
                  <i class="fas fa-list" style="margin-right: 5px;"></i> Show Cameras
                </button>
              </div>

              <div style="display: none; margin-top: 20px; padding-top: 15px; border-top: 1px solid #eee;" id="cameraList">
                <!-- Cameras will be listed here -->
              </div>
            </form>
          </div>
        </div>
      `;

      // Add modal to body
      document.body.insertAdjacentHTML('beforeend', modalHTML);

      // Setup modal functionality
      const manageCameraBtn = document.getElementById('manageCameraBtn');
      const modal = document.getElementById('manageCameraModal');
      const closeModalBtn = document.getElementById('closeModalBtn');
      const cameraForm = document.getElementById('cameraForm');

      // Show modal when camera management button is clicked
      if (manageCameraBtn) {
        manageCameraBtn.addEventListener('click', () => {
          modal.style.display = "block";
          loadCameras();
        });
      }

      // Close modal when X is clicked
      if (closeModalBtn) {
        closeModalBtn.addEventListener('click', () => {
          modal.style.display = "none";
        });
      }

      // Close modal when clicking outside
      window.addEventListener('click', (event) => {
        if (event.target === modal) {
          modal.style.display = "none";
        }
      });

      // Handle form submission
      if (cameraForm) {
        cameraForm.addEventListener('submit', async (e) => {
          e.preventDefault();

          const cameraName = document.getElementById('cameraName').value;
          const rtspUrl = document.getElementById('rtspUrl').value;

          try {
            const response = await fetch('/helmet_detection/add-camera', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ cameraName, rtspUrl }),
            });

            const result = await response.json();

            if (result.status === 'success') {
              showNotification('Camera added successfully!', 'success');
              cameraForm.reset();
              loadCameras();
              loadCameraCards();
            } else if (result.status === 'error') {
              showNotification(`RTSP URL already exists for camera: ${result.message.split(': ')[1]}`, 'error');
            } else if (result.status === 'samename') {
              showNotification('Camera name already exists', 'error');
            } else {
              showNotification('Error adding camera.', 'error');
            }
          } catch (error) {
            showNotification('Network error. Please try again.', 'error');
            console.error('Error:', error);
          }
        });
      }
    });

    // Camera Management Functions



    function openAlertModal() {
      showNotification('Alert management feature coming soon', 'info');
    }

    // Function to load and display the list of cameras - exactly like crowd_detection
    async function loadCameras() {
      try {
        const response = await fetch("/helmet_detection/get-cameras", {
          method: "GET",
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const cameras = await response.json();
        const cameraList = document.getElementById("cameraList");

        if (!cameraList) return;

        cameraList.innerHTML = ""; // Clear previous list

        if (Object.keys(cameras).length === 0) {
          cameraList.innerHTML = "<p style='text-align: center; color: #777;'>No cameras added yet</p>";
          return;
        }

        for (const [cameraName, rtspUrl] of Object.entries(cameras)) {
          const cameraItem = document.createElement("div");
          cameraItem.style.padding = "10px";
          cameraItem.style.borderBottom = "1px solid #eee";
          cameraItem.style.display = "flex";
          cameraItem.style.justifyContent = "space-between";
          cameraItem.style.alignItems = "center";

          cameraItem.innerHTML = `
            <div>
              <p style="margin: 0; font-weight: 500;">
                <i class="fas fa-video fa-fw" style="margin-right: 8px; color: var(--primary);"></i>
                ${cameraName}
              </p>
              <p style="margin: 5px 0 0 0; font-size: 12px; color: #777;">${rtspUrl[0]}</p>
            </div>
            <button onclick="deleteCamera('${cameraName}')" style="background-color: #f44336; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer;">
              <i class="fas fa-trash"></i>
            </button>
          `;

          cameraList.appendChild(cameraItem);
        }
      } catch (error) {
        console.error("Error loading cameras:", error);
        showNotification("Error loading camera list", "error");
      }
    }

    // Function to delete a camera - exactly like crowd_detection
    async function deleteCamera(cameraName) {
      const confirmed = confirm(`Are you sure you want to delete the camera "${cameraName}"?`);
      if (!confirmed) return;

      try {
        const response = await fetch(`/helmet_detection/delete-camera/${cameraName}`, {
          method: "DELETE",
        });

        const result = await response.json();

        if (result.status === "success") {
          showNotification(result.message, 'success');
          loadCameras();
          loadCameraCards();
        } else {
          showNotification("Error deleting camera: " + result.message, 'error');
        }
      } catch (error) {
        console.error("Error deleting camera:", error);
        showNotification("Network error while deleting camera", "error");
      }
    }

    // Function to toggle camera list in the modal - exactly like crowd_detection
    function toggleCameraList() {
      const cameraList = document.getElementById('cameraList');
      if (cameraList) {
        if (cameraList.style.display === 'none') {
          cameraList.style.display = 'block';
          loadCameras();
        } else {
          cameraList.style.display = 'none';
        }
      }
    }

    // Make functions globally available
    window.expandCamera = expandCamera;
    window.backToNormal = backToNormal;
    window.deleteCamera = deleteCamera;
    window.toggleCameraList = toggleCameraList;
  </script>
</body>
</html>
