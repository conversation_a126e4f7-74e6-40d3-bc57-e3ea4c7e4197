import cv2
from threading import Thread
from face_detection import FaceDetector
from face_tracker import FaceTracker

# class CameraWorker(Thread):
#     def __init__(self, camera_id, detector, tracker):
#         super().__init__()
#         self.camera_id = camera_id
#         self.detector = detector
#         self.tracker = tracker
#         self.running = True

#     def run(self):
#         cap = cv2.VideoCapture(self.camera_id)

#         if not cap.isOpened():
#             print(f"Error: Could not open camera {self.camera_id}")
#             return
#         print(f"Camera {self.camera_id} started.")

#         try:
#            while self.running:
#                 ret, frame = cap.read()
#                 if not ret:
#                     print(f"Error: Could not read frame from camera {self.camera_id}")
#                     break

#                 detections = self.detector.detect_faces(frame)
#                 tracked_faces = self.tracker.track_faces(detections, frame)

#                 for tracked_face in tracked_faces:
#                     x1, y1, x2, y2 = map(int, tracked_face["bbox"])
#                     track_id = tracked_face["id"]
#                     name = tracked_face["name"]

#                     cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
#                     cv2.putText(frame, f"ID: {track_id} ({name})", (x1, y1 - 10),
#                                 cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

#                 # Display the frame
#                 cv2.imshow(f"Camera {self.camera_id}", frame)

#                 # Stop if 'q' is pressed
#                 if cv2.waitKey(1) & 0xFF == ord('q'):
#                     self.running = False
#                     break
#         finally:
#             cap.release()
#             cv2.destroyAllWindows()
#             print(f"Camera {self.camera_id} stopped.")

# def main():
#     detector = FaceDetector()
#     tracker = FaceTracker()

#     # Define camera IDs (e.g., 0, 1 for two cameras)
#     camera_ids = [0, 1]
#     print("hi")  # Adjust this based on your setup

#     # Start a thread for each camera
#     workers = []
#     for camera_id in camera_ids:
#         worker = CameraWorker(camera_id, detector, tracker)
#         worker.start()
#         workers.append(worker)
#     print(workers)
#     # Wait for all threads to finish
#     try:
#         for worker in workers:
#             worker.join()
#     except KeyboardInterrupt:
#         print("Stopping all cameras...")
#         for worker in workers:
#             worker.running = False


# if __name__ == "__main__":
#     main()
 

 