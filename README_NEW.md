# 🔐 Vigilant Eye - AI-Powered Monitoring System

A comprehensive surveillance and monitoring system with **separate authentication service** and multiple AI-powered detection modules.

## ✨ Features

- **🔐 Separate Authentication Service**: Secure login system with beautiful UI
- **👤 Face Recognition**: Advanced facial recognition for identity verification  
- **🦺 PPE Detection**: Helmet/safety equipment compliance monitoring
- **👥 Crowd Detection**: Real-time crowd monitoring and density analysis
- **🔍 Quality Control**: Automated quality inspection and defect detection

## 🚀 Quick Start

### **Option 1: Complete System (Recommended)**
```bash
# Start both Authentication + Backend services
start_services.bat
```

### **Option 2: Backend Only (Development)**
```bash
cd Backend
venv\scripts\activate
uvicorn app.main:app --reload
```

### **Option 3: Manual Setup**
```bash
# Terminal 1 - Authentication Service
cd Authentication
python start.py

# Terminal 2 - Backend Services  
cd Backend
venv\scripts\activate
uvicorn app.main:app --reload --host 127.0.0.1 --port 8000
```

## 🌐 Access URLs

| Service | URL | Description |
|---------|-----|-------------|
| **🔐 Authentication** | `http://localhost:8001` | **START HERE** - Login page |
| **⚙️ Backend Services** | `http://127.0.0.1:8000` | Direct API access |
| **📊 Health Check** | `http://localhost:8001/health` | Service status |

## 🔑 Default Credentials

- **Username**: `admin`
- **Password**: `admin123`

## 📋 Complete User Flow

1. **🔐 Login**: Go to `http://localhost:8001`
2. **🏠 Dashboard**: After login, choose from 4 services
3. **🚀 Launch Service**: Click any service card to open in new tab
4. **🔄 Switch Services**: Return to dashboard to access other modules

## 🏗️ Architecture

```
VigilantEye UI/
├── 🔐 Authentication/          # Separate Authentication Service (Port 8001)
│   ├── templates/
│   │   ├── login.html         # Beautiful login page
│   │   └── dashboard.html     # Service selection dashboard
│   ├── main.py               # FastAPI authentication server
│   ├── auth.py               # JWT authentication logic
│   ├── database.py           # User database management
│   └── start.py              # Auto-setup with default user
│
├── ⚙️ Backend/                 # Clean Backend Services (Port 8000)
│   ├── app/
│   │   ├── main.py           # Clean FastAPI app (no auth)
│   │   ├── templates/
│   │   │   └── home.html     # Main dashboard
│   │   ├── 👤 face_recognition/
│   │   ├── 🦺 helmet_detection/
│   │   ├── 👥 crowd_detection/
│   │   └── 🔍 quality_control/
│   ├── venv/                 # Python virtual environment
│   └── start_backend.bat     # Easy backend startup
│
├── 🚀 start_services.bat      # Complete system startup
└── 📖 README.md              # This file
```

## 🎯 Key Benefits

- ✅ **Separate Authentication**: Clean architecture with isolated auth service
- ✅ **Beautiful Login UI**: Modern, responsive design with animations
- ✅ **No Breaking Changes**: Backend works exactly as before
- ✅ **Easy Development**: Run Backend independently for development
- ✅ **Secure**: JWT-based authentication with session management
- ✅ **Scalable**: Services can be deployed independently

## 🔧 Services Overview

### 🔐 Authentication Service (Port 8001)
- **Purpose**: User login and service selection
- **Features**: JWT authentication, session management, beautiful UI
- **Database**: Shared MySQL (`vigilanteye`)
- **Default User**: admin/admin123

### 👤 Face Recognition
- Real-time face detection and recognition
- User management and registration  
- Access control and monitoring

### 🦺 PPE Detection (Helmet Detection)
- Personal protective equipment detection
- Safety compliance monitoring
- Real-time alerts for violations

### 👥 Crowd Detection  
- People counting and density analysis
- Crowd flow monitoring
- Safety threshold alerts

### 🔍 Quality Control
- Automated defect detection
- Product quality inspection
- Statistical analysis and reporting

## 🛠️ Technical Requirements

- **Python**: 3.8+
- **Database**: MySQL (shared between services)
- **Frontend**: FastAPI + Jinja2 templates
- **Authentication**: JWT tokens with secure cookies
- **AI/ML**: OpenCV, TensorFlow, YOLO

## 🔒 Security Features

- JWT-based authentication
- Secure HTTP-only cookies
- Session management (1 day / 30 days with "Remember Me")
- Password hashing with bcrypt
- CORS protection

## 🚨 Troubleshooting

### Services Won't Start
```bash
# Check if ports are available
netstat -an | findstr :8001
netstat -an | findstr :8000

# Kill existing processes if needed
taskkill /f /im python.exe
taskkill /f /im uvicorn.exe
```

### Database Issues
```bash
# Reset authentication database
cd Authentication
python start.py  # Will recreate tables and default user
```

### Backend Issues
```bash
# Fix dependencies
cd Backend
call fix_dependencies.bat
```

## 📞 Support

For issues or questions:
1. Check the service health endpoints
2. Review the console output for errors
3. Ensure all dependencies are installed
4. Verify database connectivity

## 📄 License

This project is licensed under the MIT License.
