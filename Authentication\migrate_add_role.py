#!/usr/bin/env python3
"""
Database migration script to add role column to users table
"""

import os
import sys
from sqlalchemy import create_engine, text
from database import DATABASE_URL, engine

def migrate_add_role():
    """Add role column to users table if it doesn't exist"""
    try:
        print("🔄 Checking if role column exists...")
        
        # Check if role column exists
        with engine.connect() as conn:
            # Try to select role column
            try:
                result = conn.execute(text("SELECT role FROM users LIMIT 1"))
                print("✅ Role column already exists!")
                return True
            except Exception as e:
                if "Unknown column" in str(e) or "no such column" in str(e):
                    print("📝 Role column doesn't exist, adding it...")
                else:
                    print(f"❌ Error checking role column: {e}")
                    return False
        
        # Add role column
        with engine.connect() as conn:
            # Add role column with default value
            conn.execute(text("ALTER TABLE users ADD COLUMN role VARCHAR(20) DEFAULT 'viewer'"))
            conn.commit()
            print("✅ Role column added successfully!")
            
            # Update existing admin user to have admin role
            result = conn.execute(text("UPDATE users SET role = 'admin' WHERE username = 'admin'"))
            conn.commit()
            if result.rowcount > 0:
                print("✅ Updated admin user role to 'admin'")
            
            print("🎉 Migration completed successfully!")
            return True
            
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🔧 Database Migration: Add Role Column")
    print("=" * 60)
    
    success = migrate_add_role()
    
    if success:
        print("\n✅ Migration completed successfully!")
        print("You can now start the authentication service.")
    else:
        print("\n❌ Migration failed!")
        print("Please check the error messages above.")
    
    print("=" * 60)
