import cv2
import threading
from ultralytics import YOLO
import numpy as np
import concurrent.futures



class Helmetdetection:
    def __init__(self, object_model,pose_model,conf_value):
        self.conf_value = conf_value
        self.object_model = <PERSON><PERSON><PERSON>(object_model)  
        self.pose_model = <PERSON><PERSON><PERSON>(pose_model)
        self.threads = []
        self.running = False
        self.blank = cv2.imread("./static/black.jpg")
        self.blank = cv2.resize(self.blank, (640, 640),interpolation=cv2.INTER_AREA)

    def start(self, camera_details, test_video_path=None):
        self.camera_name, self.rtsp_url, self.cap_devices = [], [], []
        dic = camera_details

        # If test_video_path is provided, override camera details
        if test_video_path:
            self.camera_name.append("Test Video")
            cap = cv2.VideoCapture(test_video_path)
            if cap.isOpened():
                self.cap_devices.append(cap)
            else:
                self.cap_devices.append(None)
        else:
            for key, value in dic.items():
                self.camera_name.append(key)
                if value[0].isdigit():
                    value = int(value[0])
                    self.rtsp_url.append(value)
                    cap = cv2.VideoCapture(value)
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    if cap.isOpened():
                        self.cap_devices.append(cap)
                    else:
                        self.cap_devices.append(None)
                else:
                    cap = cv2.VideoCapture(value[0],cv2.CAP_FFMPEG)
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    if cap.isOpened():
                        self.cap_devices.append(cap)
                    else:
                        self.cap_devices.append(None)

        self.warning_count = [None] * len(self.cap_devices)
        self.helmet_frames = [None] * len(self.cap_devices)

        if not self.running:
            self.running = True
            for idx, cap in enumerate(self.cap_devices):
                if cap is not None and cap.isOpened():
                    thread = threading.Thread(target=self.update, args=(idx, cap, test_video_path))
                    thread.daemon = True
                    thread.start()
                    self.threads.append(thread)
                else:
                    temp = cv2.putText(
                        self.blank,
                        f"{self.camera_name[idx]} is Offline",
                        (35, 170),
                        fontFace=cv2.FONT_HERSHEY_SIMPLEX,
                        fontScale=1,
                        thickness=3,
                        color=(255, 255, 255),
                    )
                    _, temp = cv2.imencode(".png", temp)
                    self.helmet_frames[idx] = temp.tobytes()

    def stop(self):
        self.running = False
        for cap in self.cap_devices:
            if cap is not None:
                cap.release()
        for thread in self.threads:
            thread.join()

    def check_PPE(self, objects, poses):  #['boots', 'gloves', 'helmet', 'person', 'safety-glass', 'vest']
        boot_class_id = 0  # Class ID for boots
        gloves_class_id = 1  # Class ID for gloves
        helmet_class_id = 2  # Class ID for helmet
        safetyglass_class_id = 4  # Class ID for safety-glass
        vest_class_id = 5  # Class ID for vest

        warnings = []
        
        for pose in poses:
            keypoints = pose["keypoints"] 
            # print(keypoints)
            if keypoints.shape[0] < 17: 
                warnings.append((False, False,False,False,False))  
                continue
            
            boot_points = keypoints[[15, 16]]  # Ankle keypoints
            # print("boot_points",boot_points)
            gloves_points = keypoints[[9, 10]]  # Wrist keypoints
            # print("gloves_points",gloves_points)
            safetyglass_points = keypoints[[1,2]]
            # print("safetyglass_point",safetyglass_point)
            head_points = keypoints[:5]  # Head keypoints
            # print("head_points",head_points)
            chest_points = keypoints[[5, 6, 11, 12]]  # Shoulder and hip keypoints 
            # print("chest_points",chest_points)


            # helmet_warning, vest_warning = False, False

            # # Check for helmet
            # for obj in objects:
            #     if obj["class_id"] == helmet_class_id:
            #         x1, y1, x2, y2 = obj["bbox"]
            #         if ((head_points[:, 0] >= x1) & (head_points[:, 0] <= x2) &
            #             (head_points[:, 1] >= y1) & (head_points[:, 1] <= y2)).any():
            #             helmet_warning = True
            #             break

            # # Check for vest
            # for obj in objects:
            #     if obj["class_id"] == vest_class_id:
            #         x1, y1, x2, y2 = obj["bbox"]
            #         if ((chest_points[:, 0] >= x1) & (chest_points[:, 0] <= x2) &
            #             (chest_points[:, 1] >= y1) & (chest_points[:, 1] <= y2)).any():
            #             vest_warning = True
            #             break

            helmet_warning, vest_warning, safetyglass_warning, boot_warning, gloves_warning = False, False, False, False, False
            p = 0 # Padding for bbox
            for obj in objects:
  
                if helmet_warning == False:
                    if obj["class_id"] == helmet_class_id:
                        x1, y1, x2, y2 = obj["bbox"]
                        x1, y1, x2, y2 = x1 - p, y1 - p, x2 + p, y2 + p
                        if ((head_points[:, 0] >= x1) & (head_points[:, 0] <= x2) &
                            (head_points[:, 1] >= y1) & (head_points[:, 1] <= y2)).any():
                            helmet_warning = True
                        print(head_points[:,0],"/n",head_points[:,1])
                if vest_warning == False:
                    if obj["class_id"] == vest_class_id:
                        x1, y1, x2, y2 = obj["bbox"]
                        if ((chest_points[:, 0] >= x1) & (chest_points[:, 0] <= x2) &
                            (chest_points[:, 1] >= y1) & (chest_points[:, 1] <= y2)).any():
                            vest_warning = True
                        

                if boot_warning == False:
                    if obj["class_id"] == boot_class_id:
                        x1, y1, x2, y2 = obj["bbox"]
                        if ((boot_points[:, 0] >= x1) & (boot_points[:, 0] <= x2) &
                            (boot_points[:, 1] >= y1) & (boot_points[:, 1] <= y2)).any():
                            boot_warning = True

                if gloves_warning == False:
                    if obj["class_id"] == gloves_class_id:
                        x1, y1, x2, y2 = obj["bbox"]
                        if ((gloves_points[:, 0] >= x1) & (gloves_points[:, 0] <= x2) &
                            (gloves_points[:, 1] >= y1) & (gloves_points[:, 1] <= y2)).any():
                            gloves_warning = True

                if safetyglass_warning == False:
                    if obj["class_id"] == safetyglass_class_id:
                        x1, y1, x2, y2 = obj["bbox"]
                        if ((safetyglass_points[:, 0] >= x1) & (safetyglass_points[:, 0] <= x2) &
                            (safetyglass_points[:, 1] >= y1) & (safetyglass_points[:, 1] <= y2)).any():
                            safetyglass_warning = True

                if helmet_warning and vest_warning and boot_warning and gloves_warning and safetyglass_warning:
                    break

            warnings.append((helmet_warning, vest_warning, safetyglass_warning, boot_warning, gloves_warning))
            # print(warnings)
        
        return warnings


    def process_frame(self, frame, object_model, pose_model):
        # Use a thread pool to run both model inferences concurrently.
        with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
            future_obj = executor.submit(object_model, frame, stream=True)
            future_pose = executor.submit(pose_model, frame, stream=True)
            object_results = future_obj.result()
            pose_results = future_pose.result()

        objects = []
        poses = []

        # Process object detections
        for object_result in object_results:
            for det in object_result.boxes:
                class_id = int(det.cls)
                score = float(det.conf)
                if score > self.conf_value:
                    x1, y1, x2, y2 = map(int, det.xyxy[0])
                    # cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)  # Green rectangle with thickness 2
                    # cv2.putText(frame, f'Class: {class_id}', (x1, y1 - 10),
                    #             cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
                    objects.append({"class_id": class_id, "bbox": (x1, y1, x2, y2), "score": score})
                    
        # Process pose detections (if keypoints exist)
        for pose_result in pose_results:
            if pose_result.keypoints is not None:
                for person, box in zip(pose_result.keypoints, pose_result.boxes):
                    conf = float(box.conf)
                    if conf > self.conf_value:
                        x1, y1, x2, y2 = map(int, box.xyxy[0])
                        keypoints = person.xy.cpu().numpy()[0]
                        poses.append({"keypoints": keypoints, "bbox": (x1, y1, x2, y2)})

        return objects, poses

    def update(self, idx, cap, test_video_path=None):
        frame_counter = 0
        skip_frames = 2
        while self.running:
            ret, frame = cap.read()
            
            # If video is finished, loop it for testing
            if not ret and test_video_path:
                cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # Restart the video
                continue
            elif not ret:
                break

            frame_counter += 1
            if frame_counter % skip_frames != 0:
                continue

            frame = cv2.resize(frame, (640, 640))

            objects, poses = self.process_frame(frame, object_model=self.object_model, pose_model=self.pose_model)
            warnings = self.check_PPE(objects=objects, poses=poses)

            for pose, (helmet_status, vest_status, safetyglass_status, boot_status, gloves_status) in zip(poses, warnings):
                helmet_label = "Helmet Worn" if helmet_status else "No Helmet"
                vest_label = "Vest Worn" if vest_status else "No Vest"
                safetyglass_label = "Safety Glass Worn" if safetyglass_status else "No Safety Glass"
                gloves_label = "Gloves Worn" if gloves_status else "No Gloves"
                boot_label = "Boots Worn" if boot_status else "No Boots"

                
                helmet_color = (0, 255, 0) if helmet_status else (0, 0, 255)
                vest_color = (0, 255, 0) if vest_status else (0, 0, 255)
                safetyglass_color = (0, 255, 0) if safetyglass_status else (0, 0, 255)
                gloves_color = (0, 255, 0) if gloves_status else (0, 0, 255)
                boot_color = (0, 255, 0) if boot_status else (0, 0, 255)


                x1, y1, x2, y2 = pose["bbox"]

                # Display helmet status
                cv2.putText(frame, helmet_label, (x1, y1 - 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, helmet_color, 2)

                # Display vest status
                cv2.putText(frame, vest_label, (x1, y1), cv2.FONT_HERSHEY_SIMPLEX, 0.5, vest_color, 2)

                # Display safety glass status
                cv2.putText(frame, safetyglass_label, (x1, y1 + 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, safetyglass_color, 2)

                # Display gloves status
                cv2.putText(frame, gloves_label, (x1, y1 + 40), cv2.FONT_HERSHEY_SIMPLEX, 0.5, gloves_color, 2)

                # Display boots status
                cv2.putText(frame, boot_label, (x1, y1 + 60), cv2.FONT_HERSHEY_SIMPLEX, 0.5, boot_color, 2)

                
                # true_count = warnings.count(True)
                # false_count = warnings.count(False)
                # self.warning_count[idx] = [true_count, false_count]
                cv2.imshow("frame", frame)
                cv2.waitKey(1)


            _, buffer = cv2.imencode(".png", frame)
            self.helmet_frames[idx] = buffer.tobytes()
            

            if not self.running:
                break

        cv2.destroyAllWindows()



# import cv2
# import threading
# from ultralytics import YOLO
# import numpy as np
# import concurrent.futures



# class Helmetdetection:
#     def __init__(self, object_model,pose_model,conf_value):
#         self.conf_value = conf_value
#         self.object_model = YOLO(object_model)  
#         self.pose_model = YOLO(pose_model)
#         self.threads = []
#         self.running = False
#         self.blank = cv2.imread("./static/black.jpg")
#         self.blank = cv2.resize(self.blank, (480, 320),interpolation=cv2.INTER_AREA)

#     def start(self, camera_details, test_video_path=None):
#         self.camera_name, self.rtsp_url, self.cap_devices = [], [], []
#         dic = camera_details

#         # If test_video_path is provided, override camera details
#         if test_video_path:
#             self.camera_name.append("Test Video")
#             cap = cv2.VideoCapture(test_video_path)
#             if cap.isOpened():
#                 self.cap_devices.append(cap)
#             else:
#                 self.cap_devices.append(None)
#         else:
#             for key, value in dic.items():
#                 self.camera_name.append(key)
#                 if value[0].isdigit():
#                     value = int(value[0])
#                     self.rtsp_url.append(value)
#                     cap = cv2.VideoCapture(value)
#                     cap.set(cv2.CAP_PROP_BUFFERSIZE, 3)
#                     if cap.isOpened():
#                         self.cap_devices.append(cap)
#                     else:
#                         self.cap_devices.append(None)
#                 else:
#                     cap = cv2.VideoCapture(value[0])
#                     cap.set(cv2.CAP_PROP_BUFFERSIZE, 3)
#                     if cap.isOpened():
#                         self.cap_devices.append(cap)
#                     else:
#                         self.cap_devices.append(None)

#         self.warning_count = [None] * len(self.cap_devices)
#         self.helmet_frames = [None] * len(self.cap_devices)

#         if not self.running:
#             self.running = True
#             for idx, cap in enumerate(self.cap_devices):
#                 if cap is not None and cap.isOpened():
#                     thread = threading.Thread(target=self.update, args=(idx, cap, test_video_path))
#                     thread.daemon = True
#                     thread.start()
#                     self.threads.append(thread)
#                 else:
#                     temp = cv2.putText(
#                         self.blank,
#                         f"{self.camera_name[idx]} is Offline",
#                         (35, 170),
#                         fontFace=cv2.FONT_HERSHEY_SIMPLEX,
#                         fontScale=1,
#                         thickness=3,
#                         color=(255, 255, 255),
#                     )
#                     _, temp = cv2.imencode(".jpg", temp)
#                     self.helmet_frames[idx] = temp.tobytes()

#     def stop(self):
#         self.running = False
#         for cap in self.cap_devices:
#             if cap is not None:
#                 cap.release()
#         for thread in self.threads:
#             thread.join()

#     def check_helmet(self, objects, poses):
#         helmet_class_id = 0  # Class ID for helmet
#         warnings = []
#         # For each detected pose, check if any of the first five keypoints fall inside any helmet bbox.
#         for pose in poses:
#             keypoints = pose["keypoints"]  # assuming keypoints is already a numpy array
#             # Only take the first five keypoints
#             points = keypoints[:5]
#             warning_detected = False
#             for obj in objects:
#                 if obj["class_id"] == helmet_class_id:
#                     x1, y1, x2, y2 = obj["bbox"]
#                     # Use vectorized comparison instead of loop if possible:
#                     # For example, check if any point satisfies the condition.
#                     if ((points[:, 0] >= x1) & (points[:, 0] <= x2) &
#                         (points[:, 1] >= y1) & (points[:, 1] <= y2)).any():
#                         warning_detected = True
#                         break
#             warnings.append(warning_detected)
#         return warnings

#     def process_frame(self, frame, object_model, pose_model):
#         # Use a thread pool to run both model inferences concurrently.
#         with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
#             future_obj = executor.submit(object_model, frame, stream=True)
#             future_pose = executor.submit(pose_model, frame, stream=True)
#             object_results = future_obj.result()
#             pose_results = future_pose.result()

#         objects = []
#         poses = []

#         # Process object detections
#         for object_result in object_results:
#             for det in object_result.boxes:
#                 class_id = int(det.cls)
#                 score = float(det.conf)
#                 if score > self.conf_value:
#                     x1, y1, x2, y2 = map(int, det.xyxy[0])
#                     # cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)  # Green rectangle with thickness 2
#                     # cv2.putText(frame, f'Class: {class_id}', (x1, y1 - 10),
#                     #             cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
#                     objects.append({"class_id": class_id, "bbox": (x1, y1, x2, y2), "score": score})
                    
#         # Process pose detections (if keypoints exist)
#         for pose_result in pose_results:
#             if pose_result.keypoints is not None:
#                 for person, box in zip(pose_result.keypoints, pose_result.boxes):
#                     conf = float(box.conf)
#                     if conf > self.conf_value:
#                         x1, y1, x2, y2 = map(int, box.xyxy[0])
#                         keypoints = person.xy.cpu().numpy()[0]
#                         poses.append({"keypoints": keypoints, "bbox": (x1, y1, x2, y2)})

#         return objects, poses

#     def update(self, idx, cap, test_video_path=None):
#         frame_counter = 0
#         skip_frames = 2
#         while self.running:
#             ret, frame = cap.read()
            
#             # If video is finished, loop it for testing
#             if not ret and test_video_path:
#                 cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # Restart the video
#                 continue
#             elif not ret:
#                 break

#             frame_counter += 1
#             if frame_counter % skip_frames != 0:
#                 continue

#             frame = cv2.resize(frame, (480, 360))

#             objects, poses = self.process_frame(frame, object_model=self.object_model, pose_model=self.pose_model)
#             warnings = self.check_helmet(objects=objects, poses=poses)

#             for pose, warning in zip(poses, warnings):
#                 label = "Helmet Weared" if warning else "No Helmet"
#                 color = (0, 255, 0) if warning else (0, 0, 255)
#                 x1, y1, x2, y2 = pose["bbox"]
#                 # cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
#                 cv2.putText(frame, f"{label}", (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

#                 true_count = warnings.count(True)
#                 false_count = warnings.count(False)
#                 self.warning_count[idx] = [true_count, false_count]

#             _, buffer = cv2.imencode(".jpg", frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
#             self.helmet_frames[idx] = buffer.tobytes()

#             if not self.running:
#                 break



















# import cv2
# import threading
# import base64
# from ultralytics import YOLO
# import numpy as np


# class Helmetdetection:
#     def __init__(self, object_model,pose_model,conf_value):
#         self.conf_value = conf_value
#         self.object_model = YOLO(object_model)  
#         self.pose_model = YOLO(pose_model)
#         self.threads = []
#         self.running = False
#         self.blank = cv2.imread("./static/black.jpg")
#         self.blank = cv2.resize(self.blank, (480, 320),interpolation=cv2.INTER_AREA)

#     def start(self, camera_details):
#         self.camera_name = []
#         self.rtsp_url = []
#         self.cap_devices = []

#         dic = camera_details

#         for key,value in dic.items():
#                     self.camera_name.append(key)

#                     if value[0].isdigit():
#                         value = int(value[0])
#                         self.rtsp_url.append(value)
#                         cap = cv2.VideoCapture(value,cv2.CAP_FFMPEG)
#                         # Increase buffer size
#                         cap.set(cv2.CAP_PROP_BUFFERSIZE, 3)
#                         if cap.isOpened():
#                             self.cap_devices.append(cap)
#                         else:
#                             self.cap_devices.append(None)
                        
#                     else:
#                         cap = cv2.VideoCapture(value[0],cv2.CAP_FFMPEG)
#                         # Increase buffer size
#                         cap.set(cv2.CAP_PROP_BUFFERSIZE, 3)
#                         if cap and cap.isOpened():
#                             self.cap_devices.append(cap)  
#                         else:
#                             self.cap_devices.append(None)
        
#         self.warning_count = [None] * len(self.cap_devices)
#         self.helmet_frames = [None] * len(self.cap_devices)  # One frame for each camera
#         if not self.running:
#             self.running = True
#             for idx, cap in enumerate(self.cap_devices):
#                 if cap is not None and cap.isOpened():
#                     thread = threading.Thread(target=self.update, args=(idx, cap))
#                     thread.daemon = True
#                     thread.start()
#                     self.threads.append(thread)
#                 else:        #TO DISPLAY THE DEVICE IS OFFLINE
                    
#                     temp = cv2.putText(self.blank,f"{self.camera_name[idx]} is Offline",(35,170),fontFace=cv2.FONT_HERSHEY_SIMPLEX,fontScale=1,thickness=3,color=(255,255,255))
#                     _, temp = cv2.imencode('.jpg', temp)
#                     # self.helmet_frames[idx] = base64.b64encode(temp).decode('utf-8')
#                     self.helmet_frames[idx] = temp.tobytes()

#     def stop(self):
#         self.running = False
#         for cap in self.cap_devices:
#             if cap is not None:
#                 cap.release()
#         for thread in self.threads:
#             thread.join()

#     def check_helmet(self,objects, poses):
#         helmet_class_id = 0  # Class ID for helmet
#         warnings = []
#         for pose in poses:
#             keypoints = pose["keypoints"]
            
#             points = [keypoints[0],keypoints[1],keypoints[2],keypoints[3],keypoints[4],]
            
#             warning_detected = False
#             for obj in objects:
#                 if obj["class_id"] == helmet_class_id:
#                     x1,y1,x2,y2 = obj["bbox"]
#                     for point in points:
#                         a,b = np.array(point)
#                         if x2>=a>=x1 and y2>=b>=y1:
#                             warning_detected = True
#                             break 
                        
                
#             warnings.append(warning_detected)
    
#         return warnings

#     def process_frame(self,frame, object_model, pose_model):
#         # Detect objects using the YOLO object detection model
#         object_results = object_model(frame,stream=True)

#         # Detect pose using the YOLO pose model
#         pose_results = pose_model(frame,stream=True)



#         objects = []  # To store detected objects
#         poses = []  # To store detected poses (keypoints)


#         # # Extract detected objects
#         for object_result in object_results:
#             for det in object_result.boxes:
#                 class_id = int(det.cls)
#                 score = float(det.conf)
#                 if score > self.conf_value:
#                     x1, y1, x2, y2 = map(int, det.xyxy[0])
#                     objects.append({"class_id": class_id, "bbox": (x1, y1, x2, y2), "score": score})   #list of dictionary

#         # # Check if keypoints exist before processing
#         for pose_result in pose_results:
#             if pose_result.keypoints is not None:
#                 for person,box in zip(pose_result.keypoints,pose_result.boxes):
#                     conf = float(box.conf)
#                     if conf > self.conf_value:
#                         x1, y1, x2, y2 = map(int, box.xyxy[0])
#                         poses.append({"keypoints": person.xy.cpu().numpy()[0],"bbox": (x1,y1,x2,y2)})  #list of dictionary



#         return objects, poses

#     def update(self, idx, cap):
#         frame_counter = 0
#         skip_frames = 2
#         while self.running:
#             ret, frame = cap.read()
#             if not ret:
#                 break
#             frame_counter +=1
#             if frame_counter % skip_frames !=0:
#                 continue
 
#             frame = cv2.resize(frame, (480, 360),interpolation=cv2.INTER_AREA)

#             objects, poses = self.process_frame(frame,object_model=self.object_model,pose_model=self.pose_model)
#             warnings = self.check_helmet(objects=objects, poses=poses)

#             for pose,warning in zip(poses,warnings):
#                 if warning == True:
#                     label = "Helmet Weared"
#                     color = (0,255,0)
#                 else:
#                     label = "No Helmet"
#                     color = (0,0,255)
#                 x1, y1, x2, y2 = pose["bbox"]
#                 cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
#                 cv2.putText(frame, f"{label}", (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2) 

#                 true_count = warnings.count(True)
#                 false_count = warnings.count(False)

#                 self.warning_count[idx] = [true_count,false_count]
                    
#             _, buffer = cv2.imencode('.jpg', frame)
#             # self.helmet_frames[idx] = base64.b64encode(buffer).decode('utf-8')
#                     # self.people_count[idx] = count
#             self.helmet_frames[idx] = buffer.tobytes()
#             if not self.running:
#                 break