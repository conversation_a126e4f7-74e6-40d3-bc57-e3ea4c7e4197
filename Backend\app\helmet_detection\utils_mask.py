import cv2
import threading
import numpy as np
import concurrent.futures
from ultralytics import YOLO

class HelmetDetection:
    def __init__(self, seg_model, pose_model, seg_conf_value=0.5, pose_conf_value=0.5, iou_threshold=0.3):
        self.seg_conf_value = seg_conf_value
        self.pose_conf_value = pose_conf_value
        self.iou_threshold = iou_threshold
        self.seg_model = YOLO(seg_model,task ="segment")  # 1:gloves, 2:helmet, 3:person, 5:vest
        self.pose_model = YOLO(pose_model,task= "pose")
        self.threads = []
        self.running = False
        self.blank = cv2.imread("./static/black.jpg")
        self.blank = cv2.resize(self.blank, (640, 480))

    def start(self, camera_details, test_video_path=None):
        self.person_count, self.no_helmet_count, self.no_vest_count, self.no_gloves_count = 0, 0, 0, 0
        self.camera_name, self.rtsp_url, self.cap_devices = [], [], []
        dic = camera_details

        if test_video_path:
            self.camera_name.append("Test Video")
            cap = cv2.VideoCapture(test_video_path)
            if cap.isOpened():
                self.cap_devices.append(cap)
            else:
                self.cap_devices.append(None)
        else:
            for key, value in dic.items():
                self.camera_name.append(key)
                if value[0].isdigit():
                    value = int(value[0])
                    self.rtsp_url.append(value)
                    cap = cv2.VideoCapture(value)
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    if cap.isOpened():
                        self.cap_devices.append(cap)
                    else:
                        self.cap_devices.append(None)
                else:
                    cap = cv2.VideoCapture(value[0], cv2.CAP_FFMPEG)
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    if cap.isOpened():
                        self.cap_devices.append(cap)
                    else:
                        self.cap_devices.append(None)

        self.helmet_frames = [None] * len(self.cap_devices)
        self.warning_count = [{"person_count": 0, "no_helmet_count": 0, "no_vest_count": 0, "no_gloves_count": 0} for _ in range(len(self.cap_devices))]

        if not self.running:
            self.running = True
            for idx, cap in enumerate(self.cap_devices):
                if cap is not None and cap.isOpened():
                    thread = threading.Thread(target=self.update, args=(idx, cap, test_video_path))
                    thread.daemon = True
                    thread.start()
                    self.threads.append(thread)
                else:
                    temp = cv2.putText(self.blank, f"{self.camera_name[idx]} is Offline", (35, 170),
                        fontFace=cv2.FONT_HERSHEY_SIMPLEX,
                        fontScale=1,
                        thickness=3,
                        color=(255, 255, 255),
                    )
                    _, temp = cv2.imencode(".png", temp)
                    self.helmet_frames[idx] = temp.tobytes()

    def stop(self):
        self.running = False
        for cap in self.cap_devices:
            if cap is not None:
                cap.release()
        # for thread in self.threads:
            # thread.join()
    
    def calculate_iou(self, mask1, mask2):
        """Calculate IoU between two binary masks"""
        intersection = np.logical_and(mask1, mask2).sum()
        union = np.logical_or(mask1, mask2).sum()
        return intersection / union if union > 0 else 0
    
    def get_keypoint_region_mask(self, keypoints, region_type, frame_shape):
        """Create a binary mask around specific keypoints based on region type"""
        mask = np.zeros(frame_shape[:2], dtype=np.uint8)
        
        if region_type == "head":
            # Head region (keypoints 0-4: nose, eyes, ears)
            valid_points = []
            for i in range(min(5, len(keypoints))):
                # Only include points that are not (0,0) and have actual values
                if keypoints[i][0] > 0 and keypoints[i][1] > 0:
                    valid_points.append(keypoints[i])
            
            if len(valid_points) >= 2:  # Need at least 2 valid points to form a region
                points = np.array(valid_points)
                hull = cv2.convexHull(points.astype(np.int32))
                cv2.fillConvexPoly(mask, hull, 255)
                # Expand the region more for helmets
                mask = cv2.dilate(mask, np.ones((25, 25), np.uint8), iterations=2)
                
        elif region_type == "torso":
            # Torso region (keypoints 5,6,11,12: shoulders and hips)
            # Check which points of the torso are valid
            torso_indices = [5, 6, 11, 12]
            valid_points = []
            for idx in torso_indices:
                if idx < len(keypoints) and keypoints[idx][0] > 0 and keypoints[idx][1] > 0:
                    valid_points.append(keypoints[idx])
            
            if len(valid_points) >= 3:  # Need at least 3 valid points to form a polygon
                cv2.fillConvexPoly(mask, np.array(valid_points).astype(np.int32), 255)
                # Expand the region slightly
                mask = cv2.dilate(mask, np.ones((20, 20), np.uint8), iterations=1)
                
        elif region_type == "hands":
            # Hands (keypoints 9,10: wrists)
            wrist_indices = [9, 10]
            for idx in wrist_indices:
                if idx < len(keypoints) and keypoints[idx][0] > 0 and keypoints[idx][1] > 0:
                    cv2.circle(mask, (int(keypoints[idx][0]), int(keypoints[idx][1])), 30, 255, -1)
                
        return mask
    
    def calculate_bbox_iou(self, bbox1, bbox2):

        x1_min, y1_min, x1_max, y1_max = bbox1
        x2_min, y2_min, x2_max, y2_max = bbox2
        
        # Calculate intersection area
        x_left = max(x1_min, x2_min)
        y_top = max(y1_min, y2_min)
        x_right = min(x1_max, x2_max)
        y_bottom = min(y1_max, y2_max)
        
        if x_right < x_left or y_bottom < y_top:
            return 0.0
        
        intersection_area = (x_right - x_left) * (y_bottom - y_top)
        
        # Calculate union area
        bbox1_area = (x1_max - x1_min) * (y1_max - y1_min)
        bbox2_area = (x2_max - x2_min) * (y2_max - y2_min)
        union_area = bbox1_area + bbox2_area - intersection_area
        
        # Calculate IoU
        iou = intersection_area / union_area if union_area > 0 else 0
        
        return iou
    
    # def assign_masks_to_person(self, pose_list, seg_classes):

        person_compliance = []
        
        PERSON_ID, HELMET_ID, GLOVES_ID, VEST_ID = 3, 2, 1, 5

        HELMET_IOU_THRESHOLD = 0.2  # Lower threshold for helmets
        VEST_IOU_THRESHOLD = self.iou_threshold
        GLOVES_IOU_THRESHOLD = self.iou_threshold
        
        # For each detected person (from pose detection)
        for person_idx,(keypoints,bbox1) in enumerate(pose_list):    #bbox1 = pose person
            # Skip if keypoints are not valid
            if not np.any(keypoints):
                continue
                
            # Get person mask from segmentation results
            best_iou = 0
            person_mask = None
            person_bbox = None
            
            for mask_idx, (mask, cls_id, conf, bbox2) in enumerate(seg_classes):    #bbox2 = segmentation person
                if cls_id == PERSON_ID:
                        
                    # # Get bounding box from pose keypoints
                    # pose_kps = np.array(keypoints)
                    # if len(pose_kps) > 0:
                    #     # Create pose bounding box using min/max keypoint coordinates
                    #     kps_x_min = np.min(pose_kps[:, 0])
                    #     kps_y_min = np.min(pose_kps[:, 1])
                    #     kps_x_max = np.max(pose_kps[:, 0])
                    #     kps_y_max = np.max(pose_kps[:, 1])
                    #     pose_bbox = (kps_x_min, kps_y_min, kps_x_max, kps_y_max)
                        
                        # Calculate IoU between pose bbox and segmentation bbox
                        # bbox_iou = self.calculate_bbox_iou(pose_bbox, bbox2)
                        bbox_iou = self.calculate_bbox_iou(bbox1, bbox2)
                        
                        # Count how many keypoints fall within this mask for additional verification
                        mask_height, mask_width = mask.shape[:2]
                        keypoints_in_mask = 0
                        
                        for kp in keypoints:
                            x, y = int(kp[0]), int(kp[1])
                            # Check if the keypoint is within mask boundaries
                            if 0 <= x < mask_width and 0 <= y < mask_height:
                                if mask[y, x] > 0:
                                    keypoints_in_mask += 1
                        
                        # Use a combined score that considers both bbox IoU and keypoint overlap
                        match_score = bbox_iou * 0.7 + (keypoints_in_mask / len(keypoints)) * 0.3
                        
                        if match_score > best_iou:
                            best_iou = match_score
                            person_mask = mask
                            person_bbox = bbox2


            
            # Skip if no valid person mask was found
            if person_mask is None:
                continue
                
            # Create region-specific masks from keypoints
            head_region = self.get_keypoint_region_mask(keypoints, "head", person_mask.shape)
            torso_region = self.get_keypoint_region_mask(keypoints, "torso", person_mask.shape)
            hands_region = self.get_keypoint_region_mask(keypoints, "hands", person_mask.shape)
            
            # Check for helmet, vest, and gloves using both IoU and keypoint proximity
            helmet_worn, vest_worn, gloves_worn = False, False, False
            helmet_iou, vest_iou, gloves_iou = 0, 0, 0
            helmet_conf, vest_conf, gloves_conf = 0, 0, 0
            
            for mask, cls_id, conf, _ in seg_classes:
                if cls_id == HELMET_ID:
                    # Calculate IoU between helmet mask and head region
                    iou = self.calculate_iou(mask, head_region)
                    if iou > self.iou_threshold and iou > helmet_iou:
                        helmet_worn = True
                        helmet_iou = iou
                        
                elif cls_id == VEST_ID:
                    # Calculate IoU between vest mask and torso region
                    iou = self.calculate_iou(mask, torso_region)
                    if iou > self.iou_threshold and iou > vest_iou:
                        vest_worn = True
                        vest_iou = iou
                        
                elif cls_id == GLOVES_ID:
                    # Calculate IoU between gloves mask and hands region
                    iou = self.calculate_iou(mask, hands_region)
                    if iou > self.iou_threshold and iou > gloves_iou:
                        gloves_worn = True
                        gloves_iou = iou
            
            compliance_status = {
                "bbox": person_bbox,
                "helmet": helmet_worn,
                "vest": vest_worn,
                "gloves": gloves_worn,
                "iou_scores": {
                    "helmet": helmet_iou,
                    "vest": vest_iou,
                    "gloves": gloves_iou
                }
            }
            person_compliance.append(compliance_status)
            
        return person_compliance

    def assign_masks_to_person(self, pose_list, seg_classes):
        person_compliance = []
        
        PERSON_ID, HELMET_ID, GLOVES_ID, VEST_ID = 3, 2, 1, 5
        
        # Different IoU thresholds for different PPE items
        HELMET_IOU_THRESHOLD = 0.2  # Lower threshold for helmets
        VEST_IOU_THRESHOLD = self.iou_threshold
        GLOVES_IOU_THRESHOLD = self.iou_threshold
        
        # For each detected person (from pose detection)
        for person_idx, (keypoints, bbox1) in enumerate(pose_list):
            # Validate keypoints
            if not np.any(keypoints) or all(keypoints[0] == 0 and keypoints[1] == 0 for keypoints in keypoints):
                continue
                
            # Get person mask from segmentation results
            best_iou = 0
            person_mask = None
            person_bbox = None
            
            for mask_idx, (mask, cls_id, conf, bbox2) in enumerate(seg_classes):
                if cls_id == PERSON_ID:
                    # Calculate IoU between pose bbox and segmentation bbox
                    bbox_iou = self.calculate_bbox_iou(bbox1, bbox2)
                    
                    # Count how many keypoints fall within this mask for additional verification
                    mask_height, mask_width = mask.shape[:2]
                    keypoints_in_mask = 0
                    valid_keypoints = 0
                    
                    for kp in keypoints:
                        x, y = int(kp[0]), int(kp[1])
                        # Skip invalid keypoints (0,0)
                        if x <= 0 or y <= 0:
                            continue
                            
                        valid_keypoints += 1
                        # Check if the keypoint is within mask boundaries
                        if 0 <= x < mask_width and 0 <= y < mask_height:
                            if mask[y, x] > 0:
                                keypoints_in_mask += 1
                    
                    # If no valid keypoints, skip this person
                    if valid_keypoints == 0:
                        continue
                        
                    # Use a combined score that considers both bbox IoU and keypoint overlap
                    keypoint_ratio = keypoints_in_mask / valid_keypoints if valid_keypoints > 0 else 0
                    match_score = bbox_iou * 0.7 + keypoint_ratio * 0.3
                    
                    if match_score > best_iou:
                        best_iou = match_score
                        person_mask = mask
                        person_bbox = bbox2
            
            # Skip if no valid person mask was found
            if person_mask is None:
                continue
                
            # Create region-specific masks from keypoints
            head_region = self.get_keypoint_region_mask(keypoints, "head", person_mask.shape)
            torso_region = self.get_keypoint_region_mask(keypoints, "torso", person_mask.shape)
            hands_region = self.get_keypoint_region_mask(keypoints, "hands", person_mask.shape)
            
            # Check if valid head region was created
            has_valid_head_region = np.any(head_region)
            has_valid_torso_region = np.any(torso_region)
            has_valid_hands_region = np.any(hands_region)
            
            # Check for helmet, vest, and gloves using both IoU and confidence
            helmet_worn, vest_worn, gloves_worn = False, False, False
            helmet_iou, vest_iou, gloves_iou = 0, 0, 0
            helmet_conf, vest_conf, gloves_conf = 0, 0, 0
            
            # First pass: collect all potential matches
            helmet_candidates = []
            vest_candidates = []
            gloves_candidates = []
            
            for mask, cls_id, conf, bbox in seg_classes:
                if cls_id == HELMET_ID:
                    # If we have a valid head region, use IoU
                    if has_valid_head_region:
                        iou = self.calculate_iou(mask, head_region)
                        if iou > 0.1:  # Lower threshold for collecting candidates
                            helmet_candidates.append((iou, conf, mask))
                    # Fallback: use bounding box overlap if head region is invalid
                    else:
                        x1, y1, x2, y2 = person_bbox
                        head_top = y1
                        head_bottom = y1 + (y2 - y1) * 0.3  # Upper 30% of person bbox
                        helmet_bbox_iou = self.calculate_bbox_iou((x1, head_top, x2, head_bottom), bbox)
                        if helmet_bbox_iou > 0.3:
                            helmet_candidates.append((helmet_bbox_iou, conf, mask))
                        
                elif cls_id == VEST_ID and has_valid_torso_region:
                    # Calculate IoU between vest mask and torso region
                    iou = self.calculate_iou(mask, torso_region)
                    if iou > 0.15:  # Lower threshold for collecting candidates
                        vest_candidates.append((iou, conf, mask))
                        
                elif cls_id == GLOVES_ID and has_valid_hands_region:
                    # Calculate IoU between gloves mask and hands region
                    iou = self.calculate_iou(mask, hands_region)
                    if iou > 0.15:  # Lower threshold for collecting candidates
                        gloves_candidates.append((iou, conf, mask))
            
            # Second pass: evaluate candidates with a weighted score of IoU and confidence
            if helmet_candidates:
                # Sort by combined score of IoU and confidence
                helmet_candidates.sort(key=lambda x: (x[0] * 0.7 + x[1] * 0.3), reverse=True)
                best_helmet = helmet_candidates[0]
                helmet_iou, helmet_conf = best_helmet[0], best_helmet[1]
                
                # Use a weighted threshold approach
                weighted_score = helmet_iou * 0.7 + helmet_conf * 0.3
                helmet_worn = weighted_score > HELMET_IOU_THRESHOLD
                
            if vest_candidates:
                vest_candidates.sort(key=lambda x: (x[0] * 0.6 + x[1] * 0.4), reverse=True)
                best_vest = vest_candidates[0]
                vest_iou, vest_conf = best_vest[0], best_vest[1]
                
                weighted_score = vest_iou * 0.6 + vest_conf * 0.4
                vest_worn = weighted_score > VEST_IOU_THRESHOLD
                
            if gloves_candidates:
                gloves_candidates.sort(key=lambda x: (x[0] * 0.6 + x[1] * 0.4), reverse=True)
                best_gloves = gloves_candidates[0]
                gloves_iou, gloves_conf = best_gloves[0], best_gloves[1]
                
                weighted_score = gloves_iou * 0.6 + gloves_conf * 0.4
                gloves_worn = weighted_score > GLOVES_IOU_THRESHOLD
            
            # Special case for helmets: direct detection in the upper part of the person bbox
            if not helmet_worn and person_bbox:
                x1, y1, x2, y2 = person_bbox
                head_y_threshold = y1 + (y2 - y1) * 0.3  # Upper 30% of person bounding box
                
                for mask, cls_id, conf, bbox in seg_classes:
                    if cls_id == HELMET_ID:
                        hx1, hy1, hx2, hy2 = bbox
                        # If helmet bbox is mostly in the upper part of person bbox
                        if hy1 < head_y_threshold and self.calculate_bbox_iou((x1, y1, x2, head_y_threshold), bbox) > 0.25:
                            helmet_worn = True
                            helmet_iou = 0.3  # Assign a reasonable IoU value
                            helmet_conf = conf
                            break
            
            compliance_status = {
                "bbox": person_bbox,
                "helmet": helmet_worn,
                "vest": vest_worn,
                "gloves": gloves_worn,
                "iou_scores": {
                    "helmet": helmet_iou,
                    "vest": vest_iou,
                    "gloves": gloves_iou
                },
                "conf_scores": {
                    "helmet": helmet_conf,
                    "vest": vest_conf,
                    "gloves": gloves_conf
                }
            }
            person_compliance.append(compliance_status)
            
        return person_compliance

    def process_frame(self, frame):
        with concurrent.futures.ThreadPoolExecutor() as executor:
            future_seg = executor.submit(self.seg_model, frame,classes = [1,2,3,5])  # 1:gloves, 2:helmet, 3:person, 5:vest
            future_poses = executor.submit(self.pose_model, frame)
            
            seg_results = future_seg.result()
            pose_results = future_poses.result()
        

        seg_classes = []
        for result in seg_results:
            if result.masks is not None:
                masks = result.masks.data.cpu().numpy()
                clss  = result.boxes.cls.cpu().numpy()
                confs = result.boxes.conf.cpu().numpy()
                boxes = result.boxes.xyxy.cpu().numpy()
                for idx, (mask, cls, conf) in enumerate(zip(masks, clss, confs)):
                    if conf > self.seg_conf_value:
                        x1, y1, x2, y2 = map(int, boxes[idx])

                        binary_mask = np.zeros(frame.shape[:2], dtype=np.uint8)
                        resized_mask = cv2.resize(mask, (frame.shape[1], frame.shape[0]))
                        binary_mask[resized_mask > 0.5] = 255
                        seg_classes.append((binary_mask, int(cls), conf, (x1, y1, x2, y2)))
            

        pose_list = []
        for pose_result in pose_results:
            if pose_result.keypoints is not None:
                for kps, conf, pose_box in zip(pose_result.keypoints.xy.cpu().numpy(), pose_result.boxes.conf,pose_result.boxes.xyxy.cpu().numpy()):
                    if conf > self.pose_conf_value:
                        x1, y1, x2, y2 = map(int, pose_box)
                        # Skip if bounding box is invalid
                        if x1 >= x2 or y1 >= y2:
                            continue
                        pose_list.append((kps,(x1, y1, x2, y2)))
        
        return seg_classes, pose_list

    def update(self, idx, cap, test_video_path=None):
        frame_counter, skip_frames = 0, 2
        while self.running:
            ret, self.frame = cap.read()
            
            if not ret and test_video_path:
                cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                continue
            elif not ret:
                break

            frame_counter += 1
            if frame_counter % skip_frames != 0:
                continue

            self.frame = cv2.resize(self.frame, (640, 480), interpolation=cv2.INTER_AREA)

            seg_classes, pose_list = self.process_frame(self.frame)
            
   
            # person_masks = [mask for mask, cls_id, _, _ in seg_classes if cls_id == 3]  # Person class ID = 3
            
            # Associate masks with keypoints and validate PPE
            compliance_results = self.assign_masks_to_person(pose_list, seg_classes)


            # print(compliance_results)

            # Store counts
            self.person_count = len(compliance_results)
            self.no_helmet_count = sum(1 for person in compliance_results if not person["helmet"])
            self.no_vest_count = sum(1 for person in compliance_results if not person["vest"])
            self.no_gloves_count = sum(1 for person in compliance_results if not person["gloves"])


            self.warning_count[idx] = {
                "person_count": self.person_count,
                "no_helmet_count": self.no_helmet_count,
                "no_vest_count": self.no_vest_count,
                "no_gloves_count": self.no_gloves_count
            }

            # Visualization (if needed)
            # for result in compliance_results:
            #     x1, y1, x2, y2 = result["bbox"]
                
            #     # Create label text
            #     label = []
            #     if result["helmet"]:
            #         label.append(f"Helmet: {result['iou_scores']['helmet']:.2f}")
            #     else:
            #         label.append("No Helmet")
                    
            #     if result["vest"]:
            #         label.append(f"Vest: {result['iou_scores']['vest']:.2f}")
            #     else:
            #         label.append("No Vest")
                    
            #     if result["gloves"]:
            #         label.append(f"Gloves: {result['iou_scores']['gloves']:.2f}")
            #     else:
            #         label.append("No Gloves")
                
            #     text = " | ".join(label)
            #     color = (0, 255, 0) if all([result["helmet"], result["vest"], result["gloves"]]) else (0, 0, 255)
                
            #     cv2.rectangle(self.frame, (x1, y1), (x2, y2), color, 2)
            #     y_offset = y1 - 10
            #     for idx, item in enumerate(label):
            #         y_pos = y_offset - (idx * 20)
            #         cv2.putText(self.frame, item, (x1, y_pos), 
            #                     cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

            # Encode frame for web display
            _, buffer = cv2.imencode(".png", self.frame)
            self.helmet_frames[idx] = buffer.tobytes()

            if not self.running:
                break