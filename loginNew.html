<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Surveillance System - Login</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #43cea2 0%, #185a9d 100%);
            --glass-bg: rgba(255, 255, 255, 0.95);
            --shadow-light: 0 8px 32px rgba(0, 0, 0, 0.1);
            --shadow-heavy: 0 20px 40px rgba(0, 0, 0, 0.15);
            --text-primary: #333;
            --text-secondary: #666;
            --success-color: #10b981;
            --error-color: #ef4444;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f9f9fb;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Header Styles */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 40px;
            background: #fff;
            border-bottom: 1px solid #e5e5e5;
            position: relative;
            z-index: 10;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            animation: slideDown 0.8s ease-out;
        }

        @keyframes slideDown {
            from { transform: translateY(-100%); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        /* Main container */
        .auth-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: calc(100vh - 90px);
            padding: 20px;
            position: relative;
            z-index: 5;
        }

        .auth-box {
            position: relative;
            z-index: 10;
            width: 100%;
            max-width: 480px;
            background: #fff;
            border: 1px solid #e5e5e5;
            border-radius: 24px;
            box-shadow: var(--shadow-heavy);
            overflow: hidden;
            /* animation: slideUp 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94); */
            margin: 20px auto;
        }

        @keyframes slideUp {
            from {
                transform: translateY(50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* Header with logo */
        .auth-header {
            padding: 30px 30px 20px;
            text-align: center;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo {
            height: 50px;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
        }

        /* Fallback for when logo image is not available */
        .logo-fallback {
            height: 50px;
            width: 50px;
            background: var(--primary-gradient);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .company-name {
            font-size: 28px;
            font-weight: 800;
            color: #333;
            letter-spacing: -0.5px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* Tab navigation */
        .tab-navigation {
            display: flex;
            position: relative;
            background: rgba(0, 0, 0, 0.05);
            border-radius: 12px;
            padding: 4px;
            margin: 20px 30px 20px;
        }

        .tab-button {
            flex: 1;
            padding: 12px 20px;
            background: none;
            border: none;
            font-size: 14px;
            font-weight: 600;
            color: var(--text-secondary);
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 2;
        }

        .tab-button.active {
            color: var(--text-primary);
        }

        .tab-indicator {
            position: absolute;
            top: 4px;
            left: 4px;
            width: calc(50% - 4px);
            height: calc(100% - 8px);
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1;
        }

        .tab-indicator.register {
            transform: translateX(100%);
        }

        /* Form container */
        .form-container {
            position: relative;
            height: auto;
            overflow: hidden;
        }

        .form-wrapper {
            display: flex;
            width: 200%;
            transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .form-wrapper.show-register {
            transform: translateX(-50%);
        }

        .form-section {
            width: 50%;
            padding: 0 30px 20px;
        }

        .form-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 30px;
            text-align: center;
            opacity: 0;
            animation: fadeInUp 0.6s ease-out 0.3s forwards;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Input groups */
        .input-group {
            margin-bottom: 20px;
            position: relative;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 14px;
            letter-spacing: 0.5px;
        }

        .input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .input-group input {
            width: 100%;
            padding: 14px 18px;
            background: white;
            border: 2px solid #e5e5e5;
            border-radius: 12px;
            font-size: 15px;
            color: var(--text-primary);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
        }

        .input-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .input-icon {
            position: absolute;
            right: 18px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 4px;
        }

        .input-icon:hover {
            color: var(--text-primary);
            transform: scale(1.1);
        }

        /* Form row for side-by-side inputs */
        .form-row {
            display: flex;
            gap: 15px;
        }

        .form-row .input-group {
            flex: 1;
        }

        /* Remember me checkbox */
        .remember-me {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }

        .remember-me input[type="checkbox"] {
            width: 18px;
            height: 18px;
            margin-right: 10px;
            accent-color: #667eea;
            cursor: pointer;
        }

        .remember-me label {
            color: var(--text-secondary);
            font-size: 14px;
            cursor: pointer;
            margin-bottom: 0;
        }

        /* Submit button */
        .submit-button {
            width: 100%;
            padding: 15px;
            background: var(--primary-gradient);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 15px;
            font-weight: 700;
            letter-spacing: 0.5px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            margin-bottom: 0;
        }

        .submit-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .submit-button:hover::before {
            left: 100%;
        }

        .submit-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 30px rgba(102, 126, 234, 0.4);
        }

        .submit-button:active {
            transform: translateY(0);
        }

        /* Loading state */
        .submit-button.loading {
            pointer-events: none;
            opacity: 0.8;
        }

        .submit-button.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Forgot password link */
        .forgot-password {
            text-align: center;
            margin-top: 15px;
        }

        .forgot-password a {
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
            position: relative;
        }

        .forgot-password a::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 1px;
            background: #667eea;
            transition: width 0.3s ease;
        }

        .forgot-password a:hover {
            color: #667eea;
        }

        .forgot-password a:hover::after {
            width: 100%;
        }

        /* Messages */
        .message {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            font-weight: 500;
            text-align: center;
            animation: slideDown 0.5s ease-out;
        }

        .error-message {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .success-message {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* OTP Section */
        .otp-section {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }

        .otp-input {
            text-align: center;
            font-size: 18px;
            letter-spacing: 4px;
            font-weight: 600;
        }

        /* Hidden class */
        .hidden {
            display: none;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .header {
                padding: 15px 20px;
            }

            .company-name {
                font-size: 22px;
            }

            .auth-box {
                padding: 25px 20px;
                margin: 10px;
                border-radius: 20px;
                max-width: 100%;
            }

            .form-section {
                padding: 0 20px 30px;
            }

            .tab-navigation {
                margin: 0 20px 20px;
            }

            .form-row {
                flex-direction: column;
                gap: 0;
            }
        }

        /* Micro-interactions */
        .input-group {
            animation: fadeInUp 0.6s ease-out forwards;
            animation-delay: calc(var(--delay, 0) * 0.1s);
        }

        .tab-button:hover {
            transform: translateY(-1px);
        }

        /* .logo:hover { */
            /* transform: scale(1.05) rotate(5deg); */
        /* } */

        /* Focus visible for accessibility */
        *:focus-visible {
            outline: 2px solid #667eea;
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <!-- Header with logo and company name -->
    <div class="header">
        <div class="logo-container">
            <img src="/static/ecogo.png" alt="VigilanteEye Logo" class="logo" id="companyLogo" onerror="showLogoFallback()">
            <div class="logo-fallback" id="logoFallback" style="display: none;">
                <i class="fas fa-shield-alt"></i>
            </div>
            <!-- <span class="company-name">VigilanteEye</span> -->
        </div>
    </div>

    <!-- Main authentication container -->
    <div class="auth-container">
        <div class="auth-box">
            <!-- Tab navigation -->
            <div class="tab-navigation">
                <div class="tab-indicator" id="tabIndicator"></div>
                <button class="tab-button active" id="loginTab" onclick="switchTab('login')">
                    Login
                </button>
                <button class="tab-button" id="registerTab" onclick="switchTab('register')">
                    Register
                </button>
            </div>

        <!-- Forms container -->
        <div class="form-container">
            <div class="form-wrapper" id="formWrapper">
                <!-- Login Form -->
                <div class="form-section" id="loginSection">
                    <h2 class="form-title">Welcome Back</h2>

                    <div id="loginMessages"></div>

                    <form id="loginForm">
                        <div class="input-group" style="--delay: 1">
                            <label for="loginUsername">Username</label>
                            <div class="input-wrapper">
                                <input type="text" id="loginUsername" name="username" required>
                                <i class="fas fa-user input-icon"></i>
                            </div>
                        </div>

                        <div class="input-group" style="--delay: 2">
                            <label for="loginPassword">Password</label>
                            <div class="input-wrapper">
                                <input type="password" id="loginPassword" name="password" required>
                                <i class="fas fa-eye input-icon" onclick="togglePassword('loginPassword', this)"></i>
                            </div>
                        </div>

                        <div class="remember-me" style="--delay: 3">
                            <input type="checkbox" id="rememberMe" name="remember">
                            <label for="rememberMe">Remember me</label>
                        </div>

                        <button type="submit" class="submit-button" style="--delay: 4">
                            Sign In
                        </button>
                    </form>

                    <!-- <div class="forgot-password">
                        <a href="#" onclick="showForgotPassword()">Forgot your password?</a>
                    </div> -->
                </div>

                <!-- Register Form -->
                <div class="form-section" id="registerSection">
                    <h2 class="form-title">Create Account</h2>

                    <div id="registerMessages"></div>

                    <!-- Registration Form -->
                    <form id="registerForm">
                        <div id="registrationFields">
                            <div class="form-row">
                                <div class="input-group" style="--delay: 1">
                                    <label for="regUsername">Username</label>
                                    <div class="input-wrapper">
                                        <input type="text" id="regUsername" name="username" required>
                                        <i class="fas fa-user input-icon"></i>
                                    </div>
                                </div>

                                <div class="input-group" style="--delay: 2">
                                    <label for="regEmail">Email</label>
                                    <div class="input-wrapper">
                                        <input type="email" id="regEmail" name="email" required>
                                        <i class="fas fa-envelope input-icon"></i>
                                    </div>
                                </div>
                            </div>

                            <div class="input-group" style="--delay: 3">
                                <label for="regFullName">Full Name</label>
                                <div class="input-wrapper">
                                    <input type="text" id="regFullName" name="full_name">
                                    <i class="fas fa-id-card input-icon"></i>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="input-group" style="--delay: 4">
                                    <label for="regPassword">Password</label>
                                    <div class="input-wrapper">
                                        <input type="password" id="regPassword" name="password" required>
                                        <i class="fas fa-eye input-icon" onclick="togglePassword('regPassword', this)"></i>
                                    </div>
                                </div>

                                <div class="input-group" style="--delay: 5">
                                    <label for="regConfirmPassword">Confirm Password</label>
                                    <div class="input-wrapper">
                                        <input type="password" id="regConfirmPassword" name="confirm_password" required>
                                        <i class="fas fa-eye input-icon" onclick="togglePassword('regConfirmPassword', this)"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- OTP Section (initially hidden) -->
                        <div id="otpSection" class="otp-section hidden">
                            <div class="input-group">
                                <label for="otpCode">Verification Code</label>
                                <div class="input-wrapper">
                                    <input type="text" id="otpCode" name="otp_code" class="otp-input" placeholder="000000" maxlength="6">
                                    <i class="fas fa-key input-icon"></i>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="submit-button" id="registerButton" style="--delay: 6">
                            Create Account
                        </button>
                    </form>
                </div>
            </div>
        </div>
        </div>
    </div>

    <script>
        // Global state
        let currentMode = 'registration'; // 'registration' or 'otp'
        let formData = {};

        // Logo fallback function
        function showLogoFallback() {
            const logoImg = document.getElementById('companyLogo');
            const logoFallback = document.getElementById('logoFallback');

            if (logoImg && logoFallback) {
                logoImg.style.display = 'none';
                logoFallback.style.display = 'flex';
            }
        }

        // Tab switching functionality
        function switchTab(tab) {
            const formWrapper = document.getElementById('formWrapper');
            const tabIndicator = document.getElementById('tabIndicator');
            const loginTab = document.getElementById('loginTab');
            const registerTab = document.getElementById('registerTab');

            if (tab === 'login') {
                formWrapper.classList.remove('show-register');
                tabIndicator.classList.remove('register');
                loginTab.classList.add('active');
                registerTab.classList.remove('active');
                resetForms();
            } else {
                formWrapper.classList.add('show-register');
                tabIndicator.classList.add('register');
                registerTab.classList.add('active');
                loginTab.classList.remove('active');
                resetRegistrationForm();
            }

            // Clear messages
            clearMessages();
        }

        // Password visibility toggle
        function togglePassword(inputId, icon) {
            const input = document.getElementById(inputId);
            const isPassword = input.type === 'password';

            input.type = isPassword ? 'text' : 'password';
            icon.classList.toggle('fa-eye');
            icon.classList.toggle('fa-eye-slash');
        }

        // Show/hide loading state
        function setLoading(button, loading) {
            if (loading) {
                button.classList.add('loading');
                button.disabled = true;
            } else {
                button.classList.remove('loading');
                button.disabled = false;
            }
        }

        // Message functions
        function showMessage(containerId, message, type) {
            const container = document.getElementById(containerId);
            container.innerHTML = `<div class="message ${type}-message">${message}</div>`;
        }

        function clearMessages() {
            document.getElementById('loginMessages').innerHTML = '';
            document.getElementById('registerMessages').innerHTML = '';
        }

        // Reset forms
        function resetForms() {
            document.getElementById('loginForm').reset();
            clearMessages();
        }

        function resetRegistrationForm() {
            currentMode = 'registration';
            document.getElementById('registrationFields').classList.remove('hidden');
            document.getElementById('otpSection').classList.add('hidden');
            document.getElementById('registerButton').textContent = 'Create Account';
            document.getElementById('registerForm').reset();
            clearMessages();
        }

        // Show OTP verification
        function showOTPVerification() {
            currentMode = 'otp';
            document.getElementById('registrationFields').classList.add('hidden');
            document.getElementById('otpSection').classList.remove('hidden');
            document.getElementById('registerButton').textContent = 'Verify & Complete Registration';

            showMessage('registerMessages',
                'Verification code sent to your email. Please check your inbox.',
                'success'
            );
        }

        // API calls to backend
        async function performLogin(username, password, remember) {
            const formData = new FormData();
            formData.append('username', username);
            formData.append('password', password);
            formData.append('remember', remember);

            const response = await fetch('/login', {
                method: 'POST',
                body: formData
            });

            if (response.redirected) {
                // Login successful, redirect to home page
                window.location.href = response.url;
                return { success: true, message: 'Login successful!' };
            } else {
                // Login failed, parse error message
                const text = await response.text();
                const parser = new DOMParser();
                const doc = parser.parseFromString(text, 'text/html');
                const errorElement = doc.querySelector('.error-message');
                const errorMessage = errorElement ? errorElement.textContent : 'Login failed';
                return { success: false, message: errorMessage };
            }
        }

        async function requestRegistrationOTP(userData) {
            const formData = new FormData();
            formData.append('username', userData.username);
            formData.append('email', userData.email);
            formData.append('full_name', userData.full_name);
            formData.append('password', userData.password);
            formData.append('confirm_password', userData.password);

            const response = await fetch('/request-otp', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (response.ok) {
                return { success: true, message: result.message };
            } else {
                return { success: false, message: result.error };
            }
        }

        async function verifyOTPAndRegister(userData, otpCode) {
            const formData = new FormData();
            formData.append('username', userData.username);
            formData.append('email', userData.email);
            formData.append('full_name', userData.full_name);
            formData.append('password', userData.password);
            formData.append('otp_code', otpCode);

            const response = await fetch('/verify-otp', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (response.ok) {
                return { success: true, message: result.message };
            } else {
                return { success: false, message: result.error };
            }
        }

        // Form submission handlers
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;
            const remember = document.getElementById('rememberMe').checked;
            const submitButton = this.querySelector('.submit-button');

            clearMessages();
            setLoading(submitButton, true);

            try {
                const result = await performLogin(username, password, remember);

                if (result.success) {
                    showMessage('loginMessages', result.message, 'success');
                    // The performLogin function already handles redirection
                } else {
                    showMessage('loginMessages', result.message, 'error');
                }
            } catch (error) {
                showMessage('loginMessages', 'Network error occurred. Please try again.', 'error');
            } finally {
                setLoading(submitButton, false);
            }
        });

        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const submitButton = document.getElementById('registerButton');
            clearMessages();
            setLoading(submitButton, true);

            try {
                if (currentMode === 'registration') {
                    // Registration phase
                    const password = document.getElementById('regPassword').value;
                    const confirmPassword = document.getElementById('regConfirmPassword').value;

                    if (password !== confirmPassword) {
                        showMessage('registerMessages', 'Passwords do not match', 'error');
                        setLoading(submitButton, false);
                        return;
                    }

                    formData = {
                        username: document.getElementById('regUsername').value,
                        email: document.getElementById('regEmail').value,
                        full_name: document.getElementById('regFullName').value,
                        password: password
                    };

                    const result = await requestRegistrationOTP(formData);

                    if (result.success) {
                        showOTPVerification();
                    } else {
                        showMessage('registerMessages', result.message, 'error');
                    }
                } else {
                    // OTP verification phase
                    const otpCode = document.getElementById('otpCode').value;

                    if (!otpCode || otpCode.length !== 6) {
                        showMessage('registerMessages', 'Please enter a 6-digit verification code', 'error');
                        setLoading(submitButton, false);
                        return;
                    }

                    const result = await verifyOTPAndRegister(formData, otpCode);

                    if (result.success) {
                        showMessage('registerMessages', result.message, 'success');
                        setTimeout(() => {
                            switchTab('login');
                            showMessage('loginMessages', 'Registration completed! You can now log in.', 'success');
                        }, 2000);
                    } else {
                        showMessage('registerMessages', result.message, 'error');
                    }
                }
            } catch (error) {
                showMessage('registerMessages', 'Network error occurred. Please try again.', 'error');
            } finally {
                setLoading(submitButton, false);
            }
        });

        // Forgot password handler
        function showForgotPassword() {
            alert('Forgot password functionality would be implemented here.');
        }

        // Real-time validation for registration
        document.getElementById('regConfirmPassword').addEventListener('input', function() {
            const password = document.getElementById('regPassword').value;
            const confirmPassword = this.value;

            if (confirmPassword && password !== confirmPassword) {
                this.style.borderColor = '#ef4444';
            } else {
                this.style.borderColor = '#e5e5e5';
            }
        });

        // OTP input formatting
        document.getElementById('otpCode').addEventListener('input', function() {
            this.value = this.value.replace(/\D/g, '');
        });

        // Add some entrance animations
        window.addEventListener('load', function() {
            const authContainer = document.querySelector('.auth-container');
            authContainer.style.animationDelay = '0.2s';
        });
    </script>
</body>
</html>