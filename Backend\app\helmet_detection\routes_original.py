from fastapi import APIRouter,WebSocket, Request, HTTPException,UploadFile,File
from typing import Optional
from fastapi.templating import Jinja2Templates
from app.helmet_detection.utils import HelmetDetection
import asyncio
import json
from pathlib import Path
from pydantic import BaseModel
import cv2
import shutil
import os
router = APIRouter()

templates = Jinja2Templates(directory="app/helmet_detection/templates")

@router.get("/helmet_detection")
async def show_crowd_detection_page(request: Request):
    return templates.TemplateResponse("helmet_detection.html", {"request": request})


# Initialize Helmetdetection without starting it
helmet_stream = HelmetDetection(object_model="./app/helmet_detection/models/seg-modelv2.engine",pose_model="./app/helmet_detection/models/yolov8x-pose.engine",conf_value= 0.5)
# helmet_stream = HelmetDetection(seg_model="./app/helmet_detection/models/seg-modelv3.engine",pose_model="./app/helmet_detection/models/yolo11x-pose.engine")        #program with mask data


def shutdown_event():
    helmet_stream.stop()


# API to start video processing
@router.post("/start-helmet_detection")
async def start_stream(request: Request, file: Optional[UploadFile] = File(None)):
    if not helmet_stream.running:
        camera_details = load_cameras()
        test_video_path = None

        if file:
            save_path = f"./app/helmet_detection/uploads/{file.filename}"
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            with open(save_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            test_video_path = save_path

        helmet_stream.start(camera_details, test_video_path=test_video_path)

    return {"status": "helmet_stream started"}

# API to stop video processing
@router.post("/stop-stream")
async def stop_stream():
    if helmet_stream.running:
        helmet_stream.stop()
        helmet_stream.helmet_frames = []
    return {"status": "helmet_stream stopped"}

# WebSocket endpoint for video feeds
@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    try:
        while True:
            if helmet_stream.running:
                for idx in range(len(helmet_stream.helmet_frames)):
                    if helmet_stream.helmet_frames[idx] is not None:
                        warnings = helmet_stream.warning_count[idx]
                        cam_name = helmet_stream.camera_name[idx]
                        frame = helmet_stream.helmet_frames[idx]
                        await websocket.send_text(f"{idx}:{cam_name}:{warnings['person_count']}:{warnings['no_helmet_count']}:{warnings['no_vest_count']}:{warnings['no_gloves_count']}")
                        await websocket.send_bytes(frame)
                        
            
                    await asyncio.sleep(0.03)
                
            else:
                cv2.destroyAllWindows()
                await asyncio.sleep(0.5)  # Avoid busy waiting
    except Exception as e:
        print("WebSocket disconnected:", e)
    
    




#--------------------------------------------------------------ADD CAMERFAA---------------------------------------------------

# Path to the cameras.json file
CAMERAS_FILE_PATH = "app/crowd_detection/cameras.json"


# Load cameras data from the file if it exists
def load_cameras():
    if Path(CAMERAS_FILE_PATH).exists():
        with open(CAMERAS_FILE_PATH, "r") as file:
            return json.load(file)
    return {}

# Save cameras data to the file
def save_cameras(cameras_data):
    with open(CAMERAS_FILE_PATH, "w") as file:
        json.dump(cameras_data, file, indent=4)

# Pydantic model for receiving camera data
class CameraData(BaseModel):
    cameraName: str
    rtspUrl: str

# Route to handle adding a new camera
@router.post("/add-camera")
async def add_camera(camera_data: CameraData):
    cameras = load_cameras()
    
    # Check if the camera name already exists
    if camera_data.cameraName in cameras:
        return {"status": "samename", "message": "SAME NAME ALREADY EXIST"}
    
    # Check if the RTSP URL already exists in the values
    for existing_camera, existing_url in cameras.items():
        if existing_url[0] == camera_data.rtspUrl:
            # Return the camera name using the same URL
            return {"status": "error", "message": f"RTSP URL already exists for camera: {existing_camera}"}
            
            


    # Add new camera with empty ROI coordinates
    cameras[camera_data.cameraName] = [camera_data.rtspUrl,[]]
    
    # Save the updated data
    save_cameras(cameras)
    
    return {"status": "success", "message": "Camera added successfully"}


# Route to delete a camera by name
@router.delete("/delete-camera/{camera_name}")
async def delete_camera(camera_name: str):
    cameras = load_cameras()

    if camera_name not in cameras:
        raise HTTPException(status_code=404, detail="Camera not found")
    
    # Remove the camera
    del cameras[camera_name]
    
    # Save the updated camera list
    save_cameras(cameras)
    
    return {"status": "success", "message": f"Camera '{camera_name}' deleted successfully"}


@router.get("/get-cameras")
async def get_cameras():
    return load_cameras()