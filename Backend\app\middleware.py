from fastapi import Request, status
from fastapi.responses import RedirectResponse
from jose import JWTError, jwt
from sqlalchemy.orm import Session
import app.auth as auth
from app.database import SessionLocal

async def auth_middleware(request: Request, call_next):
    # Paths that don't require authentication
    public_paths = ["/login", "/register", "/static", "/token", "/favicon.ico"]
    
    # Check if the path is public
    for path in public_paths:
        if request.url.path.startswith(path):
            return await call_next(request)
    
    # Check for token in cookies
    token = request.cookies.get("access_token")
    
    if not token:
        # Redirect to login if no token
        return RedirectResponse(url="/login", status_code=status.HTTP_303_SEE_OTHER)
    
    try:
        # Strip the "Bearer " prefix if present
        if token.startswith("Bearer "):
            token = token[7:]
        
        # Validate the token
        payload = jwt.decode(token, auth.SECRET_KEY, algorithms=[auth.ALGORITHM])
        username = payload.get("sub")
        
        if not username:
            return RedirectResponse(url="/login", status_code=status.HTTP_303_SEE_OTHER)
            
        # Get the user from database to verify they exist
        db = SessionLocal()
        user = db.query(auth.UserDB).filter(auth.UserDB.username == username).first()
        db.close()
        
        if not user or user.disabled:
            return RedirectResponse(url="/login", status_code=status.HTTP_303_SEE_OTHER)
            
    except JWTError:
        # Invalid token, redirect to login
        return RedirectResponse(url="/login", status_code=status.HTTP_303_SEE_OTHER)
    
    # Token is valid, continue with the request
    response = await call_next(request)
    return response