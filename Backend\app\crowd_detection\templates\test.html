

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Focus Areas Configuration Modal</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* Modal Overlay */
        .focus-areas-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.85);
            backdrop-filter: blur(12px);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .focus-areas-modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        /* Modal Container */
        .focus-areas-modal {
            width: 85%; /* Further reduced width */
            max-width: 1000px; /* Further reduced max-width */
            height: 80vh; /* Further reduced height */
            background: linear-gradient(145deg, #1a202c 0%, #2d3748 100%);
            border-radius: 12px; /* Slightly smaller border-radius */
            box-shadow: 0 15px 40px -8px rgba(0, 0, 0, 0.6); /* Adjusted shadow */
            border: 1px solid rgba(74, 85, 104, 0.3);
            display: flex;
            overflow: hidden;
            transform: scale(0.98) translateY(5px); /* Adjusted transform */
            transition: all 0.3s ease;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        }

        .focus-areas-modal-overlay.active .focus-areas-modal {
            transform: scale(1) translateY(0);
        }

        /* Header */
        .modal-header {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(26, 32, 44, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 20px; /* Further reduced padding */
            border-bottom: 1px solid rgba(74, 85, 104, 0.3);
            z-index: 10;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 10px; /* Further reduced gap */
        }

        .brand-icon {
            width: 32px; /* Further reduced size */
            height: 32px; /* Further reduced size */
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px; /* Slightly smaller border-radius */
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px; /* Further reduced font size */
        }

        .header-title h1 {
            color: #f7fafc;
            font-size: 18px; /* Further reduced font size */
            font-weight: 700;
            letter-spacing: -0.025em;
            margin-bottom: 0; /* Removed margin */
        }

        .header-subtitle {
            color: #a0aec0;
            font-size: 12px; /* Further reduced font size */
            font-weight: 500;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 10px; /* Further reduced gap */
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 5px; /* Further reduced gap */
            padding: 5px 10px; /* Further reduced padding */
            border-radius: 14px; /* Slightly smaller border-radius */
            font-size: 11px; /* Further reduced font size */
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .connection-status.connected {
            background: rgba(72, 187, 120, 0.15);
            color: #48bb78;
            border: 1px solid rgba(72, 187, 120, 0.3);
        }

        .connection-status.disconnected {
            background: rgba(245, 101, 101, 0.15);
            color: #f56565;
            border: 1px solid rgba(245, 101, 101, 0.3);
        }

        .close-modal-btn, .help-btn { /* Combined styles for similar buttons */
            background: rgba(74, 85, 104, 0.2);
            border: none;
            color: #a0aec0;
            width: 34px; /* Further reduced size */
            height: 34px; /* Further reduced size */
            border-radius: 8px; /* Slightly smaller border-radius */
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            font-size: 14px; /* Further reduced font size */
        }

        .close-modal-btn:hover {
            background: rgba(245, 101, 101, 0.15);
            color: #f56565;
            transform: scale(1.05);
        }

        .help-btn:hover {
            background: rgba(102, 126, 234, 0.15);
            color: #667eea;
            transform: scale(1.05);
        }

        /* Main Content */
        .modal-body {
            display: flex;
            width: 100%;
            height: 100%;
            padding-top: 70px; /* Adjusted padding to account for smaller header */
        }

        /* Sidebar */
        .control-panel {
            width: 300px; /* Further reduced width */
            background: #1a202c;
            border-right: 1px solid rgba(74, 85, 104, 0.3);
            padding: 20px; /* Further reduced padding */
            overflow-y: auto;
        }

        .control-group {
            margin-bottom: 24px; /* Further reduced margin */
        }

        .control-group-title {
            color: #f7fafc;
            font-size: 15px; /* Further reduced font size */
            font-weight: 700;
            margin-bottom: 14px; /* Further reduced margin */
            display: flex;
            align-items: center;
            gap: 8px; /* Further reduced gap */
            padding-bottom: 8px; /* Further reduced padding */
            border-bottom: 2px solid rgba(102, 126, 234, 0.2);
        }

        .control-group-title i {
            color: #667eea;
            font-size: 16px; /* Further reduced font size */
        }

        .form-field {
            margin-bottom: 14px; /* Further reduced margin */
        }

        .field-label {
            display: block;
            color: #e2e8f0;
            font-size: 12px; /* Further reduced font size */
            font-weight: 600;
            margin-bottom: 5px; /* Further reduced margin */
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .field-select {
            width: 100%;
            padding: 10px 14px; /* Further reduced padding */
            background: #2d3748;
            border: 2px solid rgba(74, 85, 104, 0.3);
            border-radius: 8px; /* Slightly smaller border-radius */
            color: #f7fafc;
            font-size: 13px; /* Further reduced font size */
            font-weight: 500;
            outline: none;
            transition: all 0.2s ease;
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23a0aec0' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 10px center; /* Adjusted position */
            background-repeat: no-repeat;
            background-size: 16px; /* Further reduced size */
            padding-right: 35px; /* Adjusted padding */
        }

        .field-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.15);
        }

        .control-btn {
            width: 100%;
            padding: 10px 18px; /* Further reduced padding */
            border: none;
            border-radius: 8px; /* Slightly smaller border-radius */
            font-size: 13px; /* Further reduced font size */
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px; /* Further reduced gap */
            margin-bottom: 10px; /* Further reduced margin */
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 5px 15px -5px rgba(102, 126, 234, 0.5); /* Adjusted shadow */
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 25px -5px rgba(102, 126, 234, 0.6); /* Adjusted shadow */
        }

        .btn-success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            box-shadow: 0 5px 15px -5px rgba(72, 187, 120, 0.5); /* Adjusted shadow */
        }

        .btn-success:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 25px -5px rgba(72, 187, 120, 0.6); /* Adjusted shadow */
        }

        .btn-danger {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
            box-shadow: 0 5px 15px -5px rgba(245, 101, 101, 0.5); /* Adjusted shadow */
        }

        .btn-danger:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 25px -5px rgba(245, 101, 101, 0.6); /* Adjusted shadow */
        }

        .btn-secondary {
            background: rgba(74, 85, 104, 0.3);
            color: #e2e8f0;
            border: 2px solid rgba(74, 85, 104, 0.5);
        }

        .btn-secondary:hover {
            background: rgba(74, 85, 104, 0.5);
            color: #f7fafc;
            transform: translateY(-1px);
        }

        /* Video Area */
        .video-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #000;
        }

        .video-header {
            padding: 12px 20px; /* Further reduced padding */
            background: rgba(26, 32, 44, 0.8);
            border-bottom: 1px solid rgba(74, 85, 104, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .camera-info {
            color: #a0aec0;
            font-size: 12px; /* Further reduced font size */
        }

        .camera-name {
            font-weight: 700;
            color: #f7fafc;
            font-size: 14px; /* Further reduced font size */
        }

        .ready-status {
            display: flex;
            align-items: center;
            gap: 5px; /* Further reduced gap */
            color: #48bb78;
            font-size: 11px; /* Further reduced font size */
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .video-container {
            flex: 1;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
        }

        .video-feed {
            max-width: 100%;
            max-height: 100%;
            border-radius: 8px; /* Slightly smaller border-radius */
            box-shadow: 0 6px 20px -6px rgba(0, 0, 0, 0.5); /* Adjusted shadow */
        }

        .drawing-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            cursor: crosshair;
            border-radius: 8px; /* Slightly smaller border-radius */
        }

        .placeholder-content {
            text-align: center;
            color: #4a5568;
            max-width: 350px; /* Further reduced max-width */
            padding: 25px; /* Further reduced padding */
        }

        .placeholder-icon {
            font-size: 50px; /* Further reduced font size */
            margin-bottom: 16px; /* Further reduced margin */
            opacity: 0.6;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .placeholder-title {
            font-size: 18px; /* Further reduced font size */
            font-weight: 700;
            margin-bottom: 10px; /* Further reduced margin */
            color: #718096;
        }

        .placeholder-description {
            font-size: 13px; /* Further reduced font size */
            line-height: 1.5; /* Adjusted line height */
            color: #4a5568;
        }

        /* Fade animations */
        .fade-in {
            opacity: 1;
            transition: opacity 0.4s ease;
        }

        .fade-out {
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        /* Scrollbar styling */
        .control-panel::-webkit-scrollbar {
            width: 5px; /* Further reduced width */
        }

        .control-panel::-webkit-scrollbar-track {
            background: rgba(74, 85, 104, 0.2);
            border-radius: 2.5px; /* Further reduced border-radius */
        }

        .control-panel::-webkit-scrollbar-thumb {
            background: rgba(102, 126, 234, 0.4);
            border-radius: 2.5px; /* Further reduced border-radius */
        }

        .control-panel::-webkit-scrollbar-thumb:hover {
            background: rgba(102, 126, 234, 0.6);
        }

        /* Instructions Popup */
        .instructions-popup-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(8px);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .instructions-popup-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .instructions-popup-content {
            width: 90%;
            max-width: 500px;
            background: linear-gradient(145deg, #1a202c 0%, #2d3748 100%);
            border-radius: 12px;
            box-shadow: 0 20px 50px -12px rgba(0, 0, 0, 0.7);
            border: 1px solid rgba(74, 85, 104, 0.3);
            padding: 30px;
            color: #f7fafc;
            transform: scale(0.9);
            transition: all 0.3s ease;
        }

        .instructions-popup-overlay.active .instructions-popup-content {
            transform: scale(1);
        }

        .instructions-popup-content .instructions-title {
            color: #667eea;
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            padding-bottom: 10px;
            border-bottom: 2px solid rgba(102, 126, 234, 0.2);
        }

        .instructions-popup-content .instruction-item {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 12px;
            font-size: 14px;
            line-height: 1.6;
        }

        .instructions-popup-content .instruction-icon {
            color: #667eea;
            font-size: 16px;
            margin-top: 2px;
        }

        .instructions-popup-content .instruction-text {
            color: #cbd5e0;
        }

        .instructions-popup-content .instruction-action {
            color: #f7fafc;
            font-weight: 600;
        }

        .instructions-popup-close-btn {
            background: rgba(74, 85, 104, 0.2);
            border: none;
            color: #a0aec0;
            width: 38px;
            height: 38px;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            font-size: 16px;
            margin-top: 20px;
            float: right; /* Position to the right */
        }

        .instructions-popup-close-btn:hover {
            background: rgba(245, 101, 101, 0.15);
            color: #f56565;
            transform: scale(1.05);
        }

        /* Responsive design */
        @media (max-width: 1200px) {
            .focus-areas-modal {
                width: 95%;
                height: 90vh;
                flex-direction: column;
            }

            .control-panel {
                width: 100%;
                height: 280px;
                border-right: none;
                border-bottom: 1px solid rgba(74, 85, 104, 0.3);
            }

            .modal-body {
                padding-top: 120px;
            }

            .control-group {
                margin-bottom: 20px;
            }
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 10px; /* Adjusted gap */
                align-items: flex-start;
            }

            .header-right {
                width: 100%;
                justify-content: space-between;
            }

            .modal-body {
                padding-top: 130px; /* Adjusted padding */
            }
        }
    </style>
</head>
<body style="background: #0f172a; margin: 0; padding: 20px; min-height: 100vh; display: flex; align-items: center; justify-content: center;">
    
    <div id="focusAreasModal" class="focus-areas-modal-overlay">
        <div class="focus-areas-modal">
            <div class="modal-header">
                <div class="header-content">
                    <div class="header-left">
                        <div class="brand-icon">
                            <i class="fas fa-crosshairs"></i>
                        </div>
                        <div class="header-title">
                            <h1>Focus Areas Configuration</h1>
                            <p class="header-subtitle">Define regions of interest for crowd detection monitoring</p>
                        </div>
                    </div>
                    <div class="header-right">
                        <div id="connectionStatus" class="connection-status disconnected">
                            <i class="fas fa-circle"></i>
                            <span>Disconnected</span>
                        </div>
                        <button class="help-btn" onclick="showInstructionsPopup()">
                            <i class="fas fa-question-circle"></i>
                        </button>
                        <button class="close-modal-btn" onclick="hideFocusAreasModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="modal-body">
                <div class="control-panel">
                    <div class="control-group">
                        <h3 class="control-group-title">
                            <i class="fas fa-video"></i>
                            Camera Controls
                        </h3>
                        <div class="form-field">
                            <label class="field-label">Select Camera</label>
                            <select id="cameraSelect" class="field-select">
                                <option value="">Choose a camera...</option>
                            </select>
                        </div>
                        <button class="control-btn btn-primary" onclick="loadSelectedCamera()">
                            <i class="fas fa-play"></i>
                            <span>Load Camera</span>
                        </button>
                        <button class="control-btn btn-secondary" onclick="stopCamera()">
                            <i class="fas fa-stop"></i>
                            <span>Stop Camera</span>
                        </button>
                    </div>

                    <div class="control-group">
                        <h3 class="control-group-title">
                            <i class="fas fa-draw-polygon"></i>
                            Region Controls
                        </h3>
                        <button class="control-btn btn-success" onclick="sendCoordinates()">
                            <i class="fas fa-save"></i>
                            <span>Save Areas</span>
                        </button>
                        <button class="control-btn btn-danger" onclick="clearPolygons()">
                            <i class="fas fa-trash-alt"></i>
                            <span>Clear All</span>
                        </button>
                    </div>
                </div>

                <div class="video-section">
                    <div class="video-header">
                        <div class="camera-info">
                            <span>Active Camera: </span>
                            <span id="camera-info" class="camera-name">No camera selected</span>
                        </div>
                        <div class="ready-status">
                            <i class="fas fa-check-circle"></i>
                            <span>Ready to configure</span>
                        </div>
                    </div>

                    <div class="video-container">
                        <img id="video" class="video-feed fade-out" alt="Camera Feed" />
                        <canvas id="canvas" class="drawing-canvas fade-out" width="1000" height="700"></canvas>
                        
                        <div class="instruction-div fade-in">
                            <div class="placeholder-content">
                                <div class="placeholder-icon">
                                    <i class="fas fa-video-slash"></i>
                                </div>
                                <h3 class="placeholder-title">Select a camera to begin</h3>
                                <p class="placeholder-description">Choose a camera from the control panel and click "Load Camera" to start defining focus areas for crowd detection monitoring.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="instructionsPopup" class="instructions-popup-overlay">
        <div class="instructions-popup-content">
            <div class="instructions-title">
                <i class="fas fa-info-circle"></i>
                How to Use
            </div>
            <div class="instruction-item">
                <i class="fas fa-mouse-pointer instruction-icon"></i>
                <div class="instruction-text">
                    <span class="instruction-action">Click</span> on the video to add points and create focus areas
                </div>
            </div>
            <div class="instruction-item">
                <i class="fas fa-hand-pointer instruction-icon"></i>
                <div class="instruction-text">
                    <span class="instruction-action">Double-click</span> to complete the current focus area
                </div>
            </div>
            <div class="instruction-item">
                <i class="fas fa-save instruction-icon"></i>
                <div class="instruction-text">
                    <span class="instruction-action">Save</span> to store your configured areas permanently
                </div>
            </div>
            <button class="instructions-popup-close-btn" onclick="hideInstructionsPopup()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <script>
        // Global variables
        let videoElement = document.getElementById("video");
        let canvas = document.getElementById("canvas");
        let instructionDiv = document.querySelector(".instruction-div");
        let ctx = canvas.getContext("2d");
        let cameraInfo = document.getElementById("camera-info");
        let cameraSelect = document.getElementById("cameraSelect");
        let connectionStatus = document.getElementById("connectionStatus");
        let cam_name = "";
        let selectedCameraName = "";
        let availableCameras = {};

        let isDrawing = false;
        let polygons = [];
        let currentPolygon = [];
        let ws = null;

        // Modal functions
        function showFocusAreasModal() {
            document.getElementById('focusAreasModal').classList.add('active');
            document.body.style.overflow = 'hidden';
            loadAvailableCameras();
        }

        function hideFocusAreasModal() {
            document.getElementById('focusAreasModal').classList.remove('active');
            document.body.style.overflow = 'auto';
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        // Instructions Popup functions
        function showInstructionsPopup() {
            document.getElementById('instructionsPopup').classList.add('active');
        }

        function hideInstructionsPopup() {
            document.getElementById('instructionsPopup').classList.remove('active');
        }

        // Close modal when clicking outside
        document.getElementById('focusAreasModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideFocusAreasModal();
            }
        });

        // Close instructions popup when clicking outside
        document.getElementById('instructionsPopup').addEventListener('click', function(e) {
            if (e.target === this) {
                hideInstructionsPopup();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                hideFocusAreasModal();
                hideInstructionsPopup(); // Also close instructions popup
            }
        });

        // Function to load available cameras
        async function loadAvailableCameras() {
            try {
                const response = await fetch("/crowd_detection/get-cameras");
                const cameras = await response.json();
                availableCameras = cameras;

                // Populate camera dropdown
                cameraSelect.innerHTML = '<option value="">Choose a camera...</option>';
                for (const [cameraName, cameraData] of Object.entries(cameras)) {
                    const option = document.createElement('option');
                    option.value = cameraName;
                    option.textContent = cameraName;
                    cameraSelect.appendChild(option);
                }
            } catch (error) {
                console.error("Failed to load cameras:", error);
                showNotification("Failed to load cameras", "error");
            }
        }

        // Function to load selected camera
        function loadSelectedCamera() {
            selectedCameraName = cameraSelect.value;
            if (!selectedCameraName) {
                showNotification("Please select a camera first", "warning");
                return;
            }

            cam_name = selectedCameraName;
            cameraInfo.textContent = selectedCameraName;

            // Clear existing polygons when switching cameras
            clearPolygons();

            // Load existing ROI for this camera
            loadExistingROI();

            // Start camera feed
            startCameraFeed();
        }

        // Function to load existing ROI for the selected camera
        function loadExistingROI() {
            if (availableCameras[selectedCameraName] && availableCameras[selectedCameraName][1]) {
                const existingROI = availableCameras[selectedCameraName][1];
                if (existingROI.length > 0) {
                    polygons = existingROI;
                    drawPolygons();
                    showNotification(`Loaded ${existingROI.length} existing focus areas`, "success");
                }
            }
        }

        // Function to start camera feed
        function startCameraFeed() {
            // First, start the camera handler with the selected camera
            fetch("/crowd_detection/camera/start")
                .then(response => response.json())
                .then(() => {
                    // Set the camera to the selected one
                    return setSpecificCamera(selectedCameraName);
                })
                .then(() => {
                    // Start WebSocket connection
                    ws = new WebSocket("ws://localhost:8000/crowd_detection/addroi");
                    ws.binaryType = "arraybuffer";

                    ws.onopen = () => {
                        updateConnectionStatus(true);
                        showVideoElements();
                    };

                    ws.onmessage = (event) => {
                        const data = new Uint8Array(event.data);
                        const separatorIndex = data.indexOf(10);

                        if (separatorIndex === -1) {
                            console.error("Invalid data format received.");
                            return;
                        }

                        const metadataJson = new TextDecoder().decode(data.slice(0, separatorIndex));
                        const frameData = data.slice(separatorIndex + 1);

                        try {
                            const metadata = JSON.parse(metadataJson);
                            const blob = new Blob([frameData], { type: "image/jpeg" });
                            const url = URL.createObjectURL(blob);
                            videoElement.src = url;
                        } catch (error) {
                            console.error("Error parsing WebSocket message:", error);
                        }
                    };

                    ws.onclose = () => {
                        updateConnectionStatus(false);
                    };

                    ws.onerror = (error) => {
                        console.error("WebSocket error:", error);
                        updateConnectionStatus(false);
                    };
                })
                .catch(error => {
                    console.error("Error starting camera:", error);
                    showNotification("Failed to start camera", "error");
                });
        }

        // Function to set specific camera
        async function setSpecificCamera(cameraName) {
            try {
                const response = await fetch(`/crowd_detection/camera/select/${cameraName}`, {
                    method: "POST"
                });
                const result = await response.json();

                if (result.status !== "success") {
                    throw new Error(result.message || "Failed to select camera");
                }

                return result;
            } catch (error) {
                console.error("Error selecting camera:", error);
                throw error;
            }
        }

        // Function to update connection status
        function updateConnectionStatus(connected) {
            if (connected) {
                connectionStatus.className = "connection-status connected";
                connectionStatus.innerHTML = '<i class="fas fa-circle"></i> <span>Connected</span>';
            } else {
                connectionStatus.className = "connection-status disconnected";
                connectionStatus.innerHTML = '<i class="fas fa-circle"></i> <span>Disconnected</span>';
            }
        }

        // Function to show video elements
        function showVideoElements() {
            videoElement.classList.remove("fade-out");
            videoElement.classList.add("fade-in");
            canvas.classList.remove("fade-out");
            canvas.classList.add("fade-in");
            instructionDiv.classList.remove("fade-in");
            instructionDiv.classList.add("fade-out");
        }

        // Function to hide video elements
        function hideVideoElements() {
            videoElement.classList.remove("fade-in");
            videoElement.classList.add("fade-out");
            canvas.classList.remove("fade-in");
            canvas.classList.add("fade-out");
            instructionDiv.classList.remove("fade-out");
            instructionDiv.classList.add("fade-in");
        }

        // Function to stop camera
        function stopCamera() {
            if (ws) {
                ws.close();
                ws = null;
            }

            fetch("/crowd_detection/camera/stop")
                .then(response => response.json())
                .then(() => {
                    videoElement.src = "";
                    hideVideoElements();
                    updateConnectionStatus(false);
                    showNotification("Camera stopped", "info");
                })
                .catch(error => {
                    console.error("Error stopping camera:", error);
                    showNotification("Error stopping camera", "error");
                });
        }

        // Function to show notifications
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 30px;
                right: 30px;
                padding: 20px 28px;
                border-radius: 16px;
                color: white;
                font-size: 15px;
                font-weight: 600;
                z-index: 99999;
                transform: translateX(120%);
                transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
                box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.4);
                max-width: 400px;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.1);
            `;

            const colors = {
                'success': '#0070f3',
                'error': '#ef4444',
                'warning': '#f59e0b',
                'info': '#06b6d4'
            };

            notification.style.backgroundColor = colors[type] || '#0070f3';
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            setTimeout(() => {
                notification.style.transform = 'translateX(120%)';
                setTimeout(() => {
                    notification.remove();
                }, 400);
            }, 3000);
        }

        // Function to clear polygons
        function clearPolygons() {
            polygons = [];
            drawPolygons();
        }

        // Function to draw polygons
        function drawPolygons() {  
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            polygons.forEach(polygon => {
                ctx.beginPath();
                ctx.moveTo(polygon[0][0], polygon[0][1]);
                for (let i = 1; i < polygon.length; i++) {
                    ctx.lineTo(polygon[i][0], polygon[i][1]);
                }
                ctx.closePath();
                ctx.fillStyle = "rgba(255, 0, 0, 0.3)";
                ctx.fill();
            });
        }

        // Function to handle polygon creation
        function handlePolygonCreation() {
            if (isCreatingPolygon) {
                isCreatingPolygon = false;
                polygons.push(currentPolygon);
                currentPolygon = [];
                drawPolygons();
            } else {
                isCreatingPolygon = true;
            }
        } 

        // Function to handle polygon deletion
        function handlePolygonDeletion() {
            if (polygons.length > 0) {
                polygons.pop();
                drawPolygons();
            }
        }

        // Automatically show the modal on page load
        window.onload = function() {
            showFocusAreasModal();
        };
    </script>
</body>
</html>