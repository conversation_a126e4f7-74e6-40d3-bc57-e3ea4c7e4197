from sqlalchemy import create_engine, Column, Integer, <PERSON>, Boolean, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import os

# Database configuration - Using existing project database
DATABASE_URL = "mysql+pymysql://root:1234@localhost/surveillance_system"

engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

# User model - Matching existing database structure
class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    full_name = Column(String(100))
    hashed_password = Column(String(255))
    role = Column(String(20), default="viewer")  # admin or viewer
    disabled = Column(<PERSON>olean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# Dependency to get database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_database():
    """Initialize database tables and create default users"""
    try:
        print("🚀 Starting Vigilant Eye Authentication Service...")

        # First, try to add missing columns to existing users table
        migrate_users_table()

        # Create tables
        Base.metadata.create_all(bind=engine)
        print("✅ Database tables created successfully!")

        # Create default users
        create_default_users()

        print("🎉 Database initialized successfully!")
        print("✅ Authentication service startup complete!")
        return True
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False

def migrate_users_table():
    """Add missing columns to users table if they don't exist"""
    try:
        from sqlalchemy import text

        with engine.connect() as conn:
            # Check and add columns one by one
            columns_to_add = [
                ("full_name", "VARCHAR(100)"),
                ("hashed_password", "VARCHAR(255)"),
                ("role", "VARCHAR(20) DEFAULT 'viewer'"),
                ("disabled", "BOOLEAN DEFAULT FALSE"),
                ("created_at", "DATETIME DEFAULT CURRENT_TIMESTAMP"),
                ("updated_at", "DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")
            ]

            for column_name, column_def in columns_to_add:
                try:
                    # Try to select from the column to see if it exists
                    conn.execute(text(f"SELECT {column_name} FROM users LIMIT 1"))
                except Exception as e:
                    if "Unknown column" in str(e) or "no such column" in str(e):
                        # Column doesn't exist, add it
                        conn.execute(text(f"ALTER TABLE users ADD COLUMN {column_name} {column_def}"))
                        conn.commit()
                        print(f"✅ Added column: {column_name}")

    except Exception as e:
        print(f"⚠️ Migration warning: {e}")
        # Don't fail the startup, just continue

def create_default_users():
    """Create default admin and viewer users with roles"""
    try:
        from app.utils import get_password_hash
        from app.models import Role, Permission, RolePermission

        db = SessionLocal()

        # Create roles if they don't exist
        admin_role = db.query(Role).filter(Role.name == "admin").first()
        if not admin_role:
            admin_role = Role(name="admin", description="Administrator role")
            db.add(admin_role)
            print("✅ Admin role created")

        viewer_role = db.query(Role).filter(Role.name == "viewer").first()
        if not viewer_role:
            viewer_role = Role(name="viewer", description="Viewer role")
            db.add(viewer_role)
            print("✅ Viewer role created")

        # Create permissions if they don't exist
        permissions = [
            ("admin_access", "Full administrative access"),
            ("create_user", "Create new users"),
            ("view_data", "View application data"),
            ("manage_ai_modules", "Manage AI detection modules")
        ]

        for perm_name, perm_desc in permissions:
            permission = db.query(Permission).filter(Permission.name == perm_name).first()
            if not permission:
                permission = Permission(name=perm_name, description=perm_desc)
                db.add(permission)

        db.commit()

        # Assign permissions to roles
        admin_permissions = ["admin_access", "create_user", "view_data", "manage_ai_modules"]
        viewer_permissions = ["view_data"]

        for perm_name in admin_permissions:
            permission = db.query(Permission).filter(Permission.name == perm_name).first()
            if permission:
                role_perm = db.query(RolePermission).filter(
                    RolePermission.role_id == admin_role.id,
                    RolePermission.permission_id == permission.id
                ).first()
                if not role_perm:
                    role_perm = RolePermission(role_id=admin_role.id, permission_id=permission.id)
                    db.add(role_perm)

        for perm_name in viewer_permissions:
            permission = db.query(Permission).filter(Permission.name == perm_name).first()
            if permission:
                role_perm = db.query(RolePermission).filter(
                    RolePermission.role_id == viewer_role.id,
                    RolePermission.permission_id == permission.id
                ).first()
                if not role_perm:
                    role_perm = RolePermission(role_id=viewer_role.id, permission_id=permission.id)
                    db.add(role_perm)

        # Create default users
        admin_user = db.query(User).filter(User.username == "admin").first()
        if not admin_user:
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                full_name="Administrator",
                hashed_password=get_password_hash("admin123"),
                disabled=False
            )
            db.add(admin_user)
            db.commit()
            db.refresh(admin_user)

            # Assign admin role
            if admin_role not in admin_user.roles:
                admin_user.roles.append(admin_role)
            print("✅ Default admin user created (admin/admin123)")

        viewer_user = db.query(User).filter(User.username == "viewer").first()
        if not viewer_user:
            viewer_user = User(
                username="viewer",
                email="<EMAIL>",
                full_name="Viewer User",
                hashed_password=get_password_hash("viewer123"),
                disabled=False
            )
            db.add(viewer_user)
            db.commit()
            db.refresh(viewer_user)

            # Assign viewer role
            if viewer_role not in viewer_user.roles:
                viewer_user.roles.append(viewer_role)
            print("✅ Default viewer user created (viewer/viewer123)")

        db.commit()
        db.close()

    except Exception as e:
        print(f"❌ Error creating default users: {e}")
        if 'db' in locals():
            db.close()
