from sqlalchemy import create_engine, Column, Integer, <PERSON>, Boolean, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import os

# Database configuration - Using existing project database
DATABASE_URL = "mysql+pymysql://root:1234@localhost/surveillance_system"

engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

# User model - Matching existing database structure
class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    full_name = Column(String(100))
    hashed_password = Column(String(255))
    role = Column(String(20), default="viewer")  # admin or viewer
    disabled = Column(<PERSON>olean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# Dependency to get database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_database():
    """Initialize database tables and create default users"""
    try:
        print("🚀 Starting Vigilant Eye Authentication Service...")

        # First, try to add missing columns to existing users table
        migrate_users_table()

        # Create tables
        Base.metadata.create_all(bind=engine)
        print("✅ Database tables created successfully!")

        # Create default users
        create_default_users()

        print("🎉 Database initialized successfully!")
        print("✅ Authentication service startup complete!")
        return True
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False

def migrate_users_table():
    """Add missing columns to users table if they don't exist"""
    try:
        from sqlalchemy import text

        with engine.connect() as conn:
            # Check and add columns one by one
            columns_to_add = [
                ("full_name", "VARCHAR(100)"),
                ("hashed_password", "VARCHAR(255)"),
                ("role", "VARCHAR(20) DEFAULT 'viewer'"),
                ("disabled", "BOOLEAN DEFAULT FALSE"),
                ("created_at", "DATETIME DEFAULT CURRENT_TIMESTAMP"),
                ("updated_at", "DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")
            ]

            for column_name, column_def in columns_to_add:
                try:
                    # Try to select from the column to see if it exists
                    conn.execute(text(f"SELECT {column_name} FROM users LIMIT 1"))
                except Exception as e:
                    if "Unknown column" in str(e) or "no such column" in str(e):
                        # Column doesn't exist, add it
                        conn.execute(text(f"ALTER TABLE users ADD COLUMN {column_name} {column_def}"))
                        conn.commit()
                        print(f"✅ Added column: {column_name}")

    except Exception as e:
        print(f"⚠️ Migration warning: {e}")
        # Don't fail the startup, just continue

def create_default_users():
    """Create default admin and viewer users (simplified)"""
    try:
        from app.utils import get_password_hash

        db = SessionLocal()

        # Create default admin user
        admin_user = db.query(User).filter(User.username == "admin").first()
        if not admin_user:
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                full_name="Administrator",
                hashed_password=get_password_hash("admin123"),
                role="admin",
                disabled=False
            )
            db.add(admin_user)
            print("✅ Default admin user created (admin/admin123)")

        # Create default viewer user
        viewer_user = db.query(User).filter(User.username == "viewer").first()
        if not viewer_user:
            viewer_user = User(
                username="viewer",
                email="<EMAIL>",
                full_name="Viewer User",
                hashed_password=get_password_hash("viewer123"),
                role="viewer",
                disabled=False
            )
            db.add(viewer_user)
            print("✅ Default viewer user created (viewer/viewer123)")

        db.commit()
        db.close()

    except Exception as e:
        print(f"❌ Error creating default users: {e}")
        if 'db' in locals():
            db.close()
