#!/usr/bin/env python3

print("🔧 Testing auth-service imports...")

try:
    print("Testing basic imports...")
    import fastapi
    print("✅ FastAPI imported")
    
    import uvicorn
    print("✅ Uvicorn imported")
    
    import sqlalchemy
    print("✅ SQLAlchemy imported")
    
    import pymysql
    print("✅ PyMySQL imported")
    
    import jose
    print("✅ Jose imported")
    
    import passlib
    print("✅ Passlib imported")
    
    print("\nTesting app imports...")
    
    from app.database import User, get_db
    print("✅ Database imports working")
    
    from app.auth import Token, UserCreate, get_password_hash
    print("✅ Auth imports working")
    
    print("\n🎉 All imports successful!")
    
except Exception as e:
    print(f"❌ Import failed: {e}")
    import traceback
    traceback.print_exc()
