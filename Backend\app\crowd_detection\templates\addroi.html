<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ROI Configuration - VigilantEye</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary: #667eea;
            --primary-dark: #5a6fd8;
            --primary-light: #764ba2;
            --secondary: #f093fb;
            --success: #10b981;
            --danger: #ef4444;
            --warning: #f59e0b;
            --info: #3b82f6;
            --dark: #1e293b;
            --gray: #64748b;
            --gray-light: #94a3b8;
            --border: #e2e8f0;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --backdrop: rgba(0, 0, 0, 0.5);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--backdrop);
            color: var(--dark);
            height: 100vh;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(8px);
        }

        /* Modal Container */
        .roi-modal {
            background: #2a2d3a;
            border-radius: 12px;
            box-shadow: var(--shadow-lg);
            width: 900px;
            height: 500px;
            display: flex;
            flex-direction: row;
            overflow: hidden;
            animation: modalSlideIn 0.3s ease-out;
            border: 1px solid #3a3d4a;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: scale(0.95) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        /* Left Panel - Controls */
        .left-panel {
            width: 300px;
            background: #2a2d3a;
            display: flex;
            flex-direction: column;
            border-right: 1px solid #3a3d4a;
        }

        /* Modal Header */
        .modal-header {
            background: #667eea;
            color: white;
            padding: 1rem 1.25rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-radius: 12px 0 0 0;
        }

        .header-content {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .header-icon {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
        }

        .header-text h1 {
            font-size: 1rem;
            font-weight: 600;
            margin: 0;
            margin-bottom: 0.125rem;
        }

        .header-text p {
            font-size: 0.75rem;
            opacity: 0.9;
            margin: 0;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-badge {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.5rem;
            background: rgba(239, 68, 68, 0.2);
            border-radius: 12px;
            font-size: 0.625rem;
            font-weight: 600;
            text-transform: uppercase;
            color: #fecaca;
        }

        .status-badge.connected {
            background: rgba(16, 185, 129, 0.2);
            color: #d1fae5;
        }

        .status-badge.disconnected {
            background: rgba(239, 68, 68, 0.2);
            color: #fecaca;
        }

        .close-btn {
            width: 24px;
            height: 24px;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 4px;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            transition: all 0.2s ease;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .info-btn {
            width: 24px;
            height: 24px;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            transition: all 0.2s ease;
        }

        .info-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        /* Controls Content */
        .controls-content {
            padding: 1.25rem;
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .section-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.75rem;
        }

        .section-icon {
            width: 16px;
            height: 16px;
            color: #667eea;
        }

        .section-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #e2e8f0;
            margin: 0;
        }

        .camera-status-info {
            background: #1e293b;
            border-radius: 8px;
            padding: 0.75rem;
            border: 1px solid #3a3d4a;
            margin-bottom: 1rem;
        }

        .camera-status-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .camera-status-row:last-child {
            margin-bottom: 0;
        }

        .camera-status-label {
            font-size: 0.75rem;
            color: #94a3b8;
        }

        .camera-status-value {
            font-size: 0.75rem;
            color: #e2e8f0;
            font-weight: 500;
        }

        .ready-status {
            color: #10b981;
            font-weight: 600;
        }

        /* Form Styles */
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-label {
            font-weight: 500;
            color: #94a3b8;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .form-select {
            padding: 0.75rem;
            border: 1px solid #3a3d4a;
            border-radius: 6px;
            background: #1e293b;
            color: #e2e8f0;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23667eea' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.75rem center;
            background-repeat: no-repeat;
            background-size: 1em 1em;
            padding-right: 2.5rem;
        }

        .form-select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        /* Button Styles */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            white-space: nowrap;
            width: 100%;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
            transform: translateY(-1px);
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #475569;
            color: white;
        }

        .btn-secondary:hover {
            background: #334155;
            transform: translateY(-1px);
        }

        .btn-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        /* Right Panel - Video */
        .right-panel {
            flex: 1;
            background: #1e293b;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .video-wrapper {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #1e293b;
            position: relative;
        }

        #video {
            max-width: 100%;
            max-height: 100%;
            display: block;
        }

        #canvas {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            cursor: crosshair;
        }

        .instruction-div {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #94a3b8;
            text-align: center;
            padding: 2rem;
        }

        .instruction-div i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.3;
            color: #475569;
        }

        .instruction-div h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #e2e8f0;
        }

        .instruction-div p {
            font-size: 0.875rem;
            max-width: 300px;
            opacity: 0.7;
            line-height: 1.5;
        }

        /* Info Modal */
        .info-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .info-modal.active {
            opacity: 1;
            visibility: visible;
        }

        .info-modal-content {
            background: #2a2d3a;
            border-radius: 12px;
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            border: 1px solid #3a3d4a;
            color: #e2e8f0;
        }

        .info-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .info-modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #667eea;
        }

        .info-modal-close {
            background: none;
            border: none;
            color: #94a3b8;
            cursor: pointer;
            font-size: 1.5rem;
        }

        .info-steps {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .info-steps li {
            margin-bottom: 1rem;
            padding-left: 2rem;
            position: relative;
        }

        .info-steps li::before {
            content: counter(step-counter);
            counter-increment: step-counter;
            position: absolute;
            left: 0;
            top: 0;
            background: #667eea;
            color: white;
            width: 1.5rem;
            height: 1.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .info-steps {
            counter-reset: step-counter;
        }

        /* Animations */
        .fade-out {
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .fade-in {
            opacity: 1;
            transition: opacity 0.3s ease;
        }

        /* Notification Styles */
        .notification {
            position: fixed;
            top: 2rem;
            right: 2rem;
            padding: 1rem 1.5rem;
            border-radius: 10px;
            color: white;
            font-size: 0.875rem;
            font-weight: 600;
            z-index: 1000;
            transform: translateX(120%);
            transition: transform 0.3s ease;
            box-shadow: var(--shadow-lg);
            max-width: 400px;
        }

        .notification.success {
            background: linear-gradient(135deg, var(--success), #059669);
        }

        .notification.error {
            background: linear-gradient(135deg, var(--danger), #dc2626);
        }

        .notification.warning {
            background: linear-gradient(135deg, var(--warning), #d97706);
        }

        .notification.info {
            background: linear-gradient(135deg, var(--info), #2563eb);
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .roi-modal {
                width: 95vw;
                height: 90vh;
                flex-direction: column;
            }

            .left-panel {
                width: 100%;
                max-height: 200px;
                border-right: none;
                border-bottom: 1px solid #3a3d4a;
            }

            .controls-content {
                padding: 1rem;
                gap: 1rem;
            }

            .btn-group {
                flex-direction: row;
                gap: 0.5rem;
            }

            .btn {
                font-size: 0.75rem;
                padding: 0.5rem 0.75rem;
            }
        }

        @media (max-width: 768px) {
            .roi-modal {
                width: 98vw;
                height: 95vh;
            }

            .modal-header {
                padding: 0.75rem 1rem;
            }

            .header-text h1 {
                font-size: 0.875rem;
            }

            .header-text p {
                font-size: 0.625rem;
            }

            .btn-group {
                flex-direction: column;
            }

            .instruction-div {
                padding: 1.5rem;
            }

            .instruction-div i {
                font-size: 3rem;
            }

            .instruction-div h3 {
                font-size: 1rem;
            }

            .instruction-div p {
                font-size: 0.75rem;
            }
        }
    </style>
</head>

<body>
    <!-- ROI Configuration Modal -->
    <div class="roi-modal">
        <!-- Left Panel - Controls -->
        <div class="left-panel">
            <!-- Modal Header -->
            <div class="modal-header">
                <div class="header-content">
                    <div class="header-icon">
                        <i class="fas fa-crosshairs"></i>
                    </div>
                    <div class="header-text">
                        <h1>Focus Areas Configuration</h1>
                        <p>Define regions of interest for crowd detection monitoring</p>
                    </div>
                </div>
                <div class="header-actions">
                    <div class="status-badge disconnected" id="connectionStatus">
                        <i class="fas fa-circle"></i>
                        Disconnected
                    </div>
                    <button class="info-btn" onclick="showInfoModal()" title="Information">
                        <i class="fas fa-question"></i>
                    </button>
                    <button class="close-btn" onclick="closeModal()" title="Close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <!-- Controls Content -->
            <div class="controls-content">
                <!-- Camera Status Info -->
                <div class="camera-status-info">
                    <div class="camera-status-row">
                        <span class="camera-status-label">Active Camera:</span>
                        <span class="camera-status-value" id="activeCameraName">No camera selected</span>
                    </div>
                    <div class="camera-status-row">
                        <span class="camera-status-label">Status:</span>
                        <span class="camera-status-value ready-status" id="cameraStatusText">READY TO CONFIGURE</span>
                    </div>
                </div>

                <!-- Camera Controls Section -->
                <div>
                    <div class="section-header">
                        <i class="fas fa-video section-icon"></i>
                        <h3 class="section-title">Camera Controls</h3>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="cameraSelect">Select Camera</label>
                        <select id="cameraSelect" class="form-select">
                            <option value="">Choose a camera...</option>
                        </select>
                    </div>
                    <button class="btn btn-primary" onclick="loadSelectedCamera()">
                        <i class="fas fa-play"></i>
                        LOAD CAMERA
                    </button>
                </div>

                <!-- Region Controls Section -->
                <div>
                    <div class="section-header">
                        <i class="fas fa-draw-polygon section-icon"></i>
                        <h3 class="section-title">Region Controls</h3>
                    </div>
                    <div class="btn-group">
                        <button class="btn btn-success" onclick="sendCoordinates()">
                            <i class="fas fa-save"></i>
                            SAVE AREAS
                        </button>
                        <button class="btn btn-danger" onclick="clearPolygons()">
                            <i class="fas fa-trash-alt"></i>
                            CLEAR ALL
                        </button>
                        <button class="btn btn-secondary" onclick="stopCamera()">
                            <i class="fas fa-stop"></i>
                            STOP CAMERA
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Panel - Video -->
        <div class="right-panel">
            <div class="video-wrapper">
                <img id="video" alt="Camera Feed" class="fade-out" width="640" height="480" />
                <canvas id="canvas" width="640" class="fade-out" height="480"></canvas>

                <div class="instruction-div">
                    <i class="fas fa-video-slash"></i>
                    <h3>Select a camera to begin</h3>
                    <p>Choose a camera from the control panel and click "Load Camera" to start defining focus areas for crowd detection.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Info Modal -->
    <div class="info-modal" id="infoModal">
        <div class="info-modal-content">
            <div class="info-modal-header">
                <h3 class="info-modal-title">ROI Configuration Guide</h3>
                <button class="info-modal-close" onclick="hideInfoModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <ol class="info-steps">
                <li><strong>Select Camera:</strong> Choose a camera from the dropdown menu.</li>
                <li><strong>Load Camera:</strong> Click "Load Camera" to start the video feed.</li>
                <li><strong>Draw Areas:</strong> Click points on the video to create polygon boundaries.</li>
                <li><strong>Complete Area:</strong> Double-click to complete a focus area.</li>
                <li><strong>Save Configuration:</strong> Click "Save Areas" to store your configuration.</li>
                <li><strong>Management:</strong> Use "Clear All" to remove areas or "Stop Camera" to end session.</li>
            </ol>
        </div>
    </div>

    <script>
        // Global variables
        let videoElement = document.getElementById("video");
        let canvas = document.getElementById("canvas");
        let instructionDiv = document.querySelector(".instruction-div");
        let ctx = canvas.getContext("2d");
        let cameraInfo = document.getElementById("camera-info");
        let cameraSelect = document.getElementById("cameraSelect");
        let connectionStatus = document.getElementById("connectionStatus");
        let cam_name = "";
        let selectedCameraName = "";
        let availableCameras = {};

        let isDrawing = false;
        let polygons = [];
        let currentPolygon = [];
        let ws = null;

        // Load available cameras on page load
        document.addEventListener("DOMContentLoaded", async function() {
            await loadAvailableCameras();
        });

        // Close modal function
        function closeModal() {
            // Stop camera if running
            if (ws) {
                ws.close();
                ws = null;
            }

            fetch("/crowd_detection/camera/stop")
                .then(() => {
                    // Navigate back to crowd detection dashboard
                    window.location.href = "/crowd_detection/crowd_detection";
                })
                .catch(error => {
                    console.error("Error stopping camera:", error);
                    // Navigate anyway
                    window.location.href = "/crowd_detection/crowd_detection";
                });
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
        });

        // Info modal functions
        function showInfoModal() {
            const modal = document.getElementById('infoModal');
            modal.classList.add('active');
        }

        function hideInfoModal() {
            const modal = document.getElementById('infoModal');
            modal.classList.remove('active');
        }

        // Close info modal when clicking outside
        document.getElementById('infoModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideInfoModal();
            }
        });

        // Function to load available cameras
        async function loadAvailableCameras() {
            try {
                const response = await fetch("/crowd_detection/get-cameras");
                const cameras = await response.json();
                availableCameras = cameras;

                // Populate camera dropdown
                cameraSelect.innerHTML = '<option value="">Select Camera...</option>';
                for (const [cameraName, cameraData] of Object.entries(cameras)) {
                    const option = document.createElement('option');
                    option.value = cameraName;
                    option.textContent = cameraName;
                    cameraSelect.appendChild(option);
                }
            } catch (error) {
                console.error("Failed to load cameras:", error);
                showNotification("Failed to load cameras", "error");
            }
        }

        // Function to load selected camera
        function loadSelectedCamera() {
            selectedCameraName = cameraSelect.value;
            if (!selectedCameraName) {
                showNotification("Please select a camera first", "warning");
                return;
            }

            cam_name = selectedCameraName;

            // Update status display
            document.getElementById('activeCameraName').textContent = selectedCameraName;
            document.getElementById('cameraStatusText').textContent = 'LOADING...';
            document.getElementById('cameraStatusText').className = 'camera-status-value';

            // Clear existing polygons when switching cameras
            clearPolygons();

            // Load existing ROI for this camera
            loadExistingROI();

            // Start camera feed
            startCameraFeed();
        }

        // Function to load existing ROI for the selected camera
        function loadExistingROI() {
            if (availableCameras[selectedCameraName] && availableCameras[selectedCameraName][1]) {
                const existingROI = availableCameras[selectedCameraName][1];
                if (existingROI.length > 0) {
                    polygons = existingROI;
                    drawPolygons();
                    showNotification(`Loaded ${existingROI.length} existing focus areas`, "success");
                }
            }
        }

        // Function to start camera feed
        function startCameraFeed() {
            // First, start the camera handler with the selected camera
            fetch("/crowd_detection/camera/start")
                .then(response => response.json())
                .then(() => {
                    // Set the camera to the selected one
                    return setSpecificCamera(selectedCameraName);
                })
                .then(() => {
                    // Start WebSocket connection
                    ws = new WebSocket("ws://localhost:8000/crowd_detection/addroi");
                    ws.binaryType = "arraybuffer";

                    ws.onopen = () => {
                        updateConnectionStatus(true);
                        showVideoElements();
                    };

                    ws.onmessage = (event) => {
                        const data = new Uint8Array(event.data);
                        const separatorIndex = data.indexOf(10);

                        if (separatorIndex === -1) {
                            console.error("Invalid data format received.");
                            return;
                        }

                        const metadataJson = new TextDecoder().decode(data.slice(0, separatorIndex));
                        const frameData = data.slice(separatorIndex + 1);

                        try {
                            const metadata = JSON.parse(metadataJson);
                            const blob = new Blob([frameData], { type: "image/jpeg" });
                            const url = URL.createObjectURL(blob);
                            videoElement.src = url;
                        } catch (error) {
                            console.error("Error parsing WebSocket message:", error);
                        }
                    };

                    ws.onclose = () => {
                        updateConnectionStatus(false);
                    };

                    ws.onerror = (error) => {
                        console.error("WebSocket error:", error);
                        updateConnectionStatus(false);
                    };
                })
                .catch(error => {
                    console.error("Error starting camera:", error);
                    showNotification("Failed to start camera", "error");
                });
        }

        // Function to set specific camera
        async function setSpecificCamera(cameraName) {
            try {
                const response = await fetch(`/crowd_detection/camera/select/${cameraName}`, {
                    method: "POST"
                });
                const result = await response.json();

                if (result.status !== "success") {
                    throw new Error(result.message || "Failed to select camera");
                }

                return result;
            } catch (error) {
                console.error("Error selecting camera:", error);
                throw error;
            }
        }

        // Function to update connection status
        function updateConnectionStatus(connected) {
            if (connected) {
                connectionStatus.className = "status-badge connected";
                connectionStatus.innerHTML = '<i class="fas fa-circle"></i> Connected';
                document.getElementById('cameraStatusText').textContent = 'STREAMING';
                document.getElementById('cameraStatusText').className = 'camera-status-value ready-status';
            } else {
                connectionStatus.className = "status-badge disconnected";
                connectionStatus.innerHTML = '<i class="fas fa-circle"></i> Disconnected';
                document.getElementById('cameraStatusText').textContent = 'READY TO CONFIGURE';
                document.getElementById('cameraStatusText').className = 'camera-status-value ready-status';
            }
        }

        // Function to show video elements
        function showVideoElements() {
            videoElement.classList.remove("fade-out");
            videoElement.classList.add("fade-in");
            canvas.classList.remove("fade-out");
            canvas.classList.add("fade-in");
            instructionDiv.classList.remove("fade-in");
            instructionDiv.classList.add("fade-out");
        }

        // Function to hide video elements
        function hideVideoElements() {
            videoElement.classList.remove("fade-in");
            videoElement.classList.add("fade-out");
            canvas.classList.remove("fade-in");
            canvas.classList.add("fade-out");
            instructionDiv.classList.remove("fade-out");
            instructionDiv.classList.add("fade-in");
        }

        // Function to stop camera
        function stopCamera() {
            if (ws) {
                ws.close();
                ws = null;
            }

            fetch("/crowd_detection/camera/stop")
                .then(response => response.json())
                .then(() => {
                    videoElement.src = "";
                    hideVideoElements();
                    updateConnectionStatus(false);
                    showNotification("Camera stopped", "info");
                })
                .catch(error => {
                    console.error("Error stopping camera:", error);
                    showNotification("Error stopping camera", "error");
                });
        }

        // Function to show notifications
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 16px 20px;
                border-radius: 8px;
                color: white;
                font-size: 14px;
                font-weight: 600;
                z-index: 1100;
                transform: translateX(120%);
                transition: transform 0.3s ease;
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
                max-width: 400px;
            `;

            const colors = {
                'success': '#10b981',
                'error': '#ef4444',
                'warning': '#f59e0b',
                'info': '#3b82f6'
            };

            notification.style.backgroundColor = colors[type] || colors.info;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => notification.style.transform = 'translateX(0)', 10);
            setTimeout(() => {
                notification.style.transform = 'translateX(120%)';
                setTimeout(() => notification.remove(), 300);
            }, 4000);
        }





        // Function to clear all polygons and reset the canvas
        function clearPolygons() {
            polygons = [];
            currentPolygon = [];
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            showNotification("All focus areas cleared", "info");
        }

        // Draw polygon on canvas
        canvas.addEventListener("click", (event) => {
            if (!selectedCameraName) {
                showNotification("Please select and load a camera first", "warning");
                return;
            }

            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;
            currentPolygon.push({x: x, y: y});
            drawPolygons();
        });

        // Double-click to close the current polygon
        canvas.addEventListener("dblclick", (event) => {
            event.preventDefault();
            if (currentPolygon.length > 2) {
                // Convert to the format expected by the backend
                const polygonCoords = currentPolygon.map(point => [point.x, point.y]);
                polygons.push(polygonCoords);
                currentPolygon = [];
                drawPolygons();
                showNotification(`Focus area ${polygons.length} created`, "success");
            }
        });

        // Helper: Draw the polygons on the canvas
        function drawPolygons() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Draw completed polygons
            polygons.forEach((polygon, index) => {
                if (polygon.length > 2) {
                    ctx.beginPath();
                    ctx.moveTo(polygon[0][0], polygon[0][1]);

                    for (let i = 1; i < polygon.length; i++) {
                        ctx.lineTo(polygon[i][0], polygon[i][1]);
                    }

                    ctx.closePath();
                    ctx.strokeStyle = '#00ff00';
                    ctx.lineWidth = 3;
                    ctx.stroke();

                    ctx.fillStyle = 'rgba(0, 255, 0, 0.2)';
                    ctx.fill();

                    // Add region label
                    const centroid = getCentroid(polygon);
                    ctx.fillStyle = '#00ff00';
                    ctx.font = 'bold 16px Arial';
                    ctx.fillText(`ROI ${index + 1}`, centroid[0] - 20, centroid[1]);
                }
            });

            // Draw current polygon being drawn
            if (currentPolygon.length > 0) {
                ctx.beginPath();
                if (currentPolygon.length > 0) {
                    ctx.moveTo(currentPolygon[0].x, currentPolygon[0].y);

                    for (let i = 1; i < currentPolygon.length; i++) {
                        ctx.lineTo(currentPolygon[i].x, currentPolygon[i].y);
                    }
                }

                ctx.strokeStyle = '#ffff00';
                ctx.lineWidth = 2;
                ctx.stroke();

                // Draw points
                currentPolygon.forEach(point => {
                    ctx.beginPath();
                    ctx.arc(point.x, point.y, 4, 0, 2 * Math.PI);
                    ctx.fillStyle = '#ffff00';
                    ctx.fill();
                });
            }
        }

        // Function to calculate the centroid of a polygon
        function getCentroid(polygon) {
            let xSum = 0;
            let ySum = 0;
            for (let i = 0; i < polygon.length; i++) {
                xSum += polygon[i][0];
                ySum += polygon[i][1];
            }
            return [xSum / polygon.length, ySum / polygon.length];
        }

    function sendCoordinates() {
        if (!selectedCameraName) {
            showNotification("Please select a camera first", "warning");
            return;
        }

        if (polygons.length === 0) {
            showNotification("Please draw at least one ROI region", "warning");
            return;
        }

        fetch("/crowd_detection/send-coordinates", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
                cameraName: selectedCameraName,
                coordinates: polygons,
            }),
        })
            .then((response) => response.json())
            .then((data) => {
                console.log("Response:", data);
                if (data.status === "success") {
                    showNotification(`ROI saved successfully for ${selectedCameraName}`, "success");
                    // Update the available cameras data
                    availableCameras[selectedCameraName][1] = polygons;
                } else {
                    showNotification("Error saving ROI", "error");
                }
            })
            .catch((error) => {
                console.error("Error sending coordinates:", error);
                showNotification("Network error while saving ROI", "error");
            });
    }

    </script>

</body>
</html>