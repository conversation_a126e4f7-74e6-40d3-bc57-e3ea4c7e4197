<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add ROI - VigilantEye</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary: #2563eb;
            --primary-dark: #1d4ed8;
            --primary-light: #3b82f6;
            --secondary: #1e3a8a;
            --secondary-dark: #1e40af;
            --success: #059669;
            --danger: #dc2626;
            --warning: #d97706;
            --info: #0ea5e9;
            --light: #f8fafc;
            --dark: #1e293b;
            --gray: #64748b;
            --gray-light: #94a3b8;
            --card-bg: #ffffff;
            --text-muted: #6b7280;
            --border: #e2e8f0;
            --sidebar-bg: #1e293b;
            --sidebar-text: #cbd5e1;
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f0f2f5;
            color: var(--dark);
            height: 100vh;
            overflow: hidden;
        }

        .roi-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        /* Header */
        .roi-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem;
            background: white;
            border-bottom: 1px solid var(--border);
            box-shadow: var(--shadow);
            z-index: 10;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .logo i {
            font-size: 1.5rem;
            color: var(--primary);
        }

        .logo span {
            font-size: 1.125rem;
            font-weight: 700;
            letter-spacing: -0.025em;
            color: var(--dark);
        }

        .header-title {
            border-left: 1px solid var(--border);
            padding-left: 1rem;
            margin-left: 1rem;
        }

        .roi-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--dark);
            margin: 0;
        }

        .page-subtitle {
            color: var(--gray);
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-indicator.connected {
            background: rgba(46, 125, 50, 0.1);
            color: var(--success);
            border: 1px solid rgba(46, 125, 50, 0.2);
        }

        .status-indicator.disconnected {
            background: rgba(211, 47, 47, 0.1);
            color: var(--danger);
            border: 1px solid rgba(211, 47, 47, 0.2);
        }

        .back-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: var(--gray);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .back-btn:hover {
            background: #616161;
            transform: translateY(-1px);
        }

        /* Main Content Area */
        .main-content {
            display: flex;
            flex: 1;
            gap: 1rem;
            padding: 1rem;
            overflow: hidden;
        }

        /* Camera Controls Panel */
        .controls-panel {
            width: 320px;
            background: white;
            border-radius: 12px;
            box-shadow: var(--shadow);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            flex-shrink: 0;
        }

        .panel-header {
            padding: 1rem;
            border-bottom: 1px solid var(--border);
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
        }

        .panel-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .panel-subtitle {
            font-size: 0.8rem;
            opacity: 0.9;
        }

        .panel-content {
            padding: 1rem;
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        /* Form Styles */
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .form-label {
            font-weight: 600;
            color: var(--dark);
            font-size: 0.875rem;
        }

        .form-select {
            padding: 0.75rem;
            border: 1px solid var(--border);
            border-radius: 8px;
            background: white;
            color: var(--dark);
            font-size: 0.875rem;
            transition: all 0.2s ease;
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23757575' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.75rem center;
            background-repeat: no-repeat;
            background-size: 1em 1em;
            padding-right: 2.5rem;
        }

        .form-select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(30, 136, 229, 0.1);
        }

        /* Button Styles */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            white-space: nowrap;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: var(--shadow-hover);
        }

        .btn-success {
            background: var(--success);
            color: white;
        }

        .btn-success:hover {
            background: #1b5e20;
            transform: translateY(-1px);
        }

        .btn-danger {
            background: var(--danger);
            color: white;
        }

        .btn-danger:hover {
            background: #c62828;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: var(--gray);
            color: white;
        }

        .btn-secondary:hover {
            background: #616161;
            transform: translateY(-1px);
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-info:hover {
            background: #138496;
            transform: translateY(-1px);
        }

        .btn-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        /* Video Container */
        .video-container {
            flex: 1;
            background: white;
            border-radius: 12px;
            box-shadow: var(--shadow);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .video-header {
            padding: 1rem;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .camera-info {
            font-size: 1rem;
            font-weight: 600;
            color: var(--dark);
        }

        .camera-status {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .camera-status-text {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.8rem;
            color: var(--gray);
        }

        .video-wrapper {
            position: relative;
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #000;
            min-height: 400px;
        }

        #video {
            max-width: 100%;
            max-height: 100%;
            display: block;
        }

        #canvas {
            position: absolute;
            top: 0;
            left: 0;
            cursor: crosshair;
        }

        .instruction-div {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            color: var(--text-muted);
            text-align: center;
        }

        .instruction-div i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .instruction-div h3 {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--gray);
            margin-bottom: 0.5rem;
        }

        .instruction-div p {
            font-size: 0.875rem;
            max-width: 300px;
        }

        /* Quick Guide Button */
        .guide-btn {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--primary);
            color: white;
            border: none;
            cursor: pointer;
            box-shadow: var(--shadow-hover);
            transition: all 0.3s ease;
            font-size: 1.25rem;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .guide-btn:hover {
            background: var(--primary-dark);
            transform: scale(1.1);
        }

        /* Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .modal {
            background: white;
            border-radius: 12px;
            box-shadow: var(--shadow-hover);
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            transform: scale(0.9);
            transition: transform 0.3s ease;
        }

        .modal-overlay.active .modal {
            transform: scale(1);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem;
            border-bottom: 1px solid var(--border);
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            border-radius: 12px 12px 0 0;
        }

        .modal-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.25rem;
            width: 2rem;
            height: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            transition: background 0.2s ease;
        }

        .modal-close:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .modal-content {
            padding: 1.5rem;
        }

        .guide-list {
            list-style: none;
            padding: 0;
            margin: 1rem 0;
        }

        .guide-list li {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            margin-bottom: 1rem;
            padding: 1rem;
            background: rgba(30, 136, 229, 0.05);
            border-radius: 8px;
            border-left: 4px solid var(--primary);
        }

        .guide-list .step-number {
            background: var(--primary);
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: bold;
            flex-shrink: 0;
            margin-top: 0.1rem;
        }

        .guide-list .step-text {
            color: var(--dark);
            line-height: 1.5;
        }

        /* Animations */
        .fade-out {
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .fade-in {
            opacity: 1;
            transition: opacity 0.3s ease;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .controls-panel {
                width: 280px;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
                gap: 0.5rem;
                padding: 0.5rem;
            }

            .controls-panel {
                width: 100%;
                max-height: 200px;
            }

            .roi-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
                padding: 1rem;
            }

            .header-left {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .header-title {
                border-left: none;
                padding-left: 0;
                margin-left: 0;
            }

            .guide-btn {
                bottom: 1rem;
                right: 1rem;
                width: 50px;
                height: 50px;
                font-size: 1rem;
            }
        }
    </style>
</head>

<body>
    <div class="roi-container">
        <!-- Header -->
        <div class="roi-header">
            <div class="header-left">
                <div class="logo">
                    <i class="fas fa-eye"></i>
                    <span>Vigilant Eye</span>
                </div>
                <div class="header-title">
                    <h1 class="roi-title">Focus Areas Configuration</h1>
                    <p class="page-subtitle">Define regions of interest for crowd detection monitoring</p>
                </div>
            </div>
            <div class="header-right">
                <div class="status-indicator disconnected" id="connectionStatus">
                    <i class="fas fa-circle"></i>
                    Disconnected
                </div>
                <a href="/crowd_detection/crowd_detection" class="back-btn" id="exitBtn">
                    <i class="fas fa-arrow-left"></i>
                    Back to Main
                </a>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Camera Controls Panel -->
            <div class="controls-panel">
                <div class="panel-header">
                    <h3 class="panel-title">Camera Controls</h3>
                    <p class="panel-subtitle">Select and configure your camera feed</p>
                </div>
                <div class="panel-content">
                    <div class="form-group">
                        <label class="form-label" for="cameraSelect">Select Camera</label>
                        <select id="cameraSelect" class="form-select">
                            <option value="">Choose a camera...</option>
                        </select>
                    </div>

                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="loadSelectedCamera()">
                            <i class="fas fa-play"></i>
                            Load Camera
                        </button>
                        <button class="btn btn-success" onclick="sendCoordinates()">
                            <i class="fas fa-save"></i>
                            Save Areas
                        </button>
                        <button class="btn btn-danger" onclick="clearPolygons()">
                            <i class="fas fa-trash-alt"></i>
                            Clear All
                        </button>
                        <button class="btn btn-secondary" onclick="stopCamera()">
                            <i class="fas fa-stop"></i>
                            Stop Camera
                        </button>
                    </div>
                </div>
            </div>

            <!-- Video Container -->
            <div class="video-container">
                <div class="video-header">
                    <div class="camera-info" id="camera-info">Camera Feed</div>
                    <div class="camera-status">
                        <div class="camera-status-text">
                            <i class="fas fa-video"></i>
                            <span>Ready to configure</span>
                        </div>
                    </div>
                </div>
                <div class="video-wrapper">
                    <img id="video" alt="Camera Feed" class="fade-out" width="640" height="480" />
                    <canvas id="canvas" width="640" class="fade-out" height="480"></canvas>

                    <div class="instruction-div">
                        <i class="fas fa-video"></i>
                        <h3>Select a camera to begin</h3>
                        <p>Choose a camera from the control panel and click "Load Camera" to start defining focus areas.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Guide Button -->
        <button class="guide-btn" onclick="showQuickGuide()" title="Quick Guide">
            <i class="fas fa-question"></i>
        </button>

        <!-- Quick Guide Modal -->
        <div class="modal-overlay" id="guideModal">
            <div class="modal">
                <div class="modal-header">
                    <h2 class="modal-title">
                        <i class="fas fa-info-circle"></i>
                        Quick Guide
                    </h2>
                    <button class="modal-close" onclick="hideQuickGuide()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-content">
                    <p>Follow these steps to configure focus areas for crowd detection:</p>
                    <ul class="guide-list">
                        <li>
                            <div class="step-number">1</div>
                            <div class="step-text">
                                <strong>Select Camera:</strong> Choose a camera from the dropdown menu in the control panel.
                            </div>
                        </li>
                        <li>
                            <div class="step-number">2</div>
                            <div class="step-text">
                                <strong>Load Camera:</strong> Click "Load Camera" to start the video feed and display the camera view.
                            </div>
                        </li>
                        <li>
                            <div class="step-number">3</div>
                            <div class="step-text">
                                <strong>Draw Areas:</strong> Click points on the video to create polygon boundaries for your focus areas.
                            </div>
                        </li>
                        <li>
                            <div class="step-number">4</div>
                            <div class="step-text">
                                <strong>Complete Area:</strong> Double-click to complete a focus area. You can create multiple areas if needed.
                            </div>
                        </li>
                        <li>
                            <div class="step-number">5</div>
                            <div class="step-text">
                                <strong>Save Configuration:</strong> Click "Save Areas" to store your focus area configuration.
                            </div>
                        </li>
                        <li>
                            <div class="step-number">6</div>
                            <div class="step-text">
                                <strong>Management:</strong> Use "Clear All" to remove all areas, or "Stop Camera" to end the session.
                            </div>
                        </li>
                    </ul>
                    <div style="background: rgba(30, 136, 229, 0.1); padding: 1rem; border-radius: 8px; border-left: 4px solid var(--primary); margin-top: 1rem;">
                        <strong>💡 Tips:</strong>
                        <ul style="margin: 0.5rem 0 0 1rem; color: var(--gray);">
                            <li>Focus areas will be highlighted in green when active</li>
                            <li>Each area is automatically numbered for easy identification</li>
                            <li>Existing areas will be loaded when you select a camera</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let videoElement = document.getElementById("video");
        let canvas = document.getElementById("canvas");
        let instructionDiv = document.querySelector(".instruction-div");
        let ctx = canvas.getContext("2d");
        let cameraInfo = document.getElementById("camera-info");
        let cameraSelect = document.getElementById("cameraSelect");
        let connectionStatus = document.getElementById("connectionStatus");
        let cam_name = "";
        let selectedCameraName = "";
        let availableCameras = {};

        let isDrawing = false;
        let polygons = [];
        let currentPolygon = [];
        let ws = null;

        // Event listeners
        const exitBtn = document.getElementById("exitBtn");
        exitBtn.addEventListener("click", () => {
            window.location.href = "/crowd_detection/crowd_detection";
        });

        // Load available cameras on page load
        document.addEventListener("DOMContentLoaded", async function() {
            await loadAvailableCameras();
        });

        // Quick Guide Modal Functions
        function showQuickGuide() {
            const modal = document.getElementById('guideModal');
            modal.classList.add('active');
        }

        function hideQuickGuide() {
            const modal = document.getElementById('guideModal');
            modal.classList.remove('active');
        }

        // Close modal when clicking outside
        document.getElementById('guideModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideQuickGuide();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                hideQuickGuide();
            }
        });

        // Function to load available cameras
        async function loadAvailableCameras() {
            try {
                const response = await fetch("/crowd_detection/get-cameras");
                const cameras = await response.json();
                availableCameras = cameras;

                // Populate camera dropdown
                cameraSelect.innerHTML = '<option value="">Choose a camera...</option>';
                for (const [cameraName, cameraData] of Object.entries(cameras)) {
                    const option = document.createElement('option');
                    option.value = cameraName;
                    option.textContent = cameraName;
                    cameraSelect.appendChild(option);
                }
            } catch (error) {
                console.error("Failed to load cameras:", error);
                showNotification("Failed to load cameras", "error");
            }
        }

        // Function to load selected camera
        function loadSelectedCamera() {
            selectedCameraName = cameraSelect.value;
            if (!selectedCameraName) {
                showNotification("Please select a camera first", "warning");
                return;
            }

            cam_name = selectedCameraName;
            cameraInfo.textContent = `${selectedCameraName}`;

            // Clear existing polygons when switching cameras
            clearPolygons();

            // Load existing ROI for this camera
            loadExistingROI();

            // Start camera feed
            startCameraFeed();
        }

        // Function to load existing ROI for the selected camera
        function loadExistingROI() {
            if (availableCameras[selectedCameraName] && availableCameras[selectedCameraName][1]) {
                const existingROI = availableCameras[selectedCameraName][1];
                if (existingROI.length > 0) {
                    polygons = existingROI;
                    drawPolygons();
                    showNotification(`Loaded ${existingROI.length} existing focus areas`, "success");
                }
            }
        }

        // Function to start camera feed
        function startCameraFeed() {
            // First, start the camera handler with the selected camera
            fetch("/crowd_detection/camera/start")
                .then(response => response.json())
                .then(() => {
                    // Set the camera to the selected one
                    return setSpecificCamera(selectedCameraName);
                })
                .then(() => {
                    // Start WebSocket connection
                    ws = new WebSocket("ws://localhost:8000/crowd_detection/addroi");
                    ws.binaryType = "arraybuffer";

                    ws.onopen = () => {
                        updateConnectionStatus(true);
                        showVideoElements();
                    };

                    ws.onmessage = (event) => {
                        const data = new Uint8Array(event.data);
                        const separatorIndex = data.indexOf(10);

                        if (separatorIndex === -1) {
                            console.error("Invalid data format received.");
                            return;
                        }

                        const metadataJson = new TextDecoder().decode(data.slice(0, separatorIndex));
                        const frameData = data.slice(separatorIndex + 1);

                        try {
                            const metadata = JSON.parse(metadataJson);
                            const blob = new Blob([frameData], { type: "image/jpeg" });
                            const url = URL.createObjectURL(blob);
                            videoElement.src = url;
                        } catch (error) {
                            console.error("Error parsing WebSocket message:", error);
                        }
                    };

                    ws.onclose = () => {
                        updateConnectionStatus(false);
                    };

                    ws.onerror = (error) => {
                        console.error("WebSocket error:", error);
                        updateConnectionStatus(false);
                    };
                })
                .catch(error => {
                    console.error("Error starting camera:", error);
                    showNotification("Failed to start camera", "error");
                });
        }

        // Function to set specific camera
        async function setSpecificCamera(cameraName) {
            try {
                const response = await fetch(`/crowd_detection/camera/select/${cameraName}`, {
                    method: "POST"
                });
                const result = await response.json();

                if (result.status !== "success") {
                    throw new Error(result.message || "Failed to select camera");
                }

                return result;
            } catch (error) {
                console.error("Error selecting camera:", error);
                throw error;
            }
        }

        // Function to update connection status
        function updateConnectionStatus(connected) {
            if (connected) {
                connectionStatus.className = "status-indicator connected";
                connectionStatus.innerHTML = '<i class="fas fa-circle"></i> Connected';
            } else {
                connectionStatus.className = "status-indicator disconnected";
                connectionStatus.innerHTML = '<i class="fas fa-circle"></i> Disconnected';
            }
        }

        // Function to show video elements
        function showVideoElements() {
            videoElement.classList.remove("fade-out");
            videoElement.classList.add("fade-in");
            canvas.classList.remove("fade-out");
            canvas.classList.add("fade-in");
            instructionDiv.classList.remove("fade-in");
            instructionDiv.classList.add("fade-out");
        }

        // Function to hide video elements
        function hideVideoElements() {
            videoElement.classList.remove("fade-in");
            videoElement.classList.add("fade-out");
            canvas.classList.remove("fade-in");
            canvas.classList.add("fade-out");
            instructionDiv.classList.remove("fade-out");
            instructionDiv.classList.add("fade-in");
        }

        // Function to stop camera
        function stopCamera() {
            if (ws) {
                ws.close();
                ws = null;
            }

            fetch("/crowd_detection/camera/stop")
                .then(response => response.json())
                .then(() => {
                    videoElement.src = "";
                    hideVideoElements();
                    updateConnectionStatus(false);
                    showNotification("Camera stopped", "info");
                })
                .catch(error => {
                    console.error("Error stopping camera:", error);
                    showNotification("Error stopping camera", "error");
                });
        }

        // Function to show notifications
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 16px 20px;
                border-radius: 8px;
                color: white;
                font-size: 14px;
                font-weight: 600;
                z-index: 1100;
                transform: translateX(120%);
                transition: transform 0.3s ease;
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
                max-width: 400px;
            `;

            const colors = {
                'success': '#10b981',
                'error': '#ef4444',
                'warning': '#f59e0b',
                'info': '#3b82f6'
            };

            notification.style.backgroundColor = colors[type] || colors.info;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => notification.style.transform = 'translateX(0)', 10);
            setTimeout(() => {
                notification.style.transform = 'translateX(120%)';
                setTimeout(() => notification.remove(), 300);
            }, 4000);
        }

        function startCamera() {
            setTimeout(() => {
                startStopToggle('start');
            }, 2000);
            console.log("Starting camera...");
            fetch("/crowd_detection/camera/start")
                .then((response) => response.json())
                .then(() => {
                    ws = new WebSocket("ws://localhost:8000/crowd_detection/addroi");
                    ws.binaryType = "arraybuffer"; // Expect binary data

                    ws.onmessage = (event) => {
                        const data = new Uint8Array(event.data);
                        const separatorIndex = data.indexOf(10); // Find newline separator
                        console.log(event);
                        if (separatorIndex === -1) {
                            console.error("Invalid data format received.");
                            return;
                        }

                        const metadataJson = new TextDecoder().decode(data.slice(0, separatorIndex));
                        const frameData = data.slice(separatorIndex + 1);

                        try {
                            const metadata = JSON.parse(metadataJson);
                            cam_name = metadata.camera_name;
                            document.getElementById("camera-info").innerHTML = `<h3>${cam_name} SELECTED</h3>`;

                            const blob = new Blob([frameData], { type: "image/jpeg" });
                            const url = URL.createObjectURL(blob);
                            videoElement.src = url; // Update video feed
                        } catch (error) {
                            console.error("Error parsing WebSocket message:", error);
                        }
                    };

                    ws.onclose = () => {
                        console.error("WebSocket connection closed");
                        // alert("WebSocket connection closed");
                    };
                })
                .catch((error) => console.error("Error starting camera:", error));
        }

        function stopCamera() {
            startStopToggle('stop');
            fetch("/crowd_detection/camera/stop")
                .then((response) => response.json())
                .then(() => {
                    if (ws) {
                        ws.close();
                        ws = null;
                    }
                    videoElement.src = ""; // Clear video feed
                })
                .catch((error) => console.error("Error stopping camera:", error));
        }

        // Function to clear all polygons and reset the canvas
        function clearPolygons() {
            polygons = [];
            currentPolygon = [];
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            showNotification("All focus areas cleared", "info");
        }

        // Draw polygon on canvas
        canvas.addEventListener("click", (event) => {
            if (!selectedCameraName) {
                showNotification("Please select and load a camera first", "warning");
                return;
            }

            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;
            currentPolygon.push({x: x, y: y});
            drawPolygons();
        });

        // Double-click to close the current polygon
        canvas.addEventListener("dblclick", (event) => {
            event.preventDefault();
            if (currentPolygon.length > 2) {
                // Convert to the format expected by the backend
                const polygonCoords = currentPolygon.map(point => [point.x, point.y]);
                polygons.push(polygonCoords);
                currentPolygon = [];
                drawPolygons();
                showNotification(`Focus area ${polygons.length} created`, "success");
            }
        });

        // Helper: Draw the polygons on the canvas
        function drawPolygons() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Draw completed polygons
            polygons.forEach((polygon, index) => {
                if (polygon.length > 2) {
                    ctx.beginPath();
                    ctx.moveTo(polygon[0][0], polygon[0][1]);

                    for (let i = 1; i < polygon.length; i++) {
                        ctx.lineTo(polygon[i][0], polygon[i][1]);
                    }

                    ctx.closePath();
                    ctx.strokeStyle = '#00ff00';
                    ctx.lineWidth = 3;
                    ctx.stroke();

                    ctx.fillStyle = 'rgba(0, 255, 0, 0.2)';
                    ctx.fill();

                    // Add region label
                    const centroid = getCentroid(polygon);
                    ctx.fillStyle = '#00ff00';
                    ctx.font = 'bold 16px Arial';
                    ctx.fillText(`ROI ${index + 1}`, centroid[0] - 20, centroid[1]);
                }
            });

            // Draw current polygon being drawn
            if (currentPolygon.length > 0) {
                ctx.beginPath();
                if (currentPolygon.length > 0) {
                    ctx.moveTo(currentPolygon[0].x, currentPolygon[0].y);

                    for (let i = 1; i < currentPolygon.length; i++) {
                        ctx.lineTo(currentPolygon[i].x, currentPolygon[i].y);
                    }
                }

                ctx.strokeStyle = '#ffff00';
                ctx.lineWidth = 2;
                ctx.stroke();

                // Draw points
                currentPolygon.forEach(point => {
                    ctx.beginPath();
                    ctx.arc(point.x, point.y, 4, 0, 2 * Math.PI);
                    ctx.fillStyle = '#ffff00';
                    ctx.fill();
                });
            }
        }

        // Function to calculate the centroid of a polygon
        function getCentroid(polygon) {
            let xSum = 0;
            let ySum = 0;
            for (let i = 0; i < polygon.length; i++) {
                xSum += polygon[i][0];
                ySum += polygon[i][1];
            }
            return [xSum / polygon.length, ySum / polygon.length];
        }

    function sendCoordinates() {
        if (!selectedCameraName) {
            showNotification("Please select a camera first", "warning");
            return;
        }

        if (polygons.length === 0) {
            showNotification("Please draw at least one ROI region", "warning");
            return;
        }

        fetch("/crowd_detection/send-coordinates", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
                cameraName: selectedCameraName,
                coordinates: polygons,
            }),
        })
            .then((response) => response.json())
            .then((data) => {
                console.log("Response:", data);
                if (data.status === "success") {
                    showNotification(`ROI saved successfully for ${selectedCameraName}`, "success");
                    // Update the available cameras data
                    availableCameras[selectedCameraName][1] = polygons;
                } else {
                    showNotification("Error saving ROI", "error");
                }
            })
            .catch((error) => {
                console.error("Error sending coordinates:", error);
                showNotification("Network error while saving ROI", "error");
            });
    }

    </script>

</body>
</html>