import requests
import logging
import os
from typing import Optional, Dict
from functools import wraps
from fastapi import Request, HTTPException, status

logger = logging.getLogger(__name__)

class AuthClient:
    """Client for communicating with the external authentication service"""

    def __init__(self, auth_service_url: str = "http://localhost:8001"):
        self.auth_service_url = auth_service_url
        self.timeout = 5
        # Development mode - enabled by default for easier development
        self.dev_mode = os.getenv("AUTH_DEV_MODE", "true").lower() == "true"

    def validate_token(self, token: str) -> Optional[Dict]:
        """Validate a token with the authentication service"""
        try:
            # Remove Bearer prefix if present
            if token.startswith("Bearer "):
                token = token[7:]

            response = requests.post(
                f"{self.auth_service_url}/validate-token",
                json={"token": token},
                timeout=self.timeout
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("valid"):
                    return result.get("user")

            return None

        except requests.RequestException as e:
            logger.error(f"Error validating token with auth service: {e}")
            return None

    def get_user_from_request(self, request: Request) -> Optional[Dict]:
        """Extract user information from request using authentication service"""
        # Development mode - return fake user
        if self.dev_mode:
            return {
                "id": 1,
                "username": "dev_user",
                "email": "<EMAIL>",
                "full_name": "Development User",
                "disabled": False
            }

        # Try Authorization header first
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token = auth_header[7:]
            user = self.validate_token(token)
            if user:
                return user

        # Try cookies
        token_cookie = request.cookies.get("access_token")
        if token_cookie:
            if token_cookie.startswith("Bearer "):
                token = token_cookie[7:]
            else:
                token = token_cookie
            user = self.validate_token(token)
            if user:
                return user

        return None

    def require_auth(self, request: Request) -> Dict:
        """Require authentication and return user info or raise exception"""
        user = self.get_user_from_request(request)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required. Please login.",
                headers={"WWW-Authenticate": "Bearer"},
            )
        return user

    def health_check(self) -> bool:
        """Check if authentication service is healthy"""
        try:
            response = requests.get(
                f"{self.auth_service_url}/health",
                timeout=self.timeout
            )
            return response.status_code == 200
        except requests.RequestException:
            return False

# Global auth client instance
auth_client = AuthClient()

# FastAPI Dependencies
async def get_current_user(request: Request) -> Dict:
    """FastAPI dependency to get current user"""
    return auth_client.require_auth(request)

async def get_current_user_optional(request: Request) -> Optional[Dict]:
    """FastAPI dependency to get current user (optional)"""
    return auth_client.get_user_from_request(request)

# Decorator for protecting routes
def require_auth(func):
    """Decorator to require authentication for a route"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        # Find the request object in the arguments
        request = None
        for arg in args:
            if isinstance(arg, Request):
                request = arg
                break

        if not request:
            # Look in kwargs
            request = kwargs.get('request')

        if not request:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Request object not found"
            )

        # Validate authentication
        user = auth_client.require_auth(request)

        # Add user to kwargs for the route handler
        kwargs['current_user'] = user

        return await func(*args, **kwargs)

    return wrapper
