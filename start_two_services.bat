@echo off
echo ========================================
echo    VIGILANT EYE - TWO SERVICE SYSTEM
echo ========================================
echo.
echo Starting Authentication and Backend as separate services...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python and try again.
    pause
    exit /b 1
)

echo 🔐 Starting Authentication Service...
start "Authentication Service" cmd /k "cd Authentication && python start.py"

echo ⚙️ Starting Backend Service...
start "Backend Service" cmd /k "cd Backend && venv\Scripts\activate && uvicorn app.main:app --reload"

echo.
echo ========================================
echo 🎉 VIGILANT EYE SYSTEM READY!
echo ========================================
echo 🔐 Authentication: http://localhost:8001
echo ⚙️ Backend Services: http://127.0.0.1:8000
echo 🏠 Main Access: http://localhost:8001 (Login first)
echo ========================================
echo 📋 User Flow:
echo    1. Go to http://localhost:8001
echo    2. Login (admin/admin123)
echo    3. Access Dashboard
echo    4. Navigate to AI Services
echo ========================================
echo.
echo Both services are running in separate windows.
echo Close this window or press any key to continue...
pause
