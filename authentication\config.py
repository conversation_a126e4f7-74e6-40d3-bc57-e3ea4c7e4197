import os
from typing import List

class Settings:
    # Authentication Service Configuration
    AUTH_SERVICE_HOST: str = os.getenv("AUTH_SERVICE_HOST", "localhost")
    AUTH_SERVICE_PORT: int = int(os.getenv("AUTH_SERVICE_PORT", "8001"))
    
    # Database Configuration
    DATABASE_URL: str = os.getenv("DATABASE_URL", "mysql+pymysql://root:1234@localhost/vigilanteye_auth")
    
    # JWT Configuration
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-change-this-in-production-vigilanteye-auth-2024")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
    
    # CORS Configuration
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:8000",  # Backend service
        "http://localhost:3000",  # Frontend if any
        "http://127.0.0.1:8000",
        "http://127.0.0.1:3000",
    ]
    
    # Service URLs for inter-service communication
    BACKEND_SERVICE_URL: str = os.getenv("BACKEND_SERVICE_URL", "http://localhost:8000")
    
    # Security Settings
    COOKIE_SECURE: bool = os.getenv("COOKIE_SECURE", "false").lower() == "true"
    COOKIE_SAMESITE: str = os.getenv("COOKIE_SAMESITE", "lax")
    
    class Config:
        case_sensitive = True

settings = Settings()
