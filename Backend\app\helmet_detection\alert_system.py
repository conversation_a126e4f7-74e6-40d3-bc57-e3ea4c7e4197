import json
import os
import smtplib
import threading
import time as timer
from datetime import datetime, time
from email.mime.multipart import MIMEMult<PERSON>art
from email.mime.text import MIMEText
from queue import Queue
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('PPE_Alert_System')

# PPE Alert system class
class PPEAlertSystem:
    def __init__(self):
        # logger.info("Initializing PPE Alert System")
        # First, initialize empty settings
        self.settings = {}
        # Then load settings from file
        self.settings = self.load_settings()
        # Now set active flag based on settings
        self.active = self.settings.get("active", False)
        # Initialize other attributes
        self.alert_thread = None
        self.stop_event = threading.Event()
        self.last_alert_time = {} # Dictionary to track last alert time for each camera and violation type
        
        # logger.info(f"PPE Alert System initialized. Active: {self.active}")
        
        # Email configuration
        self.smtp_server = "smtp.gmail.com"
        self.smtp_port = 587
        self.email_username = "<EMAIL>"
        self.email_password = "rnqp rnqp rnqp rnqp"  # App password
        
        # Email queue for background sending
        self.email_queue = Queue()
        
        # Start email worker thread
        self.email_worker_thread = threading.Thread(target=self._email_worker, daemon=True)
        self.email_worker_thread.start()
        
        # Alert log file
        self.alert_log_file = "app/helmet_detection/ppe_alert_log.json"
        
    def load_settings(self):
        """Load alert settings from JSON file"""
        settings_file = "app/helmet_detection/ppe_alert_settings.json"
        default_settings = {
            "active": False,
            "days": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
            "startTime": "09:00",
            "endTime": "17:00",
            "alertFrequency": 5,  # minutes
            "recipients": []
        }
        
        try:
            if os.path.exists(settings_file):
                with open(settings_file, 'r') as f:
                    settings = json.load(f)
                    # Merge with defaults to ensure all keys exist
                    for key, value in default_settings.items():
                        if key not in settings:
                            settings[key] = value
                    return settings
            else:
                # Create default settings file
                with open(settings_file, 'w') as f:
                    json.dump(default_settings, f, indent=4)
                return default_settings
        except Exception as e:
            logger.error(f"Error loading settings: {e}")
            return default_settings
    
    def save_settings(self, settings):
        """Save alert settings to JSON file"""
        settings_file = "app/helmet_detection/ppe_alert_settings.json"
        try:
            with open(settings_file, 'w') as f:
                json.dump(settings, f, indent=4)
            self.settings = settings
            self.active = settings.get("active", False)
            logger.info("PPE Alert settings saved successfully")
            return True
        except Exception as e:
            logger.error(f"Error saving settings: {e}")
            return False
    
    def start(self):
        """Start the alert system"""
        try:
            if not self.active:
                self.active = True
                self.settings["active"] = True
                self.save_settings(self.settings)
                logger.info("PPE Alert system started")
                return True
            else:
                logger.info("PPE Alert system already active")
                return True
        except Exception as e:
            logger.error(f"Error starting alert system: {e}")
            return False
    
    def stop(self):
        """Stop the alert system"""
        try:
            if self.active:
                self.active = False
                self.settings["active"] = False
                self.save_settings(self.settings)
                logger.info("PPE Alert system stopped")
                return True
            else:
                logger.info("PPE Alert system already inactive")
                return True
        except Exception as e:
            logger.error(f"Error stopping alert system: {e}")
            return False
    
    def _should_check_alerts(self):
        """Check if alerts should be checked based on schedule"""
        if not self.active:
            return False
            
        current_time = datetime.now()
        current_day = current_time.strftime("%A")
        current_hour_minute = current_time.time()
        
        # Check if today is in the allowed days
        if current_day not in self.settings.get("days", []):
            return False
        
        # Check if current time is within the allowed time range
        start_time = time.fromisoformat(self.settings.get("startTime", "09:00"))
        end_time = time.fromisoformat(self.settings.get("endTime", "17:00"))
        
        if not (start_time <= current_hour_minute <= end_time):
            return False
            
        return True
    
    def check_and_alert(self, camera_name, violation_type, count):
        """Check if alert should be sent and send it if needed"""
        # logger.debug(f"Checking PPE alert for {camera_name}, {violation_type}, count {count}")
        
        # Don't alert if count is zero or negative
        if count <= 0:
            # logger.debug("Count is zero or negative, no alert needed")
            return False
            
        # Only continue if alert conditions are met
        if not self._should_check_alerts():
            # logger.debug("Alert conditions not met")
            return False
            
        # Check if we've already sent an alert recently for this camera/violation
        camera_violation_key = f"{camera_name}_{violation_type}"
        current_time = timer.time()
        
        if camera_violation_key in self.last_alert_time:
            # Check if enough time has passed since the last alert
            elapsed_minutes = (current_time - self.last_alert_time[camera_violation_key]) / 60
            if elapsed_minutes < self.settings["alertFrequency"]:
                # logger.debug(f"Too soon for another alert. Elapsed: {elapsed_minutes:.2f} min, Required: {self.settings['alertFrequency']} min")
                return False

        # Queue the alert for sending in background thread
        # logger.info(f"Queueing PPE alert for {camera_name}, {violation_type}, count {count}")
        self.email_queue.put((camera_name, violation_type, count))

        # Update last alert time
        self.last_alert_time[camera_violation_key] = current_time
        # Log alert
        self._log_alert(camera_name, violation_type, count)
        # logger.info("PPE Alert queued successfully")
        return True
    
    def _log_alert(self, camera_name, violation_type, count):
        """Log alert to file"""
        try:
            alert_entry = {
                "timestamp": datetime.now().isoformat(),
                "camera": camera_name,
                "violation_type": violation_type,
                "count": count
            }
            
            # Load existing log or create new
            log_data = []
            if os.path.exists(self.alert_log_file):
                try:
                    with open(self.alert_log_file, 'r') as f:
                        log_data = json.load(f)
                except:
                    log_data = []
            
            # Add new entry
            log_data.append(alert_entry)
            
            # Keep only last 1000 entries
            if len(log_data) > 1000:
                log_data = log_data[-1000:]
            
            # Save log
            with open(self.alert_log_file, 'w') as f:
                json.dump(log_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error logging alert: {e}")
    
    def _email_worker(self):
        """Background worker to send emails"""
        while True:
            try:
                # Get alert from queue (blocks until available)
                camera_name, violation_type, count = self.email_queue.get(timeout=1)
                self._send_email_alert(camera_name, violation_type, count)
                self.email_queue.task_done()
            except:
                # Timeout or other error, continue loop
                continue
    
    def _send_email_alert(self, camera_name, violation_type, count):
        """Send email alert"""
        try:
            recipients = self.settings.get("recipients", [])
            if not recipients:
                logger.warning("No email recipients configured")
                return
            
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.email_username
            msg['To'] = ", ".join(recipients)
            msg['Subject'] = f"PPE Safety Alert - {violation_type} Detected"
            
            # Create email body
            body = f"""
            PPE Safety Alert
            
            Camera: {camera_name}
            Violation Type: {violation_type}
            Count: {count} person(s)
            Time: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
            
            Please take immediate action to ensure workplace safety.
            
            This is an automated alert from Vigilant Eye PPE Detection System.
            """
            
            msg.attach(MIMEText(body, 'plain'))
            
            # Send email
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.email_username, self.email_password)
            text = msg.as_string()
            server.sendmail(self.email_username, recipients, text)
            server.quit()
            
            logger.info(f"PPE Alert email sent successfully for {camera_name} - {violation_type}")
            
        except Exception as e:
            logger.error(f"Error sending PPE alert email: {e}")
