# import json
# import os
# import smtplib
# import threading
# import time as timer
# from datetime import datetime, time
# from email.mime.multipart import MIMEMultipart
# from email.mime.text import MIMEText
# import queue
# import logging

# # Set up logging
# logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
# logger = logging.getLogger('PPE_Alert_System')

# # PPE Alert system class
# class PPEAlertSystem:
#     def __init__(self):
#         # logger.info("Initializing PPE Alert System")
#         # First, initialize empty settings
#         self.settings = {}
#         # Then load settings from file
#         self.settings = self.load_settings()
#         # Now set active flag based on settings
#         self.active = self.settings.get("active", False)
#         # Initialize other attributes
#         self.alert_thread = None
#         self.stop_event = threading.Event()
#         self.last_alert_time = {} # Dictionary to track last alert time for each camera and violation type

#         # logger.info(f"PPE Alert System initialized. Active: {self.active}")

#         # Email configuration
#         self.smtp_server = "smtp.gmail.com"
#         self.smtp_port = 587
#         self.email_username = "<EMAIL>"
#         self.email_password = "rnqp rnqp rnqp rnqp"  # App password

#         # Email queue for background sending
#         self.email_queue = queue.Queue()

#         # Start email worker thread
#         self.email_worker_thread = threading.Thread(target=self._email_worker, daemon=True)
#         self.email_worker_thread.start()

#         # Alert log file
#         self.alert_log_file = "app/helmet_detection/ppe_alert_log.json"

#     def load_settings(self):
#         """Load alert settings from JSON file"""
#         settings_file = "app/helmet_detection/ppe_alert_settings.json"
#         default_settings = {
#             "active": False,
#             "days": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
#             "startTime": "09:00",
#             "endTime": "17:00",
#             "alertFrequency": 5,  # minutes
#             "recipients": []
#         }

#         try:
#             if os.path.exists(settings_file):
#                 with open(settings_file, 'r') as f:
#                     settings = json.load(f)
#                     # Merge with defaults to ensure all keys exist
#                     for key, value in default_settings.items():
#                         if key not in settings:
#                             settings[key] = value
#                     return settings
#             else:
#                 # Create default settings file
#                 with open(settings_file, 'w') as f:
#                     json.dump(default_settings, f, indent=4)
#                 return default_settings
#         except Exception as e:
#             logger.error(f"Error loading settings: {e}")
#             return default_settings

#     def save_settings(self, settings):
#         """Save alert settings to JSON file"""
#         settings_file = "app/helmet_detection/ppe_alert_settings.json"
#         # logger.info(f"Saving PPE alert settings: {settings}")
#         os.makedirs(os.path.dirname(settings_file), exist_ok=True)
#         with open(settings_file, 'w') as f:
#             json.dump(settings, f, indent=4)
#         self.settings = settings
#         # Update active state based on new settings
#         self.active = settings.get("active", False)
#         # logger.info(f"PPE Alert settings saved. Active state: {self.active}")

#     def start(self):
#         """Start the alert system"""
#         # logger.info("Starting PPE alert system")
#         if not self.active:
#             self.active = True
#             self.stop_event.clear()
#             self.settings["active"] = True
#             self.save_settings(self.settings)
#             # logger.info("PPE Alert system started")
#             return True
#         # logger.info("PPE Alert system already active")
#         return False

#     def stop(self):
#         """Stop the alert system"""
#         # logger.info("Stopping PPE alert system")
#         if self.active:
#             self.active = False
#             self.settings["active"] = False
#             self.save_settings(self.settings)
#             self.stop_event.set()
#             # logger.info("PPE Alert system stopped")
#             return True
#         # logger.info("PPE Alert system already inactive")
#         return False

#     def _should_check_alerts(self):
#         """Check if alerts should be checked based on schedule"""
#         if not self.active:
#             return False

#         current_time = datetime.now()
#         current_day = current_time.strftime("%A")
#         current_hour_minute = current_time.time()

#         # Check if today is in the allowed days
#         if current_day not in self.settings.get("days", []):
#             return False

#         # Check if current time is within the allowed time range
#         start_time = time.fromisoformat(self.settings.get("startTime", "09:00"))
#         end_time = time.fromisoformat(self.settings.get("endTime", "17:00"))

#         if not (start_time <= current_hour_minute <= end_time):
#             return False

#         return True

#     def check_and_alert(self, camera_name, violation_type, count):
#         """Check if alert should be sent and send it if needed"""
#         # logger.debug(f"Checking PPE alert for {camera_name}, {violation_type}, count {count}")

#         # Don't alert if count is zero or negative
#         if count <= 0:
#             # logger.debug("Count is zero or negative, no alert needed")
#             return False

#         # Only continue if alert conditions are met
#         if not self._should_check_alerts():
#             # logger.debug("Alert conditions not met")
#             return False

#         # Check if we've already sent an alert recently for this camera/violation
#         camera_violation_key = f"{camera_name}_{violation_type}"
#         current_time = timer.time()

#         if camera_violation_key in self.last_alert_time:
#             # Check if enough time has passed since the last alert
#             elapsed_minutes = (current_time - self.last_alert_time[camera_violation_key]) / 60
#             if elapsed_minutes < self.settings["alertFrequency"]:
#                 # logger.debug(f"Too soon for another alert. Elapsed: {elapsed_minutes:.2f} min, Required: {self.settings['alertFrequency']} min")
#                 return False

#         # Queue the alert for sending in background thread
#         # logger.info(f"Queueing PPE alert for {camera_name}, {violation_type}, count {count}")
#         self.email_queue.put((camera_name, violation_type, count))

#         # Update last alert time
#         self.last_alert_time[camera_violation_key] = current_time
#         # Log alert
#         self._log_alert(camera_name, violation_type, count)
#         # logger.info("PPE Alert queued successfully")
#         return True

#     def _log_alert(self, camera_name, violation_type, count):
#         """Log alert to file"""
#         try:
#             alert_entry = {
#                 "timestamp": datetime.now().isoformat(),
#                 "camera": camera_name,
#                 "violation_type": violation_type,
#                 "count": count
#             }

#             # Load existing log or create new
#             log_data = []
#             if os.path.exists(self.alert_log_file):
#                 try:
#                     with open(self.alert_log_file, 'r') as f:
#                         log_data = json.load(f)
#                 except:
#                     log_data = []

#             # Add new entry
#             log_data.append(alert_entry)

#             # Keep only last 1000 entries
#             if len(log_data) > 1000:
#                 log_data = log_data[-1000:]

#             # Save log
#             with open(self.alert_log_file, 'w') as f:
#                 json.dump(log_data, f, indent=2)

#         except Exception as e:
#             logger.error(f"Error logging alert: {e}")

#     def _email_worker(self):
#         """Background worker to send emails"""
#         # logger.info("PPE Email worker thread started")
#         while True:
#             try:
#                 # Get item from queue, with a timeout
#                 try:
#                     camera_name, violation_type, count = self.email_queue.get(timeout=1)
#                 except queue.Empty:
#                     # Check if we should exit
#                     if self.stop_event.is_set():
#                         # logger.info("PPE Email worker thread stopping")
#                         break
#                     continue

#                 # Send the email
#                 success = self._send_email_alert(camera_name, violation_type, count)
#                 # if success:
#                     # logger.info(f"PPE Alert sent successfully for {camera_name}, {violation_type}")
#                 # else:
#                     # logger.warning(f"Failed to send PPE alert for {camera_name}, {violation_type}")

#                 # Mark task as done
#                 self.email_queue.task_done()

#             except Exception as e:
#                 # logger.error(f"Error in PPE email worker thread: {e}")
#                 # Sleep briefly to avoid spinning if there's a persistent error
#                 timer.sleep(1)

#     def _send_email_alert(self, camera_name, violation_type, count):
#         """Send email alert"""
#         try:
#             recipients = self.settings.get("recipients", [])
#             if not recipients:
#                 logger.warning("No email recipients configured")
#                 return

#             # Create message
#             msg = MIMEMultipart()
#             msg['From'] = self.email_username
#             msg['To'] = ", ".join(recipients)
#             msg['Subject'] = f"PPE Safety Alert - {violation_type} Detected"

#             # Create email body
#             body = f"""
#             PPE Safety Alert

#             Camera: {camera_name}
#             Violation Type: {violation_type}
#             Count: {count} person(s)
#             Time: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

#             Please take immediate action to ensure workplace safety.

#             This is an automated alert from Vigilant Eye PPE Detection System.
#             """

#             msg.attach(MIMEText(body, 'plain'))

#             # Send email
#             server = smtplib.SMTP(self.smtp_server, self.smtp_port)
#             server.starttls()
#             server.login(self.email_username, self.email_password)
#             text = msg.as_string()
#             server.sendmail(self.email_username, recipients, text)
#             server.quit()

#             logger.info(f"PPE Alert email sent successfully for {camera_name} - {violation_type}")

#         except Exception as e:
#             logger.error(f"Error sending PPE alert email: {e}")



import json
import os
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime, time
import threading
import time as timer
from pathlib import Path
import logging
import queue

# Set up logging for better debugging
# logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
# logger = logging.getLogger('AlertSystem')

# Path to the alert settings file
ALERT_SETTINGS_FILE = "app/helmet_detection/alert_settings.json"
ALERT_LOG_FILE = "app/helmet_detection/alert_log.json"

# Alert system class
class AlertSystem:
    def __init__(self):
        # logger.info("Initializing Alert System")
        # First, initialize empty settings
        self.settings = {}
        # Then load settings from file
        self.settings = self.load_settings()
        # Now set active flag based on settings
        self.active = self.settings.get("active", False)
        # Initialize other attributes
        self.alert_thread = None
        self.stop_event = threading.Event()
        self.last_alert_time = {} # Dictionary to track last alert time for each camera and region
        
        # logger.info(f"Alert System initialized. Active: {self.active}")
        
        # Create a queue for email sending
        self.email_queue = queue.Queue()
        # Start a worker thread for email sending
        self.email_worker = threading.Thread(target=self._email_worker, daemon=True)
        self.email_worker.start()
        
        # logger.info(f"Alert System initialized. Active: {self.active}")

        # Start monitoring if active in settings
        if self.active:
            self.start()

    def load_settings(self):
        """Load alert settings from file"""
        try:
            if Path(ALERT_SETTINGS_FILE).exists():
                with open(ALERT_SETTINGS_FILE, "r") as file:
                    content = file.read().strip()
                    if content:  # Check if file is not empty
                        settings = json.loads(content)
                        # logger.info(f"Loaded settings: {settings}")
                        return settings
            # Return default settings if file doesn't exist or is empty
            default_settings = {
                "active": False,
                "days": [],
                "startTime": "",
                "endTime": "",
                "alertFrequency": 5,  # Default 5 minutes
                "recipients": []
            }
            # logger.info(f"Using default settings: {default_settings}")
            return default_settings
        except json.JSONDecodeError as e:
            # logger.error(f"Error decoding JSON from settings file: {e}")
            # If there's an error, return default settings
            return {
                "active": False,
                "days": [],
                "startTime": "",
                "endTime": "",
                "alertFrequency": 5,
                "recipients": []
            }
    
    def save_settings(self, settings):
        """Save alert settings to file"""
        # logger.info(f"Saving settings: {settings}")
        os.makedirs(os.path.dirname(ALERT_SETTINGS_FILE), exist_ok=True)
        with open(ALERT_SETTINGS_FILE, "w") as file:
            json.dump(settings, file, indent=4)
        self.settings = settings
        # Update active state based on new settings
        self.active = settings.get("active", False)
        # logger.info(f"Settings saved. Active state: {self.active}")
    
    def start(self):
        """Start the alert system"""
        # logger.info("Starting alert system")
        if not self.active:
            self.active = True
            self.stop_event.clear()
            self.settings["active"] = True
            self.save_settings(self.settings)
            # logger.info("Alert system started")
            return True
        # logger.info("Alert system already active")
        return False
    
    def stop(self):
        """Stop the alert system"""
        # logger.info("Stopping alert system")
        if self.active:
            self.active = False
            self.settings["active"] = False
            self.save_settings(self.settings)
            self.stop_event.set()
            # logger.info("Alert system stopped")
            return True
        # logger.info("Alert system already inactive")
        return False
    
    def _should_check_alerts(self):
        """Check if alerts should be active based on day and time settings"""
        if not self.active:
            # logger.debug("Alert system not active")
            return False
            
        # Check day of the week
        now = datetime.now()
        current_day = now.strftime("%A")  # Monday, Tuesday, etc.
        
        # If no days are specified, consider all days valid
        # if not self.settings["days"]:
            # logger.debug("No specific days set, considering all days valid")
        # elif current_day not in self.settings["days"]:
            # logger.debug(f"Current day {current_day} not in allowed days {self.settings['days']}")
            # return False
        
        # Check time range if set
        if self.settings["startTime"] and self.settings["endTime"]:
            try:
                start_time = datetime.strptime(self.settings["startTime"], "%H:%M").time()
                end_time = datetime.strptime(self.settings["endTime"], "%H:%M").time()
                current_time = now.time()
         
                # Handle cases including overnight monitoring
                if start_time <= end_time:
                    if not (start_time <= current_time <= end_time):
                        # logger.debug(f"Current time {current_time} not in range {start_time}-{end_time}")
                        return False
                else:  # e.g., 22:00 - 06:00
                    if not (current_time >= start_time or current_time <= end_time):
                        # logger.debug(f"Current time {current_time} not in overnight range {start_time}-{end_time}")
                        return False
            except ValueError as e:
                # logger.error(f"Error parsing time settings: {e}")
                return False
        
        # logger.debug("Alert conditions met, checks passed")
        return True
    
    def check_and_alert(self, camera_name, region_id, count):
        """Check if alert should be sent and send it if needed"""
        # logger.debug(f"Checking alert for {camera_name}, region {region_id}, count {count}")
        
        # Don't alert if count is zero or negative
        if count <= 0:
            # logger.debug("Count is zero or negative, no alert needed")
            return False
            
        # Only continue if alert conditions are met
        if not self._should_check_alerts():
            # logger.debug("Alert conditions not met")
            return False
            
        # Check if we've already sent an alert recently for this camera/region
        camera_region_key = f"{camera_name}_{region_id}"
        current_time = timer.time()
        
        if camera_region_key in self.last_alert_time:
            # Check if enough time has passed since the last alert
            elapsed_minutes = (current_time - self.last_alert_time[camera_region_key]) / 60
            if elapsed_minutes < self.settings["alertFrequency"]:
                # logger.debug(f"Too soon for another alert. Elapsed: {elapsed_minutes:.2f} min, Required: {self.settings['alertFrequency']} min")
                return False
        


        # Queue the alert for sending in background thread
        # logger.info(f"Queueing alert for {camera_name}, region {region_id}, count {count}")
        self.email_queue.put((camera_name, region_id, count))

        # Update last alert time
        self.last_alert_time[camera_region_key] = current_time
        # Log alert
        self._log_alert(camera_name, region_id, count)
        # logger.info("Alert queued successfully")
        return True
    

    def _email_worker(self):
        """Worker thread to process email queue"""
        # logger.info("Email worker thread started")
        while True:
            try:
                # Get item from queue, with a timeout
                try:
                    camera_name, region_id, count = self.email_queue.get(timeout=1)
                except queue.Empty:
                    # Check if we should exit
                    if self.stop_event.is_set():
                        # logger.info("Email worker thread stopping")
                        break
                    continue
                
                # Send the email
                success = self._send_alert(camera_name, region_id, count)
                # if success:
                    # logger.info(f"Alert sent successfully for {camera_name}, region {region_id}")
                # else:
                    # logger.warning(f"Failed to send alert for {camera_name}, region {region_id}")
                
                # Mark task as done
                self.email_queue.task_done()
                
            except Exception as e:
                # logger.error(f"Error in email worker thread: {e}")
                # Sleep briefly to avoid spinning if there's a persistent error
                timer.sleep(1)


    def _send_alert(self, camera_name, region_id, count):
        """Send alert email"""
        if not self.settings["recipients"]:
            # logger.warning("No email recipients configured")
            return False
            
        try:
            # Email settings - should be configured in production
            smtp_server = "smtp.gmail.com"
            port = 587
            sender_email = "<EMAIL>"  # Update this
            password = "lljb owqm pyrj pwnq"  # Update this - Use app password for Gmail
            
            # Create message
            message = MIMEMultipart("alternative")
            message["Subject"] = f"Alert: People Detected - {camera_name}"
            message["From"] = sender_email
            
            # Email content
            html = f"""
            <html>
              <body>
                <h2>Helmet Detection Alert</h2>
                <p>This is an automated alert from your Helmet Monitoring System.</p>
                <p><strong>Alert Details:</strong></p>
                <ul>
                  <li><strong>Camera:</strong> {camera_name}</li>
                  <li><strong>Region:</strong> {region_id + 1}</li>
                  <li><strong>People Count:</strong> {count}</li>
                  <li><strong>Time:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</li>
                </ul>
                <p>Please check your monitoring system for more details.</p>
              </body>
            </html>
            """
            
            # Attach HTML content
            part = MIMEText(html, "html")
            message.attach(part)
            
            # Create a secure SSL context
            context = ssl.create_default_context()
            
            # Connect to server
            # logger.info(f"Connecting to SMTP server {smtp_server}:{port}")
            with smtplib.SMTP(smtp_server, port) as server:
                server.starttls(context=context)
                server.login(sender_email, password)
                for recipient in self.settings["recipients"]:
                    message["To"] = recipient
                    server.sendmail(sender_email, recipient, message.as_string())
                    # logger.info(f"Email sent to {recipient}")
            
            # logger.info(f"Alert sent: {camera_name}, Region {region_id + 1}, Count: {count}")
            return True
            
        except Exception as e:
            # logger.error(f"Error sending email alert: {e}")
            return False
    
    def _log_alert(self, camera_name, region_id, count):
        """Log alert to file"""
        try:
            logs = []
            if Path(ALERT_LOG_FILE).exists():
                with open(ALERT_LOG_FILE, "r") as file:
                    logs = json.load(file)
            
            logs.append({
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "camera": camera_name,
                "region": region_id + 1,
                "count": count
            })
            
            # Keep only the last 1000 logs
            if len(logs) > 1000:
                logs = logs[-1000:]
                
            with open(ALERT_LOG_FILE, "w") as file:
                json.dump(logs, file, indent=4)
                
            # logger.info(f"Alert logged to {ALERT_LOG_FILE}")
                
        except Exception as e:
            pass
            # logger.error(f"Error logging alert: {e}")

    def cleanup(self):
        """Cleanup resources when shutting down"""
        # logger.info("Cleaning up alert system resources")
        self.stop_event.set()
        if self.email_worker and self.email_worker.is_alive():
            # logger.info("Waiting for email worker thread to finish...")
            self.email_worker.join(timeout=5)
            # if self.email_worker.is_alive():
                # logger.warning("Email worker thread did not finish in time")
        # logger.info("Alert system cleanup complete")