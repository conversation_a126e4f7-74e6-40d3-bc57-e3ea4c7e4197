* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
  background-color: #f5f7fa;
  min-height: 100vh;
  overflow: hidden;
}

.container {
  display: grid;
  grid-template-columns: 280px 1fr 350px;
  width: 100vw;
  height: 100vh;
  background: #f5f7fa;
}

/* Sidebar styling */
.sidebar {
  background-color: #1e2635;
  color: #fff;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  box-shadow: 3px 0 10px rgba(0, 0, 0, 0.1);
}

.logo {
  margin-top: 20px;
  text-align: center;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo img {
  width: 85%;
  max-width: 150px;
  height: auto;
}

.buttons {
  margin-top: 30px;
}

.menu-btn, .addcamera-btn, .homepage-btn, .upload-btn {
  margin: 12px 0;
  padding: 12px 20px;
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  width: 100%;
  text-align: left;
  font-size: 14px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.menu-btn:hover, .addcamera-btn:hover, .homepage-btn:hover, .upload-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Main content area */
.main {
  padding: 25px;
  overflow-y: auto;
  background-color: #f5f7fa;
  height: 100vh;
}

.webcam-section {
  margin-bottom: 20px;
}

.webcam-section h2 {
  color: #333;
  font-size: 24px;
  margin-bottom: 15px;
  font-weight: 500;
}

.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
  gap: 25px;
  align-items: start;
}

.camera-container {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: transform 0.3s ease;
}

.camera-container:hover {
  transform: translateY(-5px);
}

.camera-container h3 {
  background-color: #1e2635;
  color: white;
  padding: 15px;
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

.camera-container img {
  width: 100%;
  height: auto;
  object-fit: cover;
  display: block;
}

/* Live observations panel */
.live-observations {
  background-color: #fff;
  height: 100vh;
  box-shadow: -3px 0 10px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.live-observations h2 {
  color: #1e2635;
  font-size: 20px;
  padding: 25px 20px 15px;
  margin: 0;
  font-weight: 500;
  background-color: #f5f7fa;
  border-bottom: 1px solid #eaeaea;
  position: sticky;
  top: 0;
  z-index: 5;
}

.live-observations ul {
  list-style: none;
  padding: 15px;
}

.live-observations li {
  margin-bottom: 15px;
  padding: 15px;
  background: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  font-size: 14px;
  transition: all 0.2s ease;
  border-left: 4px solid #1e2635;
}

.live-observations li:hover {
  background: #f0f0f0;
}

/* Modal window styling */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.6);
  animation: fadeIn 0.3s;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-content {
  background-color: #fff;
  margin: 5% auto;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  width: 550px;
  max-width: 80%;
  position: relative;
  animation: slideDown 0.4s;
}

@keyframes slideDown {
  from { transform: translateY(-50px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.modal-content h2 {
  font-size: 24px;
  color: #1e2635;
  margin-bottom: 25px;
  text-align: center;
  font-weight: 500;
}

.close-btn {
  position: absolute;
  top: 15px;
  right: 15px;
  color: #999;
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
  transition: color 0.2s;
}

.close-btn:hover {
  color: #333;
}

#cameraForm label {
  display: block;
  margin-bottom: 8px;
  color: #555;
  font-size: 14px;
}

#cameraForm input {
  width: 100%;
  padding: 12px;
  margin-bottom: 20px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border 0.3s;
}

#cameraForm input:focus {
  border-color: #1e2635;
  outline: none;
  box-shadow: 0 0 0 2px rgba(30, 38, 53, 0.1);
}

#cameraForm button {
  padding: 12px 20px;
  background-color: #1e2635;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  margin-right: 10px;
  transition: background-color 0.2s;
}

#cameraForm button:hover {
  background-color: #333c4e;
}

#cameraList {
  margin-top: 25px;
  padding: 15px;
  background-color: #f7f7f7;
  border-radius: 8px;
  max-height: 250px;
  overflow-y: auto;
}

#cameraList p {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: white;
  margin-bottom: 8px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

#cameraList button {
  background-color: #ff4757;
  color: white;
  padding: 5px 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

#cameraList button:hover {
  background-color: #ff6b81;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .container {
    grid-template-columns: 220px 1fr 300px;
  }
  
  .video-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

@media (max-width: 992px) {
  .container {
    grid-template-columns: 200px 1fr 280px;
  }
}

@media (max-width: 768px) {
  .container {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr auto;
  }
  
  .sidebar {
    height: auto;
    padding: 15px;
  }
  
  .live-observations {
    height: auto;
    max-height: 300px;
  }
}