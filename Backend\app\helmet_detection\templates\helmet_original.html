<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PPE Detection Dashboard</title>
  <link rel="stylesheet" href="{{ url_for('helmet_detection_static', path='helmet_detection.css') }}">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css">
</head>
<body>
  <div class="container">
    <div class="sidebar">
      <div>
        <div class="logo">
          <img src="/static/logo.png" alt="Logo">
        </div>
        <div class="buttons">
          <button class="menu-btn" id="startStreamBtn">
            <i class="fas fa-play-circle fa-fw" style="margin-right: 10px;"></i> Start Monitoring
          </button>
          <button class="menu-btn" id="stopStreamBtn">
            <i class="fas fa-stop-circle fa-fw" style="margin-right: 10px;"></i> Stop Monitoring
          </button>
          <button class="addcamera-btn" id="managecamera">
            <i class="fas fa-video fa-fw" style="margin-right: 10px;"></i> Camera Management
          </button>
          <label for="testVideoFile" class="upload-btn">
            <i class="fas fa-upload fa-fw" style="margin-right: 10px;"></i> Upload Test Video
          </label>
          <input type="file" id="testVideoFile" accept="video/*" style="display: none;">
        </div>
      </div>
      <button class="homepage-btn" id="homepage">
        <i class="fas fa-home fa-fw" style="margin-right: 10px;"></i> Go To Homepage
      </button>
    </div>

    <div class="main">
      <div class="webcam-section" id="webcamSection">
        <h2><i class="fas fa-hard-hat" style="margin-right: 10px;"></i>PPE Detection Dashboard</h2>
      </div>
      <div class="video-grid" id="feeds">
        <!-- Video feeds will be added dynamically -->
      </div>
    </div>

    <div class="live-observations">
      <h2><i class="fas fa-clipboard-list" style="margin-right: 10px;"></i>Live Observations</h2>
      <ul id="observationsList">
        <!-- Observations will be dynamically added here -->
      </ul>
    </div>
  </div>

  <!-- Popup window for managing cameras -->
  <div id="manageCameraModal" class="modal">
    <div class="modal-content">
      <span class="close-btn" id="closeModalBtn">&times;</span>
      <h2><i class="fas fa-video" style="margin-right: 10px;"></i>Add RTSP Camera</h2>
      <form id="cameraForm">
        <label for="cameraName">Camera Name (Unique):</label>
        <input type="text" id="cameraName" name="cameraName" placeholder="Enter camera name" required>
        
        <label for="rtspUrl">RTSP URL:</label>
        <input type="text" id="rtspUrl" name="rtspUrl" placeholder="rtsp://" required>
        
        <div style="display: flex; gap: 10px;">
          <button type="submit">Add Camera</button>
          <button type="button" onclick="toggleList()">
            <i class="fas fa-list" style="margin-right: 5px;"></i> Show Cameras
          </button>
        </div>
        
        <div style="display: none;" id="cameraList">
          <!-- Dynamically populate cameras and their delete buttons -->
        </div>
      </form>
    </div>
  </div>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/js/all.min.js"></script>
  <script>
    const startStreamBtn = document.getElementById('startStreamBtn');
    const stopStreamBtn = document.getElementById('stopStreamBtn');
    const feedsDiv = document.getElementById("feeds");
    const liveObservations = document.querySelector(".live-observations ul");
    const homepageBtn = document.getElementById("homepage");

    let websocket = null;
    
    // Event listener for the "Go to Homepage" button
    homepageBtn.addEventListener("click", () => {
      window.location.href = "/";
    });

    // Function to start the video stream
    async function startStream() {
      const fileInput = document.getElementById('testVideoFile');
      const file = fileInput.files[0];

      let options = {};

      if (file) {
        const formData = new FormData();
        formData.append('file', file);
        options.method = 'POST';
        options.body = formData;
      } else {
        options.method = 'POST';
      }

      try {
        startStreamBtn.disabled = true;
        startStreamBtn.innerHTML = '<i class="fas fa-spinner fa-spin fa-fw" style="margin-right: 10px;"></i> Starting...';
        
        const response = await fetch('/helmet_detection/start-helmet_detection', options);
        if (response.ok) {
          console.log("Stream started successfully");
          startWebSocketFeed();
          startStreamBtn.innerHTML = '<i class="fas fa-check-circle fa-fw" style="margin-right: 10px;"></i> Monitoring Active';
        } else {
          console.error("Failed to start stream");
          startStreamBtn.disabled = false;
          startStreamBtn.innerHTML = '<i class="fas fa-play-circle fa-fw" style="margin-right: 10px;"></i> Start Monitoring';
        }
      } catch (error) {
        console.error("Error starting stream:", error);
        startStreamBtn.disabled = false;
        startStreamBtn.innerHTML = '<i class="fas fa-play-circle fa-fw" style="margin-right: 10px;"></i> Start Monitoring';
      }
    }

    // Function to stop the video stream
    async function stopStream() {
      try {
        stopStreamBtn.disabled = true;
        stopStreamBtn.innerHTML = '<i class="fas fa-spinner fa-spin fa-fw" style="margin-right: 10px;"></i> Stopping...';
        
        if (websocket) {
          websocket.close(); // Close the WebSocket connection
        }
        
        const response = await fetch('/helmet_detection/stop-stream', { method: 'POST' });
        if (response.ok) {
          console.log("Stream stopped successfully");
          feedsDiv.innerHTML = ""; // Clear video feeds
          liveObservations.innerHTML = ""; // Clear observations
          startStreamBtn.disabled = false;
          startStreamBtn.innerHTML = '<i class="fas fa-play-circle fa-fw" style="margin-right: 10px;"></i> Start Monitoring';
        } else {
          console.error("Failed to stop stream");
        }
        
        stopStreamBtn.disabled = false;
        stopStreamBtn.innerHTML = '<i class="fas fa-stop-circle fa-fw" style="margin-right: 10px;"></i> Stop Monitoring';
      } catch (error) {
        console.error("Error stopping stream:", error);
        stopStreamBtn.disabled = false;
        stopStreamBtn.innerHTML = '<i class="fas fa-stop-circle fa-fw" style="margin-right: 10px;"></i> Stop Monitoring';
      }
    }

    // Function to start the WebSocket connection and handle incoming frames and counts
    function startWebSocketFeed() {
      websocket = new WebSocket("ws://localhost:8000/helmet_detection/ws");
      websocket.binaryType = 'arraybuffer'; 
      
      let lastMetadata = null;

      websocket.onopen = () => {
        console.log("Websocket connection established");
      };

      websocket.onmessage = (event) => {
        if (typeof event.data == 'string') {
          lastMetadata = event.data.split(':');
        } else if (event.data instanceof ArrayBuffer) {
          if (lastMetadata) {
            const [cameraId, cam_name, person_count, no_helmet_count, no_vest_count, no_gloves_count] = lastMetadata;
          
            let warnings = [parseInt(person_count), parseInt(no_helmet_count), parseInt(no_vest_count), parseInt(no_gloves_count)];

            if (isNaN(warnings[0]) || isNaN(warnings[1]) || isNaN(warnings[2]) || isNaN(warnings[3])) {
              console.error("Invalid warning data received:", lastMetadata);
              warnings = null;
            }

            // Convert ArrayBuffer to Blob and create URL
            const blob = new Blob([event.data], { type: 'image/jpeg' });
            const url = URL.createObjectURL(blob);
            
            // Update video feed
            let cameraContainer = document.querySelector(`#camera-container-${cameraId}`);
            if (!cameraContainer) {
              // Create a new container for this camera
              cameraContainer = document.createElement("div");
              cameraContainer.id = `camera-container-${cameraId}`;
              cameraContainer.classList.add("camera-container");

              // Add camera label
              const cameraLabel = document.createElement("h3");
              cameraLabel.innerHTML = `<i class="fas fa-video fa-fw" style="margin-right: 10px;"></i>${cam_name}`;
              cameraContainer.appendChild(cameraLabel);

              // Add the video feed
              const imgElem = document.createElement("img");
              imgElem.id = `feed-${cameraId}`;
              imgElem.alt = "Processed Video Feed";
              cameraContainer.appendChild(imgElem);

              // Append the camera container to the feeds
              feedsDiv.appendChild(cameraContainer);
            }
            
            // Update the video feed image
            const imgElem = document.querySelector(`#feed-${cameraId}`);
            imgElem.src = url;

            // Update live observations
            let observationElem = document.querySelector(`#observation-${cameraId}`);
            if (!observationElem) {
              // Create a new list item if it doesn't exist
              observationElem = document.createElement("li");
              observationElem.id = `observation-${cameraId}`;
              liveObservations.appendChild(observationElem);
            }
            
            // Update the observation text with icons
            let observationText = `<strong><i class="fas fa-video fa-fw"></i> ${cam_name}</strong><br><br>`;

            if (warnings === null) {
              observationText += `<span style="color: #ff4757;"><i class="fas fa-exclamation-triangle"></i> Camera Offline</span><br>`;
            } else {
              observationText += `<i class="fas fa-user fa-fw"></i> Total People: <strong>${warnings[0]}</strong><br>`;
              
              if (warnings[1] > 0) {
                observationText += `<i class="fas fa-hard-hat fa-fw" style="color: #ff4757;"></i> No Helmet: <strong style="color: #ff4757;">${warnings[1]}</strong><br>`;
              } else {
                observationText += `<i class="fas fa-hard-hat fa-fw" style="color: #27ae60;"></i> No Helmet: <strong style="color: #27ae60;">${warnings[1]}</strong><br>`;
              }
              
              if (warnings[2] > 0) {
                observationText += `<i class="fas fa-vest fa-fw" style="color: #ff4757;"></i> No Vest: <strong style="color: #ff4757;">${warnings[2]}</strong><br>`;
              } else {
                observationText += `<i class="fas fa-vest fa-fw" style="color: #27ae60;"></i> No Vest: <strong style="color: #27ae60;">${warnings[2]}</strong><br>`;
              }
              
              if (warnings[3] > 0) {
                observationText += `<i class="fas fa-mitten fa-fw" style="color: #ff4757;"></i> No Gloves: <strong style="color: #ff4757;">${warnings[3]}</strong><br>`;
              } else {
                observationText += `<i class="fas fa-mitten fa-fw" style="color: #27ae60;"></i> No Gloves: <strong style="color: #27ae60;">${warnings[3]}</strong><br>`;
              }
              
              const timestamp = new Date().toLocaleTimeString();
              observationText += `<div style="font-size: 12px; color: #999; margin-top: 10px;"><i class="fas fa-clock fa-fw"></i> ${timestamp}</div>`;
            }

            observationElem.innerHTML = observationText.trim();
            // Reset metadata after processing
            lastMetadata = null;
          }
        }
      };

      websocket.onerror = (error) => {
        console.error("WebSocket error:", error);
      };

      websocket.onclose = () => {
        console.log("WebSocket closed");
      };
    }

    // Event listeners for start and stop buttons
    startStreamBtn.addEventListener('click', startStream);
    stopStreamBtn.addEventListener('click', stopStream);

    // MANAGE CAMERA BUTTON POPUP 
    let showList = false;
    const managecameraBtn = document.getElementById("managecamera");
    const modal = document.getElementById("manageCameraModal");
    const closeModalBtn = document.getElementById("closeModalBtn");
    const cameraForm = document.getElementById("cameraForm");

    function toggleList() {
      showList = !showList;
      const cameraList = document.getElementById('cameraList');
      if (showList) {
        cameraList.style.display = 'block';
      } else {
        cameraList.style.display = 'none';
      }
    }
    
    // Show modal when 'Manage Camera' button is clicked
    managecameraBtn.addEventListener("click", () => {
      modal.style.display = "block";
    });

    // Close modal when the close button is clicked
    closeModalBtn.addEventListener("click", () => {
      modal.style.display = "none";
    });

    // Close modal when clicking outside the modal
    window.addEventListener("click", (event) => {
      if (event.target === modal) {
        modal.style.display = "none";
      }
    });

    // Handle form submission to add the camera
    cameraForm.addEventListener("submit", async (e) => {
      e.preventDefault();
      
      const cameraName = document.getElementById("cameraName").value;
      const rtspUrl = document.getElementById("rtspUrl").value;
      
      // Send data to backend to save the camera information
      const response = await fetch('/helmet_detection/add-camera', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ cameraName, rtspUrl }),
      });

      const result = await response.json();
      
      if (result.status === 'success') {
        showNotification('Camera added successfully!', 'success');
        cameraForm.reset();
        loadCameras();
      } else if (result.status === 'error') {
        showNotification(`RTSP URL already exists for camera: ${result.message.split(': ')[1]}`, 'error');
      } else if (result.status === 'samename') {
        showNotification('Camera name already exists', 'error');
      } else {
        showNotification('Error adding camera.', 'error');
      }
    });

    // Function to show notification
    function showNotification(message, type) {
      const notification = document.createElement('div');
      notification.className = `notification ${type}`;
      notification.innerHTML = `
        <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>
        ${message}
      `;
      document.body.appendChild(notification);
      
      setTimeout(() => {
        notification.classList.add('show');
      }, 10);
      
      setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
          notification.remove();
        }, 300);
      }, 3000);
    }

    // Function to load and display the list of cameras
    async function loadCameras() {
      const response = await fetch("/helmet_detection/get-cameras", {
        method: "GET",
      });
      const cameras = await response.json();

      const cameraList = document.getElementById("cameraList");
      cameraList.innerHTML = ""; // Clear previous list

      if (Object.keys(cameras).length === 0) {
        cameraList.innerHTML = "<p style='text-align: center; color: #777;'>No cameras added yet</p>";
        return;
      }

      for (const [cameraName, rtspUrl] of Object.entries(cameras)) {
        const cameraItem = document.createElement("div");
        cameraItem.innerHTML = `
          <p>
            <strong><i class="fas fa-video fa-fw"></i> ${cameraName}</strong>
            <button onclick="deleteCamera('${cameraName}')"><i class="fas fa-trash"></i></button>
          </p>
        `;
        cameraList.appendChild(cameraItem);
      }
    }

    // Function to delete a camera
    async function deleteCamera(cameraName) {
      const confirmed = confirm(`Are you sure you want to delete the camera "${cameraName}"?`);
      if (!confirmed) return;

      const response = await fetch(`/helmet_detection/delete-camera/${cameraName}`, {
        method: "DELETE",
      });

      const result = await response.json();

      if (result.status === "success") {
        showNotification(result.message, 'success');
        loadCameras(); // Refresh the list
      } else {
        showNotification("Error deleting camera: " + result.message, 'error');
      }
    }

    // Load the camera list when the modal is opened
    managecameraBtn.addEventListener("click", () => {
      modal.style.display = "block";
      loadCameras(); // Populate the camera list
    });

    // Make deleteCamera function available globally
    window.deleteCamera = deleteCamera;
    window.toggleList = toggleList;
  </script>
  
  <style>
    /* Adding notification styles inline to avoid another CSS file */
    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 20px;
      border-radius: 5px;
      color: white;
      font-size: 14px;
      z-index: 1100;
      display: flex;
      align-items: center;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateX(120%);
      transition: transform 0.3s ease;
    }
    
    .notification.show {
      transform: translateX(0);
    }
    
    .notification.success {
      background-color: #2ecc71;
    }
    
    .notification.error {
      background-color: #e74c3c;
    }
    
    .notification i {
      margin-right: 10px;
      font-size: 16px;
    }
  </style>
</body>
</html>
