from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password, hashed_password):
    """Verify a plain password against a hashed password"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password):
    """Hash a password"""
    return pwd_context.hash(password)

def has_permission(user, permission_name: str) -> bool:
    """Check if user has a specific permission"""
    for role in user.roles:
        for role_permission in role.permissions:
            if role_permission.permission.name == permission_name:
                return True
    return False

def has_role(user, role_name: str) -> bool:
    """Check if user has a specific role"""
    for role in user.roles:
        if role.name == role_name:
            return True
    return False
