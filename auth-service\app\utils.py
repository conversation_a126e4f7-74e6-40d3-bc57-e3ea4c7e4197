from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password, hashed_password):
    """Verify a plain password against a hashed password"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password):
    """Hash a password"""
    return pwd_context.hash(password)

def has_permission(user, permission_name: str) -> bool:
    """Check if user has a specific permission (simplified)"""
    if hasattr(user, 'role'):
        if user.role == "admin":
            return True  # Admin has all permissions
        elif permission_name == "view_data":
            return True  # All users can view data
    return False

def has_role(user, role_name: str) -> bool:
    """Check if user has a specific role (simplified)"""
    if hasattr(user, 'role'):
        return user.role == role_name
    return False
