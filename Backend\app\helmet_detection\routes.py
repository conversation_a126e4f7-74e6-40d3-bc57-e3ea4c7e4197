from fastapi import APIRouter, WebSocket, Request, HTTPException, UploadFile, File
from fastapi.templating import Jin<PERSON>2Templates
from app.helmet_detection.utils import HelmetDetection
from app.helmet_detection.alert_system import PPEAlertSystem
import asyncio
import json
from pathlib import Path
from pydantic import BaseModel
from typing import List
import cv2
import shutil
import os
import base64
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('PPE_API_Routes')

router = APIRouter()

templates = Jinja2Templates(directory="app/helmet_detection/templates")

@router.get("/helmet_detection")
async def show_helmet_detection_page(request: Request):
    return templates.TemplateResponse("helmet_detection.html", {"request": request})

# @router.get("/upload")
# async def show_expand_page(request: Request):
#     return templates.TemplateResponse("upload.html", {"request": request})

# Initialize HelmetDetection without starting it
helmet_stream = HelmetDetection(
    object_model="./app/helmet_detection/models/best.pt",
    pose_model="./app/helmet_detection/models/yolov8s-pose.pt",
    conf_value=0.5
)

# Initialize PPE Alert System
ppe_alert_system = PPEAlertSystem()

# Global variables for camera management
current_camera_selection = "all"  # "all" or specific camera name

def shutdown_event():
    helmet_stream.stop()

# API to start video processing
@router.post("/start-stream")
async def start_stream():
    if not helmet_stream.running:
        camera_details = load_cameras()
        if not camera_details:
            return {"status": "error", "message": "No cameras configured."}

        print(f"Loaded Cameras: {camera_details}")
        helmet_stream.start(camera_details)

    return {"status": "helmet_stream started"}

# API to stop video processing
@router.post("/stop-stream")
async def stop_stream():
    if helmet_stream.running:
        helmet_stream.stop()
        helmet_stream.helmet_frames = []
    return {"status": "helmet_stream stopped"}

# Camera selection endpoints
@router.post("/select-camera/{camera_name}")
async def select_camera(camera_name: str):
    global current_camera_selection
    current_camera_selection = camera_name
    return {"status": "success", "selected_camera": camera_name}

@router.post("/select-all-cameras")
async def select_all_cameras():
    global current_camera_selection
    current_camera_selection = "all"
    return {"status": "success", "selected_camera": "all"}

# WebSocket endpoint for video feeds
@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    try:
        while True:
            if helmet_stream.running:
                if current_camera_selection == "all":
                    # Send data for all cameras
                    for idx, frame in enumerate(helmet_stream.helmet_frames):
                        if frame is not None:
                            with helmet_stream.locks[idx]:
                                warnings = helmet_stream.warning_count[idx]
                                cam_name = helmet_stream.camera_name[idx]
                                helmet_frame = helmet_stream.helmet_frames[idx]

                            # Encode frame to base64
                            if isinstance(helmet_frame, bytes):
                                frame_data = helmet_frame
                            else:
                                success, buffer = cv2.imencode('.jpg', helmet_frame)
                                if success:
                                    frame_data = buffer.tobytes()
                                else:
                                    continue

                            frame_base64 = base64.b64encode(frame_data).decode('utf-8')
                            image_url = f"data:image/jpeg;base64,{frame_base64}"

                            # Check for PPE violations and send alerts
                            if warnings['no_helmet_count'] > 0:
                                ppe_alert_system.check_and_alert(cam_name, "No Helmet", warnings['no_helmet_count'])
                            if warnings['no_vest_count'] > 0:
                                ppe_alert_system.check_and_alert(cam_name, "No Vest", warnings['no_vest_count'])
                            if warnings['no_gloves_count'] > 0:
                                ppe_alert_system.check_and_alert(cam_name, "No Gloves", warnings['no_gloves_count'])

                            # Send video frame data
                            await websocket.send_json({
                                "type": "video_frame",
                                "camera_name": cam_name,
                                "image_url": image_url,
                                "metadata": {
                                    "person_count": warnings['person_count'],
                                    "no_helmet_count": warnings['no_helmet_count'],
                                    "no_vest_count": warnings['no_vest_count'],
                                    "no_gloves_count": warnings['no_gloves_count']
                                }
                            })
                else:
                    # Send data for specific camera
                    for idx, cam_name in enumerate(helmet_stream.camera_name):
                        if cam_name == current_camera_selection:
                            frame = helmet_stream.helmet_frames[idx]
                            if frame is not None:
                                with helmet_stream.locks[idx]:
                                    warnings = helmet_stream.warning_count[idx]
                                    helmet_frame = helmet_stream.helmet_frames[idx]

                                # Encode frame to base64
                                if isinstance(helmet_frame, bytes):
                                    frame_data = helmet_frame
                                else:
                                    success, buffer = cv2.imencode('.jpg', helmet_frame)
                                    if success:
                                        frame_data = buffer.tobytes()
                                    else:
                                        continue

                                frame_base64 = base64.b64encode(frame_data).decode('utf-8')
                                image_url = f"data:image/jpeg;base64,{frame_base64}"

                                # Check for PPE violations and send alerts
                                if warnings['no_helmet_count'] > 0:
                                    ppe_alert_system.check_and_alert(cam_name, "No Helmet", warnings['no_helmet_count'])
                                if warnings['no_vest_count'] > 0:
                                    ppe_alert_system.check_and_alert(cam_name, "No Vest", warnings['no_vest_count'])
                                if warnings['no_gloves_count'] > 0:
                                    ppe_alert_system.check_and_alert(cam_name, "No Gloves", warnings['no_gloves_count'])

                                # Send video frame data
                                await websocket.send_json({
                                    "type": "video_frame",
                                    "camera_name": cam_name,
                                    "image_url": image_url,
                                    "metadata": {
                                        "person_count": warnings['person_count'],
                                        "no_helmet_count": warnings['no_helmet_count'],
                                        "no_vest_count": warnings['no_vest_count'],
                                        "no_gloves_count": warnings['no_gloves_count']
                                    }
                                })
                            break

                await asyncio.sleep(0.03)
            else:
                await asyncio.sleep(0.5)
    except Exception as e:
        print("WebSocket disconnected:", e)


# Video upload endpoint
@router.post("/upload-video")
async def upload_video(file: UploadFile = File(...)):
    camera_details = load_cameras()
    test_video_path = None

    if file:
        save_path = f"./app/helmet_detection/uploads/{file.filename}"
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        with open(save_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        test_video_path = save_path

    # Always stop the stream before starting a new one
    if helmet_stream.running:
        helmet_stream.stop()

    helmet_stream.start(camera_details, test_video_path=test_video_path)
    return {"status": "helmet_stream restarted with new video"}



# Camera management endpoints
CAMERAS_FILE_PATH = "app/helmet_detection/cameras.json"

# Load cameras data from the file if it exists
def load_cameras():
    if Path(CAMERAS_FILE_PATH).exists():
        with open(CAMERAS_FILE_PATH, "r") as file:
            return json.load(file)
    return {}

# Save cameras data to the file
def save_cameras(cameras_data):
    os.makedirs(os.path.dirname(CAMERAS_FILE_PATH), exist_ok=True)
    with open(CAMERAS_FILE_PATH, "w") as file:
        json.dump(cameras_data, file, indent=4)

# Pydantic model for receiving camera data
class CameraData(BaseModel):
    cameraName: str
    rtspUrl: str

# Route to handle adding a new camera
@router.post("/add-camera")
async def add_camera(camera_data: CameraData):
    cameras = load_cameras()

    # Check if the camera name already exists
    if camera_data.cameraName in cameras:
        return {"status": "samename", "message": "SAME NAME ALREADY EXIST"}

    # Check if the RTSP URL already exists in the values
    for existing_camera, existing_url in cameras.items():
        if existing_url[0] == camera_data.rtspUrl:
            return {"status": "error", "message": f"RTSP URL already exists for camera: {existing_camera}"}

    # Add new camera with empty ROI coordinates
    cameras[camera_data.cameraName.upper()] = [camera_data.rtspUrl, []]

    # Save the updated data
    save_cameras(cameras)

    return {"status": "success", "message": "Camera added successfully"}

# Route to delete a camera by name
@router.delete("/delete-camera/{camera_name}")
async def delete_camera(camera_name: str):
    cameras = load_cameras()

    if camera_name not in cameras:
        raise HTTPException(status_code=404, detail="Camera not found")

    # Remove the camera
    del cameras[camera_name]

    # Save the updated camera list
    save_cameras(cameras)

    return {"status": "success", "message": f"Camera '{camera_name}' deleted successfully"}

@router.get("/get-cameras")
async def get_cameras():
    return load_cameras()


# Alert Management API Routes

# Pydantic model for alert settings
class AlertSettings(BaseModel):
    active: bool
    days: List[str]
    startTime: str
    endTime: str
    alertFrequency: int
    recipients: List[str]

@router.get("/get-alert-settings")
async def get_alert_settings():
    """Get current alert settings"""
    try:
        settings = ppe_alert_system.load_settings()
        return settings
    except Exception as e:
        logger.error(f"Error getting alert settings: {e}")
        raise HTTPException(status_code=500, detail="Error loading alert settings")

@router.post("/save-alert-settings")
async def save_alert_settings(settings: AlertSettings):
    """Save alert settings"""
    try:
        settings_dict = settings.model_dump()
        success = ppe_alert_system.save_settings(settings_dict)

        if success:
            return {"status": "success", "message": "Alert settings saved successfully"}
        else:
            return {"status": "error", "message": "Failed to save alert settings"}
    except Exception as e:
        logger.error(f"Error saving alert settings: {e}")
        return {"status": "error", "message": f"Error saving alert settings: {str(e)}"}

@router.post("/start-alerts")
async def start_alerts():
    """Start the alert system"""
    try:
        success = ppe_alert_system.start()

        if success:
            return {"status": "success", "message": "PPE Alert system started successfully"}
        else:
            return {"status": "error", "message": "Failed to start alert system"}
    except Exception as e:
        logger.error(f"Error starting alerts: {e}")
        return {"status": "error", "message": f"Error starting alerts: {str(e)}"}

@router.post("/stop-alerts")
async def stop_alerts():
    """Stop the alert system"""
    try:
        success = ppe_alert_system.stop()

        if success:
            return {"status": "success", "message": "PPE Alert system stopped successfully"}
        else:
            return {"status": "error", "message": "Failed to stop alert system"}
    except Exception as e:
        logger.error(f"Error stopping alerts: {e}")
        return {"status": "error", "message": f"Error stopping alerts: {str(e)}"}

@router.get("/alert-status")
async def get_alert_status():
    """Get current alert system status"""
    try:
        return {
            "status": "success",
            "active": ppe_alert_system.active,
            "settings": ppe_alert_system.settings
        }
    except Exception as e:
        logger.error(f"Error getting alert status: {e}")
        return {"status": "error", "message": f"Error getting alert status: {str(e)}"}


