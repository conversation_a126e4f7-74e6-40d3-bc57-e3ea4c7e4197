from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, JSON
from app.core.database import Base
from sqlalchemy.orm import relationship
from pydantic import BaseModel
from typing import List
from datetime import datetime, timezone
from fastapi import UploadFile


class User(Base):
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)

    images = relationship("Image", back_populates="user", cascade="all, delete-orphan")
    attendances = relationship("Attendance", back_populates="user", cascade="all, delete-orphan")
    encodings = relationship("Encoding", back_populates="user", cascade="all, delete-orphan")

class Encoding(Base):
    __tablename__ = 'encodings'

    id = Column(Integer, primary_key=True, index=True)
    encoding = Column(JSON, nullable=False)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    image_id = Column(Integer, ForeignKey('images.id'))

    user = relationship("User", back_populates="encodings")
    image = relationship("Image", back_populates="encodings")

    def __repr__(self):
        return f"<Encoding(id={self.id}, user_id={self.user_id}, encoding_size={len(self.encoding)})>"


class Image(Base):
    __tablename__ = 'images'

    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String(100), index=True)
    image_url = Column(String(255), nullable=True)

    user_id = Column(Integer, ForeignKey('users.id'))

    user = relationship(User, back_populates="images")
    encodings = relationship("Encoding", back_populates="image")


class Attendance(Base):
    __tablename__ = 'attendances'

    id = Column(Integer, primary_key=True, index=True)
    timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    user_id = Column(Integer, ForeignKey('users.id'))
    user = relationship(User, back_populates="attendances")


class UserCreate(BaseModel):
    username: str
    email: str
    image_urls: List[UploadFile] = []

class AttendanceCreate(BaseModel):
    user_id: int
    timestamp: datetime


class EncodingCreate(BaseModel):
    encoding: List[float]
    user_id: int
    image_id: int = None


class CameraData(BaseModel):
    cameraName: str
    rtspUrl: str

class Unknown(Base):
    __tablename__ = 'unknowns'

    id = Column(Integer, primary_key=True, index=True)
    persistent_id = Column(String(64), unique=True)
    encoding = Column(JSON, nullable=False)
    timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f"<Unknown(persistent_id={self.persistent_id}, timestamp={self.timestamp})>"
