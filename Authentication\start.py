#!/usr/bin/env python3
"""
Startup script for Vigilant Eye Authentication Service
"""

import uvicorn
import os
import sys
from pathlib import Path

# Add the authentication directory to Python path
auth_dir = Path(__file__).parent
sys.path.insert(0, str(auth_dir))

def create_default_user():
    """Create a default admin user if no users exist"""
    try:
        from database import SessionLocal, create_tables
        from auth import create_user, get_user, UserCreate
        
        # Create tables first
        create_tables()
        
        db = SessionLocal()
        try:
            # Check if admin user exists
            admin_user = get_user(db, "admin")
            if not admin_user:
                print("Creating default admin user...")
                user_data = UserCreate(
                    username="admin",
                    email="<EMAIL>",
                    full_name="System Administrator",
                    password="admin123"  # Change this in production!
                )
                create_user(db, user_data)
                print("Default admin user created:")
                print("  Username: admin")
                print("  Password: admin123")
                print("  Please change the password after first login!")
            else:
                print("Admin user already exists.")
        finally:
            db.close()
            
    except Exception as e:
        print(f"Error creating default user: {e}")

def main():
    """Main startup function"""
    print("=" * 50)
    print("Vigilant Eye Authentication Service")
    print("=" * 50)
    
    # Create default user
    create_default_user()
    
    # Get configuration from environment
    host = os.getenv("AUTH_HOST", "0.0.0.0")
    port = int(os.getenv("AUTH_PORT", "8001"))
    reload = os.getenv("AUTH_RELOAD", "true").lower() == "true"
    
    print(f"Starting authentication service on {host}:{port}")
    print(f"Reload mode: {reload}")
    print("=" * 50)
    
    # Start the server
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=reload,
        log_level="info"
    )

if __name__ == "__main__":
    main()
