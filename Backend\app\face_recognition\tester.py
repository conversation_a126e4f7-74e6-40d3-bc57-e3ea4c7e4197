import threading
import queue
import time
import base64
import cv2
import datetime
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor
from deep_sort_realtime.deepsort_tracker import DeepSort
from ultralytics import YOLO
from . import models
from .utils import (generate_encoding, calculate_cosine_similarity)
import numpy as np
import uuid
from .models import Encoding, User
from app.core import database
from scipy.spatial.distance import cosine
from sqlalchemy.orm import Session
from app.core.database import sessionmaker, SessionLocal
from keras_facenet import FaceNet
from .utils import load_cameras, save_person_image

class FaceDetection:
    def __init__(self, model_path, db: Session):
        self.model = YOLO(model_path)
        self.db = db
        self.running = False
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
        self.cap_devices = []
        self.frame_queues = []
        self.frames = []
        self.camera_threads = []
        self.process_threads = []
        self.embedder = FaceNet()
        self.lock = threading.Lock()  # Lock for thread safety
        self.cameraname = []
        self.db_encodings_cache = {}
        self.db_users_cache = {}
        self.threshold = None  # Will be initialized in start method
        self.unknown_track_map = {}  # {track_id: persistent_unknown_id}


    def start(self, threshold=0.7):
        print(threshold)
        """Starts the face detection system.

        Args:
            threshold (float, optional): Similarity threshold for face recognition.
                                        Defaults to 0.7 if not provided.
        """
        if self.running:
            print("Face detection system is already running")
            return

        # Initialize threshold with the provided value
        self.threshold = threshold
        print(f"Face detection started with threshold: {self.threshold}")

        # Cache database entries to avoid repeated queries
        self.db_encodings_cache = {enc.user_id: np.array(enc.encoding) for enc in self.db.query(Encoding).all()}
        self.db_users_cache = {user.id: user.username for user in self.db.query(User).all()}

        cameras = load_cameras()
        if not cameras:
            print("No cameras configured. Please add cameras first.")
            return

        # Clear previous state
        self.stop_connections()

        # Initialize arrays with appropriate size
        self.cameraname = list(cameras.keys())
        print(f"Camera names: {self.cameraname}")

        camera_urls = []
        for cam_name in self.cameraname:
            url = cameras[cam_name][0]
            if url.isdigit():
                url = int(url)
            camera_urls.append(url)

        print(f"Starting with cameras: {camera_urls}")

        # Initialize cameras
        for cam_url in camera_urls:
            cap = cv2.VideoCapture(cam_url)
            cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

            if cap.isOpened():
                print(f"Camera {cam_url} opened successfully.")
                self.cap_devices.append(cap)
                self.frame_queues.append(queue.Queue(maxsize=10))
            else:
                print(f"Failed to open camera: {cam_url}")

        # Ensure frames array matches the number of cameras
        self.frames = [None] * len(self.cap_devices)

        if not self.running and self.cap_devices:
            self.running = True
            # Start threads for each camera
            for idx, cap in enumerate(self.cap_devices):
                read_thread = self.thread_pool.submit(self.read_frames, idx, cap)
                process_thread = self.thread_pool.submit(self.process_frames, idx)
                self.camera_threads.append(read_thread)
                self.process_threads.append(process_thread)
            print(f"Started {len(self.camera_threads)} camera threads")
        else:
            print("No cameras were successfully initialized")

    def stop_connections(self):
        """Clean up camera connections without stopping the entire system."""
        for cap in self.cap_devices:
            if cap and cap.isOpened():
                cap.release()
        self.cap_devices = []
        self.frame_queues = []
        self.frames = []
        # Keep the cameraname list for reference

    def stop(self):
        """Stops the face detection system."""
        if not self.running:
            return

        print("Stopping face detection system...")
        self.running = False

        # Give threads time to exit gracefully
        time.sleep(0.5)

        # Release all camera resources
        self.stop_connections()

        print("Face detection system stopped")

    def read_frames(self, idx, cap):
        """Reads frames from the camera and adds them to the queue."""
        reconnect_attempts = 0
        max_reconnect_attempts = 5

        while self.running:
            try:
                ret, frame = cap.read()
                if not ret:
                    print(f"Failed to read frame from camera {idx}")
                    reconnect_attempts += 1

                    if reconnect_attempts > max_reconnect_attempts:
                        print(f"Too many failures for camera {idx}, stopping capture")
                        break

                    time.sleep(1)  # Wait before retry
                    continue

                reconnect_attempts = 0  # Reset on successful frame

                # Resize frame for consistency
                frame = cv2.resize(frame, (480, 320),interpolation=cv2.INTER_NEAREST)

                with self.lock:
                    if idx < len(self.frame_queues):  # Make sure the index is still valid
                        if self.frame_queues[idx].full():
                            self.frame_queues[idx].get()  # Remove the oldest frame
                        self.frame_queues[idx].put(frame)
                    else:
                        break  # This camera is no longer in our list, exit thread

            except Exception as e:
                print(f"Error in read_frames for camera {idx}: {e}")
                time.sleep(0.1)  # Prevent CPU spin in case of errors

    def process_frames(self, idx):
        """Processes frames and performs face detection and recognition."""
        deepsort = DeepSort(max_age=7, nn_budget=100)
        tracked_faces = {}
        retry_tracker = {}
        retry_limit = 3

        while self.running:
            try:
                if idx >= len(self.frame_queues):
                    break  # Exit if this camera is no longer valid

                if self.frame_queues[idx].empty():
                    time.sleep(0.01)  # Short sleep when no frames
                    continue

                frame = self.frame_queues[idx].get()

                # Make a copy for processing
                processing_frame = frame.copy()

                detections = []
                results = self.model(processing_frame, stream=True)

                for result in results:
                    for face in result.boxes:
                        x1, y1, x2, y2 = face.xyxy[0].cpu().numpy()
                        confidence = face.conf[0].cpu().item()
                        detections.append(([int(x1), int(y1), int(x2 - x1), int(y2 - y1)], confidence, "face"))
                        # cv2.imshow("face Crop", processing_frame[int(y1):int(y2), int(x1):int(x2)])
                        # cv2.waitKey(1)

                trackers = deepsort.update_tracks(raw_detections=detections, frame=processing_frame)

                for tracker in trackers:
                    if not tracker.is_confirmed():
                        continue

                    track_id = tracker.track_id
                    x1, y1, x2, y2 = tracker.to_ltrb()

                    # Make sure coordinates are valid
                    x1, y1, x2, y2 = max(0, int(x1)), max(0, int(y1)), min(frame.shape[1], int(x2)), min(frame.shape[0], int(y2))
                    if x2 <= x1 or y2 <= y1:
                        continue  # Skip invalid boxes

                    face_image = frame[y1:y2, x1:x2]
                    if face_image.size == 0 or face_image.shape[0] < 50 or face_image.shape[1] < 50:
                        continue  # Skip if face crop is empty or too small

                    if track_id not in tracked_faces:
                        try:
                            face_encoding = self.generate_encoding(face_image)
                            tracked_faces[track_id] = {'encoding': face_encoding, 'last_seen': time.time()}
                            retry_tracker[track_id] = 0
                        except Exception as e:
                            print(f"Error generating encoding: {e}")
                            continue
                    else:
                        tracked_faces[track_id]['last_seen'] = time.time()

                    face_encoding = tracked_faces[track_id]['encoding']
                    most_similar_user, highest_similarity = self.match_face(face_encoding)

                    if most_similar_user and highest_similarity >= self.threshold:
                        track_name = most_similar_user
                        color = (0, 255, 0)
                        user_id = self.get_user_id(most_similar_user)

                        if user_id:
                            self.log_attendance(user_id=user_id)
                            # Only save if the face image is valid
                            if face_image.size > 0 and face_image.shape[0] >= 50 and face_image.shape[1] >= 50:
                                save_person_image(face_image, track_name)
                    else:
                        if retry_tracker.get(track_id, 0) < retry_limit:
                            retry_tracker[track_id] = retry_tracker.get(track_id, 0) + 1
                            try:
                                tracked_faces[track_id]['encoding'] = self.generate_encoding(face_image)
                            except Exception as e:
                                print(f"Error regenerating encoding: {e}")
                            continue
                        else:
                            # Assign a persistent UUID to this track_id if not already assigned
                            if track_id not in self.unknown_track_map:
                                persistent_unknown_id = str(uuid.uuid4())
                                self.unknown_track_map[track_id] = persistent_unknown_id
                            else:
                                persistent_unknown_id = self.unknown_track_map[track_id]

                            track_name = f"unknown_{persistent_unknown_id[:8]}"  # display short UUID in frame
                            color = (0, 0, 255)

                            from .utils import save_unknown_image
                            saved = save_unknown_image(face_image, persistent_unknown_id)  # use persistent ID in image file

                            # Save unknown log to DB only if a new image was saved (avoiding duplicates)
                            if saved:
                                try:
                                    # Check if this persistent_id already exists in the database
                                    existing_unknown = self.db.query(models.Unknown).filter(
                                        models.Unknown.persistent_id == persistent_unknown_id
                                    ).first()

                                    if not existing_unknown:
                                        # Only add if it doesn't exist
                                        unknown_encoding = self.generate_encoding(face_image)
                                        unknown_entry = models.Unknown(persistent_id=persistent_unknown_id, encoding=unknown_encoding.tolist())
                                        self.db.add(unknown_entry)
                                        self.db.commit()
                                        print(f"[INFO] Logged unknown person {persistent_unknown_id}")
                                    else:
                                        print(f"[INFO] Unknown person {persistent_unknown_id} already exists in database, skipping")
                                except Exception as e:
                                    print(f"[ERROR] Failed to log unknown person: {e}")
                                    self.db.rollback()  # Rollback the transaction in case of error


                    cv2.rectangle(processing_frame, (x1, y1), (x2, y2), color, 2)
                    cv2.putText(processing_frame, track_name, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 2)

                # Clean up old tracks
                current_time = time.time()
                tracked_faces = {k: v for k, v in tracked_faces.items()
                                if current_time - v['last_seen'] < 5.0}  # Keep tracks for 5 seconds

                # Convert processed frame to JPEG
                _, buffer = cv2.imencode('.jpg', processing_frame)
                encoded_frame = base64.b64encode(buffer).decode('utf-8')

                with self.lock:
                    if idx < len(self.frames):  # Check if the index is still valid
                        self.frames[idx] = encoded_frame
                        # Debug output to verify frame is being encoded
                        if encoded_frame:
                            frame_length = len(encoded_frame)
                            print(f"Frame {idx} encoded: {frame_length} bytes, camera: {self.cameraname[idx] if idx < len(self.cameraname) else 'unknown'}")
                        else:
                            print(f"Frame {idx} encoding failed!")

            except Exception as e:
                print(f"Error in process_frames for camera {idx}: {e}")
                time.sleep(0.1)  # Prevent CPU spin in case of errors

    def generate_encoding(self, face_image):
        """Generates a face encoding using FaceNet."""
        face_image = cv2.resize(face_image, (160, 160))
        face_image = np.expand_dims(face_image, axis=0)
        return self.embedder.embeddings(face_image)[0]

    def match_face(self, face_encoding):
        """Finds the most similar face in the database."""
        highest_similarity = -1
        most_similar_user = None

        for user_id, db_encoding in self.db_encodings_cache.items():
            similarity = self.calculate_cosine_similarity(face_encoding, db_encoding)
            if similarity > highest_similarity:
                highest_similarity = similarity
                most_similar_user = self.db_users_cache.get(user_id)

        return most_similar_user, highest_similarity

    def get_user_id(self, username: str):
        """Look up user ID by username using the cache."""
        for user_id, user_name in self.db_users_cache.items():
            if user_name == username:
                return user_id
        return None

    def log_attendance(self, user_id):
        """Logs attendance if the user has not been logged in the past minute."""
        try:
            # Check if the session is in a valid state
            if self.db.is_active:
                time_window_start = datetime.datetime.now() - datetime.timedelta(minutes=1)
                if not self.db.query(models.Attendance).filter(models.Attendance.user_id == user_id, models.Attendance.timestamp >= time_window_start).first():
                    self.db.add(models.Attendance(user_id=user_id, timestamp=datetime.datetime.now()))
                    self.db.commit()
                    print(f"Logged attendance for user ID: {user_id}")
            else:
                # If session is not active, create a new one
                print("Session is not active, creating a new one")
                self.db = database.SessionLocal()
                self.log_attendance(user_id)  # Retry with new session
        except Exception as e:
            print(f"Error logging attendance: {e}")
            try:
                self.db.rollback()  # Rollback the transaction in case of error
            except Exception as rollback_error:
                print(f"Error during rollback: {rollback_error}")
                # If rollback fails, create a new session
                self.db = database.SessionLocal()


    def calculate_cosine_similarity(self, enc1, enc2):
        """Calculate cosine similarity between two encodings."""
        enc1 = np.array(enc1)
        enc2 = np.array(enc2)

        if np.linalg.norm(enc1) == 0 or np.linalg.norm(enc2) == 0:
            return 0  # Handle division by zero case

        cosine_similarity = np.dot(enc1, enc2) / (np.linalg.norm(enc1) * np.linalg.norm(enc2))
        return cosine_similarity

    def reload_encodings_and_users(self):
        """Refresh cached encodings and usernames from the database."""
        self.db_encodings_cache = {enc.user_id: np.array(enc.encoding) for enc in self.db.query(Encoding).all()}
        self.db_users_cache = {user.id: user.username for user in self.db.query(User).all()}
        print("[INFO] User and encoding cache reloaded.")



