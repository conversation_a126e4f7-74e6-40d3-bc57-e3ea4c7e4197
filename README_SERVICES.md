# Vigilant Eye - Two Service System

## 🏗️ **Architecture**

This system consists of **2 completely separate services**:

### **🔐 Authentication Service** (Port 8001)
- **Purpose**: User authentication, login, dashboard, role management
- **Files**: `Authentication/` directory
- **Dependencies**: FastAPI, SQLAlchemy, JWT, Jinja2
- **Database**: MySQL (authentication database)
- **Roles**: Admin and Viewer support

### **⚙️ Backend Service** (Port 8000)
- **Purpose**: AI detection modules (Crowd, PPE, Quality, Face)
- **Files**: `Backend/` directory
- **Dependencies**: FastAPI, OpenCV, YOLO, DeepFace
- **Database**: None (stateless AI processing)

---

## 🚀 **How to Run**

### **Option 1: Both Services Together (Recommended)**

#### **Windows Batch File:**
```bash
start_two_services.bat
```

#### **Python Script:**
```bash
python start_two_services.py
```

### **Option 2: Run Services Separately**

#### **Authentication Service Only:**
```bash
cd Authentication
python start.py
```
*Runs on: http://localhost:8001*

#### **Backend Service Only:**
```bash
cd Backend
venv\scripts\activate
uvicorn app.main:app --reload
```
*Runs on: http://127.0.0.1:8000*

---

## 🌐 **Service URLs**

| Service | URL | Purpose |
|---------|-----|---------|
| **🔐 Authentication** | `http://localhost:8001` | **START HERE** - Login & Dashboard |
| **⚙️ Backend** | `http://127.0.0.1:8000` | AI Detection Modules |

---

## 📋 **Complete User Flow**

1. **🌐 Access**: Go to `http://localhost:8001`
2. **🔐 Login**: Choose your role:
   - **👑 Admin**: `admin` / `admin123` (Full access)
   - **👁️ Viewer**: `viewer` / `viewer123` (View access)
3. **🏠 Dashboard**: Beautiful dashboard with service cards and role display
4. **🎯 Navigate**: Click service cards → Opens Backend AI modules in new tabs
5. **🏠 Home**: Click "Home" in any service → Returns to Authentication dashboard
6. **🚪 Logout**: Click logout → Returns to login page

---

## 🔧 **Service Details**

### **Authentication Service Features:**
- ✅ User login/logout with role-based access
- ✅ JWT token management with role information
- ✅ Beautiful dashboard interface with role display
- ✅ User registration with role selection
- ✅ Session management
- ✅ Navigation to Backend services
- ✅ Two user roles: Admin and Viewer
- ✅ Role-based UI elements

### **Backend Service Features:**
- ✅ Crowd Detection with ROI
- ✅ PPE/Helmet Detection
- ✅ Quality Control
- ✅ Face Recognition
- ✅ Camera management
- ✅ Real-time streaming
- ✅ Alert management

---

## 🔄 **Service Communication**

- **Authentication → Backend**: Dashboard links open Backend services in new tabs
- **Backend → Authentication**: "Home" buttons redirect to Authentication dashboard
- **Independent**: Each service can run and function independently
- **Stateless**: No direct API communication between services

---

## 🛠️ **Development**

### **Authentication Development:**
```bash
cd Authentication
python start.py
# Edit files in Authentication/ directory
# Service auto-reloads on file changes
```

### **Backend Development:**
```bash
cd Backend
venv\scripts\activate
uvicorn app.main:app --reload
# Edit files in Backend/app/ directory
# Service auto-reloads on file changes
```

---

## 📦 **Dependencies**

### **Authentication Dependencies:**
- fastapi==0.104.1
- uvicorn==0.24.0
- sqlalchemy==2.0.23
- python-jose[cryptography]==3.3.0
- passlib[bcrypt]==1.7.4
- python-multipart==0.0.6
- jinja2==3.1.2

### **Backend Dependencies:**
- opencv-python==*********
- ultralytics==8.3.133
- deepface==0.0.93
- tf-keras==2.19.0
- torch==2.2.2+cpu
- torchvision==0.17.2+cpu

---

## ✅ **System Status**

✅ **Two Separate Services**: Authentication and Backend completely independent
✅ **No Cross-Contamination**: Each service has only its required dependencies
✅ **Individual Operation**: Each service can run independently
✅ **Combined Operation**: Both services work together seamlessly
✅ **Clean Architecture**: Clear separation of concerns
✅ **Easy Development**: Independent development and testing

---

## 🎯 **Quick Start Commands**

### **Start Both Services:**
```bash
python start_two_services.py
```

### **Start Authentication Only:**
```bash
cd Authentication && python start.py
```

### **Start Backend Only:**
```bash
cd Backend && venv\scripts\activate && uvicorn app.main:app --reload
```

### **Access System:**
```
http://localhost:8001
```

**Login Options:**
- **👑 Admin**: admin / admin123 (Full access)
- **👁️ Viewer**: viewer / viewer123 (View access)
