@echo off
echo ========================================
echo    VIGILANT EYE - INTEGRATED SYSTEM
echo ========================================
echo.
echo Starting integrated system...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python and try again.
    pause
    exit /b 1
)

REM Run the integrated startup script
python start_integrated_system.py

echo.
echo System shutdown complete.
pause
