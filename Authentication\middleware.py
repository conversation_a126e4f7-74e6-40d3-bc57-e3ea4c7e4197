from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
import requests
import logging
from typing import Optional

logger = logging.getLogger(__name__)

class AuthMiddleware:
    """Middleware for handling authentication across services"""
    
    def __init__(self, auth_service_url: str = "http://localhost:8001"):
        self.auth_service_url = auth_service_url
    
    async def validate_token(self, token: str) -> Optional[dict]:
        """Validate token with authentication service"""
        try:
            response = requests.post(
                f"{self.auth_service_url}/validate-token",
                json={"token": token},
                timeout=5
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("valid"):
                    return result.get("user")
            
            return None
            
        except requests.RequestException as e:
            logger.error(f"Error validating token: {e}")
            return None
    
    async def get_user_from_request(self, request: Request) -> Optional[dict]:
        """Extract and validate user from request"""
        # Try to get token from Authorization header
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token = auth_header[7:]
            return await self.validate_token(token)
        
        # Try to get token from cookies
        token_cookie = request.cookies.get("access_token")
        if token_cookie and token_cookie.startswith("Bearer "):
            token = token_cookie[7:]
            return await self.validate_token(token)
        
        return None
    
    async def require_auth(self, request: Request) -> dict:
        """Require authentication and return user or raise exception"""
        user = await self.get_user_from_request(request)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required",
                headers={"WWW-Authenticate": "Bearer"},
            )
        return user

# Global auth middleware instance
auth_middleware = AuthMiddleware()
