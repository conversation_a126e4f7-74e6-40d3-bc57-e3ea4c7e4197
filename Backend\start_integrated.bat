@echo off
echo ========================================
echo    VIGILANT EYE - INTEGRATED SYSTEM
echo ========================================
echo.
echo Starting integrated system (Single Service)...
echo.

REM Activate virtual environment
call venv\Scripts\activate.bat

REM Run the integrated application
echo 🚀 Starting Vigilant Eye Integrated System...
echo 🔐 Authentication + ⚙️ Backend Services  
echo 🌐 Access: http://127.0.0.1:8000
echo.
echo 📋 User Flow:
echo    1. Go to http://127.0.0.1:8000
echo    2. Login (admin/admin123)  
echo    3. Access Dashboard
echo    4. Navigate to AI Services
echo.
echo Press Ctrl+C to stop the service
echo ========================================

uvicorn integrated_main:app --reload

echo.
echo System shutdown complete.
pause
