from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status, Form, Request
from fastapi.security import OAuth2PasswordRequestForm
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from datetime import <PERSON><PERSON><PERSON>
from sqlalchemy.orm import Session

# Import authentication modules
from auth import (
    Token, User, UserCreate, authenticate_user, create_access_token,
    get_current_active_user, ACCESS_TOKEN_EXPIRE_MINUTES,
    get_user, create_user, verify_token
)
from database import get_db, create_tables

# Initialize FastAPI app
app = FastAPI(title="Vigilant Eye Authentication Service", version="1.0.0")

# Create database tables if they don't exist
create_tables()

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this properly in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup templates and static files
templates = Jinja2Templates(directory="templates")
app.mount("/static", StaticFiles(directory="static"), name="static")

# Backend service URL
BACKEND_SERVICE_URL = "http://localhost:8000"

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "authentication"}

# Login page
@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

# Dashboard page - main page after login
@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request, current_user: User = Depends(get_current_active_user)):
    return templates.TemplateResponse("dashboard.html", {
        "request": request,
        "user": current_user,
        "backend_url": BACKEND_SERVICE_URL
    })

# Root redirect to dashboard
@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    # Check if user is logged in
    token = request.cookies.get("access_token")
    if token:
        try:
            db = next(get_db())
            user = verify_token(token, db)
            if user:
                return RedirectResponse(url="/dashboard", status_code=status.HTTP_302_FOUND)
        except:
            pass

    # Not logged in, redirect to login
    return RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)

# Login form submission
@app.post("/login", response_class=HTMLResponse)
async def login(
    request: Request,
    username: str = Form(...),
    password: str = Form(...),
    remember: bool = Form(False),
    db: Session = Depends(get_db)
):
    user = authenticate_user(db, username, password)

    if not user:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Invalid username or password"}
        )

    # Create access token
    access_token_expires = timedelta(
        days=30 if remember else 1  # 30 days if remember is checked, 1 day otherwise
    )
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )

    # Create redirect response to dashboard
    response = RedirectResponse(url="/dashboard", status_code=status.HTTP_302_FOUND)

    # Set cookie with token
    response.set_cookie(
        key="access_token",
        value=f"Bearer {access_token}",
        httponly=True,
        max_age=int(access_token_expires.total_seconds()),
        expires=int(access_token_expires.total_seconds()),
        secure=False,  # Set to True in production with HTTPS
        samesite="lax"
    )

    return response

# API token endpoint for programmatic access
@app.post("/token", response_model=Token)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )

    return {"access_token": access_token, "token_type": "bearer"}

# Token validation endpoint for other services
@app.post("/validate-token")
async def validate_token(
    token: str,
    db: Session = Depends(get_db)
):
    """Validate token and return user information"""
    try:
        user = verify_token(token, db)
        if user:
            return {
                "valid": True,
                "user": {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "full_name": user.full_name,
                    "disabled": user.disabled
                }
            }
        else:
            return {"valid": False, "error": "Invalid token"}
    except Exception as e:
        return {"valid": False, "error": str(e)}

# User registration endpoint
@app.post("/register", response_class=HTMLResponse)
async def register(
    request: Request,
    username: str = Form(...),
    email: str = Form(...),
    full_name: str = Form(...),
    password: str = Form(...),
    db: Session = Depends(get_db)
):
    existing_user = get_user(db, username)
    if existing_user:
        return templates.TemplateResponse(
            "register.html",
            {"request": request, "error": "Username already registered"}
        )

    user_data = UserCreate(
        username=username,
        email=email,
        full_name=full_name,
        password=password
    )

    create_user(db, user_data)
    return RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)

# Get current user endpoint
@app.get("/me", response_model=User)
async def read_users_me(current_user: User = Depends(get_current_active_user)):
    return current_user

# Logout endpoint
@app.get("/logout")
@app.post("/logout")
async def logout():
    response = RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)
    response.delete_cookie("access_token")
    return response

# User management endpoints
@app.get("/users/{username}", response_model=User)
async def get_user_by_username(
    username: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    user = get_user(db, username)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user

@app.post("/users", response_model=User)
async def create_new_user(
    user: UserCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    existing_user = get_user(db, user.username)
    if existing_user:
        raise HTTPException(status_code=400, detail="Username already registered")

    return create_user(db, user)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
