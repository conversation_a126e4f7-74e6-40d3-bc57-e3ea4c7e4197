#!/usr/bin/env python3
"""
Database setup script for Vigilant Eye Authentication Service
"""

import mysql.connector
from mysql.connector import Error
import sys

def create_database():
    """Create the vigilanteye database if it doesn't exist"""
    try:
        # Database connection parameters
        config = {
            'host': 'localhost',
            'user': 'root',
            'password': '1234'  # Change this to your MySQL password
        }
        
        print("Connecting to MySQL server...")
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        # Check if database exists
        cursor.execute("SHOW DATABASES LIKE 'vigilanteye'")
        result = cursor.fetchone()
        
        if result:
            print("✅ Database 'vigilanteye' already exists")
        else:
            print("Creating database 'vigilanteye'...")
            cursor.execute("CREATE DATABASE vigilanteye")
            print("✅ Database 'vigilanteye' created successfully")
        
        # Check if auth database exists
        cursor.execute("SHOW DATABASES LIKE 'vigilanteye_auth'")
        result = cursor.fetchone()
        
        if result:
            print("✅ Database 'vigilanteye_auth' already exists")
        else:
            print("Creating database 'vigilanteye_auth'...")
            cursor.execute("CREATE DATABASE vigilanteye_auth")
            print("✅ Database 'vigilanteye_auth' created successfully")
        
        print("\n🎉 Database setup completed successfully!")
        print("\nYou can now start the authentication service:")
        print("cd authentication")
        print("python start.py")
        
    except Error as e:
        print(f"❌ Error connecting to MySQL: {e}")
        print("\nPlease check:")
        print("1. MySQL server is running")
        print("2. Username and password are correct")
        print("3. You have permission to create databases")
        sys.exit(1)
        
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()
            print("\nMySQL connection closed.")

def main():
    print("=" * 50)
    print("Vigilant Eye Database Setup")
    print("=" * 50)
    
    # Check if mysql-connector-python is installed
    try:
        import mysql.connector
    except ImportError:
        print("❌ mysql-connector-python is not installed")
        print("\nPlease install it using:")
        print("pip install mysql-connector-python")
        sys.exit(1)
    
    create_database()

if __name__ == "__main__":
    main()
