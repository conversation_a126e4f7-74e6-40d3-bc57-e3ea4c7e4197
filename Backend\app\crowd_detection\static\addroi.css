/* General Reset */
body, html {
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
    box-sizing: border-box;
    height: 100%;
}
button:focus, input:focus, img:focus , canvas:focus , #canvas:focus {
    outline: none !important;
  }
  

/* Main Container: Divided Vertically */


.main-container {
    display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  grid-template-rows: auto; /* Adjust rows dynamically */
  gap: 10px;
  width: 100vw; /* Full viewport width */
  height: 100vh; /* Full viewport height */
  max-width: 100%;
  max-height: 100%;
  background: #fff;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  box-sizing: border-box; /* Ensure padding and border are included in the width/height */
  overflow: hidden; /* Prevent content overflow */
}


.logo {
    font-size: 1.5rem;
    margin-top: 30px;
    text-align: center;
    width: 100%;
  }
.logo img{
    width: 100%;
    max-width: 150px;
    height: auto;
}


/* Left Container: Controls */
.left-container {
    /* flex:0.3; Take up a smaller portion */
    background-color: #202020;
    padding: 20px;
    color: #fff;
    border-right: 2px solid #dee2e6;
    display: flex;
    flex-direction: column;
    /* align-items: center; */
    justify-content: space-between;
    width: 255px;
}

.left-container h2 {
    margin-bottom: 20px;
    color: #c7cbca50;
}


.left-container button {
    width: 100%;
    margin: 10px 0;
    padding: 10px;
    background-color: #555;
    color: white;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-size: 14px;
    text-align: start;
}

.left-container button:hover {
    background-color: #0033b3be;
}

/* Right Container: Video and Canvas */
.right-container {
    flex: 3; /* Take up more space */
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
}

.right-container h2 {
    margin-bottom: 20px;
    color: #343a40;
}

/* Video Feed and Canvas Overlaid */
#container {
    position: relative;
    width: 640px;
    height: 480px;
}

#video {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    pointer-events: none;
}

#video, img {
    user-select: none; /* Prevent text/image selection */
    -webkit-user-select: none; /* For Safari */
    -moz-user-select: none; /* For Firefox */
    -ms-user-select: none; /* For Internet Explorer/Edge */
}
#canvas {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2; /* Ensure it's on top of the video */
    cursor: crosshair; /* Show crosshair cursor for drawing */;
}

.instruction-div {
    background-color: #f4f4f4; 
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); 
    text-align: center;
    max-width: 600px;
    margin: 20px auto; 
}

.instruction-div h3 {
    font-family: 'Arial', sans-serif; 
    font-size: 18px;
    color: #333; 
    margin: 0; 
}

/* Initial styles for the elements */
.instruction-div,
#videoElement,
#canvas {
    transition: opacity 0.10s ease-in-out;
}

.fade-in {
    opacity: 1;
    pointer-events: all;
}

.fade-out {
    opacity: 0;
    pointer-events: none;
}

