from sqlalchemy import create_engine, Column, Integer, String, Boolean, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import os

# Database configuration
DATABASE_URL = "mysql+pymysql://root:@localhost/vigilant_eye"

# Create engine
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for models
Base = declarative_base()

# User model
class UserDB(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    full_name = Column(String(100))
    hashed_password = Column(String(255))
    role = Column(String(20), default="viewer")  # admin or viewer
    disabled = Column(<PERSON>olean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# Dependency to get database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_database():
    """Initialize database tables and create default users"""
    try:
        print("🚀 Starting Vigilant Eye Backend...")
        
        # Create tables
        Base.metadata.create_all(bind=engine)
        print("✅ Database tables created successfully!")
        
        # Create default users
        create_default_users()
        
        print("🎉 Database initialized successfully!")
        print("✅ Backend startup complete!")
        return True
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False

def create_default_users():
    """Create default admin and viewer users"""
    try:
        from app.auth import create_user, get_user, UserCreate
        
        db = SessionLocal()
        try:
            # Check if admin user exists
            admin_user = get_user(db, "admin")
            if not admin_user:
                admin_data = UserCreate(
                    username="admin",
                    email="<EMAIL>",
                    full_name="System Administrator",
                    password="admin123",
                    role="admin"
                )
                create_user(db, admin_data)
                print("✅ Default admin user created (admin/admin123)")
            
            # Check if viewer user exists
            viewer_user = get_user(db, "viewer")
            if not viewer_user:
                viewer_data = UserCreate(
                    username="viewer",
                    email="<EMAIL>",
                    full_name="System Viewer",
                    password="viewer123",
                    role="viewer"
                )
                create_user(db, viewer_data)
                print("✅ Default viewer user created (viewer/viewer123)")
                
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Error creating default users: {e}")
