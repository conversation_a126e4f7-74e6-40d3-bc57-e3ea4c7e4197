from sqlalchemy import create_engine, Column, Inte<PERSON>, <PERSON>, Bo<PERSON>an, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import os

# Database configuration
DATABASE_URL = "mysql+pymysql://root:@localhost/vigilant_eye"

# Create engine
engine = create_engine(
    DATABASE_URL,
    echo=False,  # Set to True for SQL debugging
    pool_pre_ping=True,
    pool_recycle=300
)

# Create session
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create base class
Base = declarative_base()

# User model for authentication
class UserDB(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    full_name = Column(String(100))
    hashed_password = Column(String(255))
    role = Column(String(20), default="viewer")  # admin or viewer
    disabled = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# Dependency to get database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Create tables
def create_tables():
    """Create all tables in the database"""
    try:
        Base.metadata.create_all(bind=engine)
        print("✅ Database tables created successfully!")
        return True
    except Exception as e:
        print(f"❌ Error creating database tables: {e}")
        return False

# Initialize database
def init_database():
    """Initialize database and create default users"""
    try:
        # Create tables
        create_tables()

        # Create default users
        create_default_users()

        print("🎉 Database initialized successfully!")
        return True
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False

def create_default_users():
    """Create default admin and viewer users"""
    try:
        from app.auth import create_user, get_user, UserCreate

        db = SessionLocal()
        try:
            # Check if admin user exists
            admin_user = get_user(db, "admin")
            if not admin_user:
                print("Creating default admin user...")
                admin_data = UserCreate(
                    username="admin",
                    email="<EMAIL>",
                    full_name="System Administrator",
                    password="admin123",  # Change this in production!
                    role="admin"
                )
                create_user(db, admin_data)
                print("✅ Default admin user created:")
                print("   Username: admin")
                print("   Password: admin123")
                print("   Role: admin")
                print("   Email: <EMAIL>")
            else:
                print("✅ Admin user already exists.")

            # Check if viewer user exists
            viewer_user = get_user(db, "viewer")
            if not viewer_user:
                print("Creating default viewer user...")
                viewer_data = UserCreate(
                    username="viewer",
                    email="<EMAIL>",
                    full_name="System Viewer",
                    password="viewer123",  # Change this in production!
                    role="viewer"
                )
                create_user(db, viewer_data)
                print("✅ Default viewer user created:")
                print("   Username: viewer")
                print("   Password: viewer123")
                print("   Role: viewer")
                print("   Email: <EMAIL>")
            else:
                print("✅ Viewer user already exists.")
        finally:
            db.close()
    except Exception as e:
        print(f"❌ Error creating default users: {e}")

# Test database connection
def test_connection():
    """Test database connection"""
    try:
        db = SessionLocal()
        try:
            # Try to execute a simple query
            db.execute("SELECT 1")
            print("✅ Database connection successful!")
            return True
        finally:
            db.close()
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Testing database connection...")
    if test_connection():
        print("🔧 Initializing database...")
        init_database()
    else:
        print("❌ Please check your database configuration.")
