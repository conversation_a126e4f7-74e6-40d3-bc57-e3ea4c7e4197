#!/usr/bin/env python3
"""
Vigilant Eye - Two Service Startup
Runs Authentication and Backend as separate services
"""

import subprocess
import sys
import time
import os
import threading
from pathlib import Path

def run_authentication_service():
    """Run the Authentication service"""
    print("🔐 Starting Authentication Service...")
    auth_dir = Path("Authentication")
    
    if not auth_dir.exists():
        print("❌ Authentication directory not found!")
        return None
    
    try:
        # Change to Authentication directory and run
        process = subprocess.Popen(
            [sys.executable, "start.py"],
            cwd=auth_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        print("✅ Authentication Service started on http://localhost:8001")
        return process
    except Exception as e:
        print(f"❌ Failed to start Authentication Service: {e}")
        return None

def run_backend_service():
    """Run the Backend service"""
    print("⚙️ Starting Backend Service...")
    backend_dir = Path("Backend")
    
    if not backend_dir.exists():
        print("❌ Backend directory not found!")
        return None
    
    try:
        # Activate venv and run uvicorn
        if os.name == 'nt':  # Windows
            python_path = backend_dir / "venv" / "Scripts" / "python.exe"
            uvicorn_cmd = [str(python_path), "-m", "uvicorn", "app.main:app", "--reload", "--port", "8000"]
        else:  # Linux/Mac
            python_path = backend_dir / "venv" / "bin" / "python"
            uvicorn_cmd = [str(python_path), "-m", "uvicorn", "app.main:app", "--reload", "--port", "8000"]
        
        if not python_path.exists():
            print("❌ Backend virtual environment not found!")
            return None
        
        process = subprocess.Popen(
            uvicorn_cmd,
            cwd=backend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        print("✅ Backend Service started on http://127.0.0.1:8000")
        return process
    except Exception as e:
        print(f"❌ Failed to start Backend Service: {e}")
        return None

def monitor_process(process, service_name):
    """Monitor a process and print its output"""
    try:
        for line in iter(process.stdout.readline, ''):
            if line:
                print(f"[{service_name}] {line.strip()}")
    except Exception as e:
        print(f"❌ Error monitoring {service_name}: {e}")

def main():
    """Main function to start both services"""
    print("🚀 Starting Vigilant Eye Two-Service System...")
    print("=" * 60)
    
    # Start Authentication Service
    auth_process = run_authentication_service()
    if not auth_process:
        print("❌ Failed to start Authentication Service. Exiting.")
        return
    
    # Wait a moment for auth service to start
    time.sleep(3)
    
    # Start Backend Service
    backend_process = run_backend_service()
    if not backend_process:
        print("❌ Failed to start Backend Service. Stopping Authentication.")
        auth_process.terminate()
        return
    
    # Wait a moment for backend service to start
    time.sleep(3)
    
    print("\n" + "=" * 60)
    print("🎉 VIGILANT EYE SYSTEM READY!")
    print("=" * 60)
    print("🔐 Authentication: http://localhost:8001")
    print("⚙️ Backend Services: http://127.0.0.1:8000")
    print("🏠 Main Access: http://localhost:8001 (Login first)")
    print("=" * 60)
    print("📋 User Flow:")
    print("   1. Go to http://localhost:8001")
    print("   2. Login (admin/admin123)")
    print("   3. Access Dashboard")
    print("   4. Navigate to AI Services")
    print("=" * 60)
    print("Press Ctrl+C to stop all services")
    print("=" * 60)
    
    # Start monitoring threads
    auth_thread = threading.Thread(
        target=monitor_process, 
        args=(auth_process, "AUTH"),
        daemon=True
    )
    backend_thread = threading.Thread(
        target=monitor_process, 
        args=(backend_process, "BACKEND"),
        daemon=True
    )
    
    auth_thread.start()
    backend_thread.start()
    
    try:
        # Wait for processes to complete
        while auth_process.poll() is None and backend_process.poll() is None:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 Shutting down services...")
        
        # Terminate processes gracefully
        if auth_process.poll() is None:
            print("🔐 Stopping Authentication Service...")
            auth_process.terminate()
            try:
                auth_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                auth_process.kill()
        
        if backend_process.poll() is None:
            print("⚙️ Stopping Backend Service...")
            backend_process.terminate()
            try:
                backend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                backend_process.kill()
        
        print("✅ All services stopped successfully!")

if __name__ == "__main__":
    main()
